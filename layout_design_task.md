# 统一Layout设计任务

## 分析阶段 (Analysis)

### 现有架构分析
1. **当前Layout结构**：
   - 主Layout组件：`src/layout/index.vue`
   - 包含组件：SideBar、Navbar、AppMain、Settings、TagsView
   - 使用传统的后台管理系统布局（侧边栏+顶部导航+内容区域）

2. **路由配置**：
   - 所有路由都使用同一个Layout组件
   - 通过`component: Layout`指定布局
   - 支持动态路由和权限控制

3. **现有问题**：
   - 只有一种布局模式
   - 无法区分不同类型的页面布局需求
   - 首页需要低空监控专用布局，其他页面需要传统后台布局

### 需求分析
1. **总Layout容器**：需要一个智能的布局容器，能够根据路由自动切换子布局
2. **低空监控布局**：专门用于首页的低空监控界面
3. **传统后台布局**：保持现有的后台管理系统布局
4. **无缝集成**：后台布局能够无缝接入新的layout架构中

## 提议解决方案 (Proposed Solution)

### 核心设计理念
采用"智能布局切换系统"，通过路由元信息(meta)来决定使用哪种布局模式。

### 架构设计

#### 1. 文件结构
```
src/layout/
├── index.vue                 # 原有的传统后台布局（重命名为AdminLayout）
├── UnifiedLayout.vue         # 新的统一布局容器
├── AirspaceLayout.vue        # 低空监控专用布局
├── components/               # 共享组件
│   ├── Sidebar/
│   ├── Navbar/
│   ├── AppMain/
│   ├── Settings/
│   └── TagsView/
└── airspace/                 # 低空监控专用组件
    ├── AirspaceHeader.vue
    ├── ControlPanel.vue
    ├── MapContainer.vue
    └── DataPanel.vue
```

#### 2. 布局类型定义
```typescript
// 布局类型枚举
enum LayoutType {
  ADMIN = 'admin',      // 传统后台布局
  AIRSPACE = 'airspace' // 低空监控布局
}
```

#### 3. 路由配置策略
通过路由meta字段指定布局类型：

```typescript
// 低空监控首页
{
  path: '/index',
  component: () => import('@/views/index.vue'),
  meta: { 
    title: '首页', 
    icon: 'dashboard', 
    layoutType: 'airspace' // 指定使用低空监控布局
  }
}

// 传统后台页面
{
  path: '/system/user',
  component: () => import('@/views/system/user/index.vue'),
  meta: { 
    title: '用户管理', 
    icon: 'user',
    layoutType: 'admin' // 指定使用传统后台布局
  }
}

// 默认情况（不指定layoutType时默认使用admin布局）
```

#### 4. 统一布局容器逻辑
`UnifiedLayout.vue`作为总容器：
- 监听路由变化
- 根据当前路由的meta.layoutType决定渲染哪个子布局
- 提供布局切换的过渡动画
- 管理全局状态（如用户信息、权限等）

#### 5. 状态管理优化
创建专门的布局状态管理模块：
```typescript
// store/modules/layout.ts
interface LayoutState {
  currentLayoutType: LayoutType;
  airspaceConfig: AirspaceLayoutConfig;
  adminConfig: AdminLayoutConfig;
}
```

#### 6. 滚动策略优化（Router-View 独占滚动）

> 目标：保证任何布局场景下，仅 `router-view` 对应的内容区域出现滚动条，头部（Navbar / TagsView / CommonHeader 等）始终固定。

1. **总体思路**  
   - 在 `UnifiedLayout.vue` 顶层容器中使用 `flex` 布局，将头部放置在 `flex` 起始位置，主体区域(`.unified-layout-body`) 充满剩余空间。  
   - 通过 `overflow: hidden` 阻止顶层及头部出现滚动条，仅在内部 `.app-main`（Admin 布局）或 `.airspace-main`（Airspace 布局）中开启 `overflow: auto`。  
   - 使用 `min-height: 0` 修复 Flex 子项高度计算溢出导致无法滚动的问题。  

2. **核心样式片段** （已在 `src/layout/UnifiedLayout.vue` 中实现）

```scss
.unified-layout-wrapper {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden; // 阻止外层滚动
}

.unified-layout-body {
  flex: 1;
  min-height: 0; // 关键：允许子项计算剩余高度
  overflow: hidden;
}

// 让业务区域滚动，而非整个页面
:deep(.app-main),
:deep(.airspace-main) {
  overflow: auto !important;
}
```

3. **模板结构关键变更**

```vue
<template>
  <div class="unified-layout-wrapper">
    <CommonHeader v-if="showCommonHeader" />
    <div class="unified-layout-body">
      <component :is="layoutComponent" class="layout-content" />
    </div>
  </div>
</template>
```

4. **影响范围**  
   - **AdminLayout**：`TagsView`、`Navbar` 固定，`.app-main` 内部滚动。  
   - **AirspaceLayout**：公共头部固定，`.airspace-main` 内部滚动。  
   - 其余布局若新增，只需保证内部主体块使用 `overflow: auto` 即可无缝接入。

5. **待验证清单**  
   - [ ] 横向／纵向滚动条仅出现于业务内容区域  
   - [ ] 切换路由时滚动位置复位／保持符合业务预期  
   - [ ] 各布局在移动端自适应场景下表现正常

### 技术实现要点

1. **组件复用**：
   - Navbar、Settings等通用组件可在两种布局中复用
   - 通过props传递配置来适应不同布局需求

2. **样式隔离**：
   - 每种布局使用独立的CSS作用域
   - 共享基础样式变量和主题配置

3. **性能优化**：
   - 布局组件按需加载
   - 缓存布局状态，避免重复渲染

4. **响应式适配**：
   - 两种布局都支持移动端适配
   - 统一的断点管理

### 集成方案

1. **路由配置更新**：
   - 将所有路由的component从Layout改为UnifiedLayout
   - 为需要特殊布局的路由添加layoutType meta

2. **渐进式迁移**：
   - 保持现有AdminLayout功能不变
   - 新增AirspaceLayout和UnifiedLayout
   - 逐步迁移路由配置

3. **向后兼容**：
   - 不指定layoutType的路由默认使用admin布局
   - 保持现有API和组件接口不变

## 任务进度 (Task Progress)

- [x] UnifiedLayout.vue 已完成开发，实现根据路由 meta.layoutType 智能切换子布局
- [x] CommonHeader.vue 公共头部组件完成，实现示例 UI、导航、时间及环境信息
- [x] CommonHeader.vue 精细化样式升级完成：
  - ✅ 头部高度调整为90px
  - ✅ 基于Element-Plus el-dropdown实现二级导航菜单
  - ✅ 渐变背景动画效果
  - ✅ 导航项hover动画和选中状态
  - ✅ 右侧信息区域分隔线和hover效果
  - ✅ 自定义下拉菜单样式适配设计稿
  - ✅ 支持数据驱动的多级菜单结构
- [ ] AirspaceLayout.vue 具体界面开发中（已创建基础占位版本）
- [ ] 路由配置迁移至 UnifiedLayout，待实施

## 最终审查 (Final Review)

待完成实施后进行审查...