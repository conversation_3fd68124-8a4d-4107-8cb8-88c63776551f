export interface TestPermissionDemoVO {
  /**
   * 主键ID
   */
  id: string | number;

  /**
   * 演示名称
   */
  demoName: string;

  /**
   * 演示编码
   */
  demoCode: string;

  /**
   * 演示类型（type1/type2/type3）
   */
  demoType: string;

  /**
   * 演示状态（active/inactive）
   */
  demoStatus: string;

  /**
   * 演示值
   */
  demoValue: string;

  /**
   * 排序号
   */
  sortOrder: number;

  /**
   * 备注
   */
  remark: string;

}

export interface TestPermissionDemoForm extends BaseEntity {
  /**
   * 主键ID
   */
  id?: string | number;

  /**
   * 演示名称
   */
  demoName?: string;

  /**
   * 演示编码
   */
  demoCode?: string;

  /**
   * 演示类型（type1/type2/type3）
   */
  demoType?: string;

  /**
   * 演示状态（active/inactive）
   */
  demoStatus?: string;

  /**
   * 演示值
   */
  demoValue?: string;

  /**
   * 排序号
   */
  sortOrder?: number;

  /**
   * 备注
   */
  remark?: string;

}

export interface TestPermissionDemoQuery extends PageQuery {

  /**
   * 演示名称
   */
  demoName?: string;

  /**
   * 演示编码
   */
  demoCode?: string;

  /**
   * 演示类型（type1/type2/type3）
   */
  demoType?: string;

  /**
   * 演示状态（active/inactive）
   */
  demoStatus?: string;

  /**
   * 演示值
   */
  demoValue?: string;

  /**
   * 排序号
   */
  sortOrder?: number;

    /**
     * 日期范围参数
     */
    params?: any;
}



