import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { TestPermissionDemoVO, TestPermissionDemoForm, TestPermissionDemoQuery } from '@/api/demo/testPermissionDemo/types';

/**
 * 查询数据权限演示列表
 * @param query
 * @returns {*}
 */

export const listTestPermissionDemo = (query?: TestPermissionDemoQuery): AxiosPromise<TestPermissionDemoVO[]> => {
  return request({
    url: '/demo/testPermissionDemo/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询数据权限演示详细
 * @param id
 */
export const getTestPermissionDemo = (id: string | number): AxiosPromise<TestPermissionDemoVO> => {
  return request({
    url: '/demo/testPermissionDemo/' + id,
    method: 'get'
  });
};

/**
 * 新增数据权限演示
 * @param data
 */
export const addTestPermissionDemo = (data: TestPermissionDemoForm) => {
  return request({
    url: '/demo/testPermissionDemo',
    method: 'post',
    data: data
  });
};

/**
 * 修改数据权限演示
 * @param data
 */
export const updateTestPermissionDemo = (data: TestPermissionDemoForm) => {
  return request({
    url: '/demo/testPermissionDemo',
    method: 'put',
    data: data
  });
};

/**
 * 删除数据权限演示
 * @param id
 */
export const delTestPermissionDemo = (id: string | number | Array<string | number>) => {
  return request({
    url: '/demo/testPermissionDemo/' + id,
    method: 'delete'
  });
};
