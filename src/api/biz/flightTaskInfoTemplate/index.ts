import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { FlightTaskInfoTemplateVO, FlightTaskInfoTemplateForm, FlightTaskInfoTemplateQuery } from '@/api/biz/flightTaskInfoTemplate/types';

/**
 * 查询飞行任务计划模板（用于飞行任务按需执行）列表
 * @param query
 * @returns {*}
 */

export const listFlightTaskInfoTemplate = (query?: FlightTaskInfoTemplateQuery): AxiosPromise<FlightTaskInfoTemplateVO[]> => {
  return request({
    url: '/biz/flightTaskInfoTemplate/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询飞行任务计划模板（用于飞行任务按需执行）详细
 * @param id
 */
export const getFlightTaskInfoTemplate = (id: string | number): AxiosPromise<FlightTaskInfoTemplateVO> => {
  return request({
    url: '/biz/flightTaskInfoTemplate/' + id,
    method: 'get'
  });
};

/**
 * 新增飞行任务计划模板（用于飞行任务按需执行）
 * @param data
 */
export const addFlightTaskInfoTemplate = (data: FlightTaskInfoTemplateForm) => {
  return request({
    url: '/biz/flightTaskInfoTemplate',
    method: 'post',
    data: data
  });
};

/**
 * 修改飞行任务计划模板（用于飞行任务按需执行）
 * @param data
 */
export const updateFlightTaskInfoTemplate = (data: FlightTaskInfoTemplateForm) => {
  return request({
    url: '/biz/flightTaskInfoTemplate',
    method: 'put',
    data: data
  });
};

/**
 * 删除飞行任务计划模板（用于飞行任务按需执行）
 * @param id
 */
export const delFlightTaskInfoTemplate = (id: string | number | Array<string | number>) => {
  return request({
    url: '/biz/flightTaskInfoTemplate/' + id,
    method: 'delete'
  });
};

/**
 * 批量从模板触发执行飞行任务
 * @param templateIds
 */
export const executeBatchFromTemplates = (templateIds: Array<string | number>) => {
  return request({
    url: '/biz/flightTaskInfoTemplate/executeBatch',
    method: 'post',
    data: templateIds
  });
};
