export interface FlightTaskInfoTemplateVO {
  /**
   * 任务ID，主键
   */
  id: string | number;

  /**
   * 任务名称
   */
  taskName: string;

  /**
   * 任务编号
   */
  taskNo: string;

  /**
   * 任务类型（0-路面巡检，1-风险隐患排查，2-高边坡巡查）
   */
  taskType: string;

  /**
   * 任务频率
   */
  taskFrequency: string;

  /**
   * 任务计划开始时间
   */
  plannedStartTime: string;

  /**
   * 任务计划结束时间
   */
  plannedEndTime: string;

  /**
   * 电池容量达到
   */
  batteryCapacityReached: number;

  /**
   * 储存容量达到
   */
  storageCapacityReached: number;

  /**
   * 备注
   */
  notes: string;

  /**
   * 任务状态
   */
  taskStatus: string;

  /**
   * 实际开始时间
   */
  actualStartTime: string;

  /**
   * 实际结束时间
   */
  actualEndTime: string;

  /**
   * 返航高度
   */
  returnAltitude: number;

  /**
   * 矢量操作
   */
  vectorOperation: string;

  /**
   * 任务对应设备的拥有租户 ID（房东）
   */
  ownerTenantId: string | number;

  /**
   * 任务实际执行租户 ID（房客，自用时 = owner_tenant_id）
   */
  targetTenantId: string | number;

  /**
   * 执行设备序列号（对应飞控平台device_sn）
   */
  deviceSn: string;

  /**
   * 执行航线编号（对应飞控平台file_id）
   */
  routeNo: string;

  /**
   * 执行任务的时间点，支持多时间段配置，JSON格式
   */
  taskPeriodsJson: string;

  /**
   * AI识别模型，多个模型用逗号分隔，如：vehicle_dense,psw,person_dense
   */
  aiRecognitionModels: string;

}

export interface FlightTaskInfoTemplateForm extends BaseEntity {
  /**
   * 任务ID，主键
   */
  id?: string | number;

  /**
   * 任务名称
   */
  taskName?: string;

  /**
   * 任务编号
   */
  taskNo?: string;

  /**
   * 任务类型（0-路面巡检，1-风险隐患排查，2-高边坡巡查）
   */
  taskType?: string;

  /**
   * 任务频率
   */
  taskFrequency?: string;

  /**
   * 任务计划开始时间
   */
  plannedStartTime?: string;

  /**
   * 任务计划结束时间
   */
  plannedEndTime?: string;

  /**
   * 电池容量达到
   */
  batteryCapacityReached?: number;

  /**
   * 储存容量达到
   */
  storageCapacityReached?: number;

  /**
   * 备注
   */
  notes?: string;

  /**
   * 任务状态
   */
  taskStatus?: string;

  /**
   * 返航高度
   */
  returnAltitude?: number;

  /**
   * 矢量操作
   */
  vectorOperation?: string;

  /**
   * 任务对应设备的拥有租户 ID（房东）
   */
  ownerTenantId?: string | number;

  /**
   * 任务实际执行租户 ID（房客，自用时 = owner_tenant_id）
   */
  targetTenantId?: string | number;

  /**
   * 执行设备序列号（对应飞控平台device_sn）
   */
  deviceSn?: string;

  /**
   * 执行航线编号（对应飞控平台file_id）
   */
  routeNo?: string;

  /**
   * 执行任务的时间点，支持多时间段配置，JSON格式
   */
  taskPeriodsJson?: string;

  /**
   * AI识别模型，多个模型用逗号分隔，如：vehicle_dense,psw,person_dense
   */
  aiRecognitionModels?: string;

}

export interface FlightTaskInfoTemplateQuery extends PageQuery {

  /**
   * 任务名称
   */
  taskName?: string;

  /**
   * 任务编号
   */
  taskNo?: string;

  /**
   * 任务类型（0-路面巡检，1-风险隐患排查，2-高边坡巡查）
   */
  taskType?: string;

  /**
   * 任务频率
   */
  taskFrequency?: string;

  /**
   * 任务计划开始时间
   */
  plannedStartTime?: string;

  /**
   * 任务计划结束时间
   */
  plannedEndTime?: string;

  /**
   * 电池容量达到
   */
  batteryCapacityReached?: number;

  /**
   * 储存容量达到
   */
  storageCapacityReached?: number;

  /**
   * 任务状态
   */
  taskStatus?: string;

  /**
   * 实际开始时间
   */
  actualStartTime?: string;

  /**
   * 实际结束时间
   */
  actualEndTime?: string;

  /**
   * 返航高度
   */
  returnAltitude?: number;

  /**
   * 矢量操作
   */
  vectorOperation?: string;

  /**
   * 任务对应设备的拥有租户 ID（房东）
   */
  ownerTenantId?: string | number;

  /**
   * 任务实际执行租户 ID（房客，自用时 = owner_tenant_id）
   */
  targetTenantId?: string | number;

  /**
   * 执行设备序列号（对应飞控平台device_sn）
   */
  deviceSn?: string;

  /**
   * 执行航线编号（对应飞控平台file_id）
   */
  routeNo?: string;

  /**
   * 执行任务的时间点，支持多时间段配置，JSON格式
   */
  taskPeriodsJson?: string;

  /**
   * AI识别模型，多个模型用逗号分隔，如：vehicle_dense,psw,person_dense
   */
  aiRecognitionModels?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



