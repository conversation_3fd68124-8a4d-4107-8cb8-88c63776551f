import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { RoadEmergencyEvenInfoVO, RoadEmergencyEvenInfoForm, RoadEmergencyEvenInfoQuery } from '@/api/biz/roadEmergencyEvenInfo/types';

/**
 * 查询应急事件列表
 * @param query
 * @returns {*}
 */

export const listRoadEmergencyEvenInfo = (query?: RoadEmergencyEvenInfoQuery): AxiosPromise<RoadEmergencyEvenInfoVO[]> => {
  return request({
    url: '/biz/roadEmergencyEvenInfo/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询应急事件详细
 * @param id
 */
export const getRoadEmergencyEvenInfo = (id: string | number): AxiosPromise<RoadEmergencyEvenInfoVO> => {
  return request({
    url: '/biz/roadEmergencyEvenInfo/' + id,
    method: 'get'
  });
};

/**
 * 新增应急事件
 * @param data
 */
export const addRoadEmergencyEvenInfo = (data: RoadEmergencyEvenInfoForm) => {
  return request({
    url: '/biz/roadEmergencyEvenInfo',
    method: 'post',
    data: data
  });
};

/**
 * 修改应急事件
 * @param data
 */
export const updateRoadEmergencyEvenInfo = (data: RoadEmergencyEvenInfoForm) => {
  return request({
    url: '/biz/roadEmergencyEvenInfo',
    method: 'put',
    data: data
  });
};

/**
 * 删除应急事件
 * @param id
 */
export const delRoadEmergencyEvenInfo = (id: string | number | Array<string | number>) => {
  return request({
    url: '/biz/roadEmergencyEvenInfo/' + id,
    method: 'delete'
  });
};

/**
 * 发送短信通知
 * @param id
 */
export const pushEvenSms = (id: string | number | Array<string | number>) => {
  return request({
    url: '/biz/roadEmergencyEvenInfo/sendLevel2SmsNotify/' + id,
    method: 'get'
  });
};

/**
 * 查询公路巡检应急事件统计信息
 * @param taskResultId
 * @param evenType
 * @returns {*}
 */
export const getEmergencyEvenStatistics = (taskResultId?: string | number, evenType?: string): AxiosPromise<BizRoadEmergencyEvenStatisticsVo[]> => {
  return request({
    url: '/biz/roadEmergencyEvenInfo/statistics',
    method: 'get',
    params: { taskResultId, evenType }
  });
};
