export interface RoadEmergencyEvenInfoVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 巡检任务id
   */
  taskResultId: string | number;

  /**
   * 巡检任务名称
   */
  taskResultName: string;

  /**
   * 事件时间
   */
  eventTime: string;

  /**
   * 事件类型
   */
  evenType: string;

  /**
   * 纬度
   */
  latitude: number;

  /**
   * 经度
   */
  longitude: number;

  /**
   * 项目名称
   */
  projectName: string;

  /**
   * 桩号
   */
  pileNo: string;

  /**
   * 图片URL
   */
  imageUrl: string;

  /**
   * 置信度
   */
  confidence: string | number;

  /**
   * 是否确认
   */
  isConfirmed: string;

  /**
   * 是否推送
   */
  isPush: string;

  /**
   * 对象存储id
   */
  ossId: string | number;

  /**
   * 对象存储idUrl
   */
  ossIdUrl: string;
}

export interface RoadEmergencyEvenInfoForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 巡检任务id
   */
  taskResultId?: string | number;

  /**
   * 巡检任务名称
   */
  taskResultName?: string;

  /**
   * 事件时间
   */
  eventTime?: string;

  /**
   * 事件类型
   */
  evenType?: string;

  /**
   * 纬度
   */
  latitude?: number;

  /**
   * 经度
   */
  longitude?: number;

  /**
   * 项目名称
   */
  projectName?: string;

  /**
   * 桩号
   */
  pileNo?: string;

  /**
   * 图片URL
   */
  imageUrl?: string;

  /**
   * 置信度
   */
  confidence?: string | number;

  /**
   * 是否确认
   */
  isConfirmed?: string;

  /**
   * 是否推送
   */
  isPush?: string;
}

export interface RoadEmergencyEvenInfoQuery extends PageQuery {
  /**
   * 巡检任务id
   */
  taskResultId?: string | number;

  /**
   * 巡检任务名称
   */
  taskResultName?: string;

  /**
   * 事件时间
   */
  eventTime?: string;

  /**
   * 事件类型
   */
  evenType?: string;

  /**
   * 纬度
   */
  latitude?: number;

  /**
   * 经度
   */
  longitude?: number;

  /**
   * 项目名称
   */
  projectName?: string;

  /**
   * 桩号
   */
  pileNo?: string;

  /**
   * 图片URL
   */
  imageUrl?: string;

  /**
   * 置信度
   */
  confidence?: string | number;

  /**
   * 是否确认
   */
  isConfirmed?: string;

  /**
   * 是否推送
   */
  isPush?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}

/**
 * 边坡灾害统计信息VO
 */
export interface BizRoadEmergencyEvenStatisticsVo {
  /**
   * 事件类型
   */
  evenType: string;

  /**
   * 事件数量
   */
  count: number;
}
