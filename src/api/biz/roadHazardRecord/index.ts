import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { RoadHazardRecordVO, RoadHazardRecordForm, RoadHazardRecordQuery } from '@/api/biz/roadHazardRecord/types';

/**
 * 查询隐患巡查记录列表
 * @param query
 * @returns {*}
 */

export const listRoadHazardRecord = (query?: RoadHazardRecordQuery): AxiosPromise<RoadHazardRecordVO[]> => {
  return request({
    url: '/biz/roadHazardRecord/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询隐患巡查记录详细
 * @param id
 */
export const getRoadHazardRecord = (id: string | number): AxiosPromise<RoadHazardRecordVO> => {
  return request({
    url: '/biz/roadHazardRecord/' + id,
    method: 'get'
  });
};

/**
 * 新增隐患巡查记录
 * @param data
 */
export const addRoadHazardRecord = (data: RoadHazardRecordForm) => {
  return request({
    url: '/biz/roadHazardRecord',
    method: 'post',
    data: data
  });
};

/**
 * 修改隐患巡查记录
 * @param data
 */
export const updateRoadHazardRecord = (data: RoadHazardRecordForm) => {
  return request({
    url: '/biz/roadHazardRecord',
    method: 'put',
    data: data
  });
};

/**
 * 删除隐患巡查记录
 * @param id
 */
export const delRoadHazardRecord = (id: string | number | Array<string | number>) => {
  return request({
    url: '/biz/roadHazardRecord/' + id,
    method: 'delete'
  });
};

/**
 * 获取巡查记录涉及的隐患点摘要列表
 * @param recordId 巡查记录ID
 * @param query 查询参数
 */
export const getHazardPointsByRecordId = (recordId: string | number, query?: { pageNum?: number; pageSize?: number }) => {
  return request({
    url: `/biz/roadHazardRecord/${recordId}/hazardPoints`,
    method: 'get',
    params: query
  });
};

/**
 * 获取上一次巡检记录ID
 * @param currentRecordId 当前巡检记录ID
 * @param hazardPointId 隐患点ID
 */
export const getPreviousRecordId = (currentRecordId: string | number, hazardPointId: string | number): AxiosPromise<number> => {
  return request({
    url: `/biz/roadHazardRecord/previous/${currentRecordId}`,
    method: 'get',
    params: { hazardPointId }
  });
};
