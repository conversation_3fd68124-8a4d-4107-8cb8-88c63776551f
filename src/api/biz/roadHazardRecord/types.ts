export interface RoadHazardRecordVO {
  /**
   * 任务执行结果ID，主键
   */
  id: string | number;

  /**
   * 任务名称
   */
  taskName: string;

  /**
   * 执行航线名称
   */
  routeName: string;

  /**
   * 实际开始时间
   */
  actualStartTime: string;

  /**
   * 实际结束时间
   */
  actualEndTime: string;

}

export interface RoadHazardRecordForm extends BaseEntity {
  /**
   * 任务执行结果ID，主键
   */
  id?: string | number;

  /**
   * 任务名称
   */
  taskName?: string;

  /**
   * 执行航线名称
   */
  routeName?: string;

  /**
   * 是否存在风险：Y是    N否
   */
  hasRisk?: string;

}

export interface RoadHazardRecordQuery extends PageQuery {

  /**
   * 任务名称
   */
  taskName?: string;

  /**
   * 任务类型
   */
  taskType?: string;

  /**
   * 任务频率
   */
  taskFrequency?: string;

  /**
   * 任务计划开始时间
   */
  plannedStartTime?: string;

  /**
   * 任务计划结束时间
   */
  plannedEndTime?: string;

  /**
   * 执行航线名称
   */
  routeName?: string;

  /**
   * 执行设备名称
   */
  executionDeviceName?: string;

  /**
   * 执行人员
   */
  executor?: string;

  /**
   * 任务状态
   */
  taskStatus?: string;

  /**
   * 实际开始时间
   */
  actualStartTime?: string;

  /**
   * 实际结束时间
   */
  actualEndTime?: string;

  /**
   * 返航高度
   */
  returnAltitude?: number;

  /**
   * 矢量操作
   */
  vectorOperation?: string;

  /**
   * 机场名称
   */
  dockName?: string;

  /**
   * 是否存在风险：Y是    N否
   */
  hasRisk?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}

/**
 * 隐患点摘要视图对象
 */
export interface HazardPointSummaryVO {
  /**
   * 隐患点ID
   */
  hazardPointId: number;

  /**
   * 隐患点名称
   */
  hazardPointName: string;

  /**
   * 隐患点类型
   */
  hazardType: string;

  /**
   * 桩号
   */
  pileNo: string;

  /**
   * 该隐患点在本次巡检中是否有风险 (Y/N)
   */
  hasRisk: string;

  /**
   * 图片数量（用于显示"查看图片"按钮）
   */
  imageCount: number;
}



