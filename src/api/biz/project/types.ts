export interface ProjectVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 项目编码
   */
  projectCode: string;

  /**
   * 项目名称
   */
  projectName: string;

  /**
   * 飞控平台工作区间id
   */
  workspaceId: string | number;

  /**
   * 项目描述
   */
  projectDesc: string;

  /**
   * 项目状态：ACTIVE / ARCHIVED / DELETED
   */
  status: string;

}

export interface ProjectForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 项目编码
   */
  projectCode?: string;

  /**
   * 项目名称
   */
  projectName?: string;

  /**
   * 飞控平台工作区间id
   */
  workspaceId?: string | number;

  /**
   * 项目描述
   */
  projectDesc?: string;

  /**
   * 项目状态：ACTIVE / ARCHIVED / DELETED
   */
  status?: string;

  /**
   * 扩展配置
   */
  configJson?: string;

}

export interface ProjectQuery extends PageQuery {

  /**
   * 项目编码
   */
  projectCode?: string;

  /**
   * 项目名称
   */
  projectName?: string;

  /**
   * 飞控平台工作区间id
   */
  workspaceId?: string | number;

  /**
   * 项目状态：ACTIVE / ARCHIVED / DELETED
   */
  status?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



