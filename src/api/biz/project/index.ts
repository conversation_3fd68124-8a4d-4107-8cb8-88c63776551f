import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ProjectVO, ProjectForm, ProjectQuery } from '@/api/biz/project/types';

/**
 * 查询项目管理列表
 * @param query
 * @returns {*}
 */

export const listProject = (query?: ProjectQuery): AxiosPromise<ProjectVO[]> => {
  return request({
    url: '/biz/project/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询项目管理详细
 * @param id
 */
export const getProject = (id: string | number): AxiosPromise<ProjectVO> => {
  return request({
    url: '/biz/project/' + id,
    method: 'get'
  });
};

/**
 * 新增项目管理
 * @param data
 */
export const addProject = (data: ProjectForm) => {
  return request({
    url: '/biz/project',
    method: 'post',
    data: data
  });
};

/**
 * 修改项目管理
 * @param data
 */
export const updateProject = (data: ProjectForm) => {
  return request({
    url: '/biz/project',
    method: 'put',
    data: data
  });
};

/**
 * 删除项目管理
 * @param id
 */
export const delProject = (id: string | number | Array<string | number>) => {
  return request({
    url: '/biz/project/' + id,
    method: 'delete'
  });
};

/**
 * 获取当前租户有权限的项目列表
 * @returns {*}
 */
export const listTenantProjects = (): AxiosPromise<ProjectVO[]> => {
  return request({
    url: '/biz/project/tenant/all',
    method: 'get'
  });
};
