export interface RoadThrowObjectInfoVO {
  /**
   * 抛洒物记录ID，主键
   */
  id: string | number;

  /**
   * 关联的智能巡检任务结果ID
   */
  taskResultId: string | number;

  /**
   * 抛洒物类型
   */
  throwType: string;

  /**
   * 纬度
   */
  latitude: number;

  /**
   * 经度
   */
  longitude: number;

  /**
   * 图片URL
   */
  imageUrl: string;

  /**
   * 置信度，范围0-100%
   */
  confidence: string | number;

  /**
   * 抛洒物确认，1为已确认，0为未确认
   */
  throwConfirmed: string;

  /**
   * 发生时间
   */
  eventTime: string;

  /**
   * 桩号
   */
  pileNo: string;

  /**
   * 任务记录编号
   */
  taskResultNo: string;

  /**
   * 路线编号
   */
  roadNum: string;

  /**
   * 备注
   */
  remark: string;
}

export interface RoadThrowObjectInfoForm extends BaseEntity {
  /**
   * 抛洒物记录ID，主键
   */
  id?: string | number;

  /**
   * 关联的智能巡检任务结果ID
   */
  taskResultId?: string | number;

  /**
   * 抛洒物类型
   */
  throwType?: string;

  /**
   * 纬度
   */
  latitude?: number;

  /**
   * 经度
   */
  longitude?: number;

  /**
   * 图片URL
   */
  imageUrl?: string;

  /**
   * 置信度，范围0-100%
   */
  confidence?: string | number;

  /**
   * 抛洒物确认，1为已确认，0为未确认
   */
  throwConfirmed?: string;

  /**
   * 发生时间
   */
  eventTime?: string;

  /**
   * 桩号
   */
  pileNo?: string;

  /**
   * 任务记录编号
   */
  taskResultNo?: string;

  /**
   * 路线编号
   */
  roadNum?: string;

  /**
   * 备注
   */
  remark?: string;
}

export interface RoadThrowObjectInfoQuery extends PageQuery {
  /**
   * 关联的智能巡检任务结果ID
   */
  taskResultId?: string | number;

  /**
   * 抛洒物类型
   */
  throwType?: string;

  /**
   * 纬度
   */
  latitude?: number;

  /**
   * 经度
   */
  longitude?: number;

  /**
   * 图片URL
   */
  imageUrl?: string;

  /**
   * 置信度，范围0-100%
   */
  confidence?: string | number;

  /**
   * 抛洒物确认，1为已确认，0为未确认
   */
  throwConfirmed?: string;

  /**
   * 发生时间
   */
  eventTime?: string;

  /**
   * 桩号
   */
  pileNo?: string;

  /**
   * 任务记录编号
   */
  taskResultNo?: string;

  /**
   * 路线编号
   */
  roadNum?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}

/**
 * 抛洒物统计信息VO
 */
export interface BizRoadThrowObjectStatisticsVo {
  /**
   * 抛洒物类型
   */
  throwType: string;

  /**
   * 抛洒物数量
   */
  count: number;
}
