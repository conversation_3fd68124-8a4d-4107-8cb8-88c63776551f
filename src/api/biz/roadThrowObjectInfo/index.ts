import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { RoadThrowObjectInfoVO, RoadThrowObjectInfoForm, RoadThrowObjectInfoQuery } from '@/api/biz/roadThrowObjectInfo/types';
import { BizRoadThrowObjectStatisticsVo } from '@/api/biz/roadThrowObjectInfo/types';

/**
 * 查询公路智能巡检抛洒物信息列表
 * @param query
 * @returns {*}
 */

export const listRoadThrowObjectInfo = (query?: RoadThrowObjectInfoQuery): AxiosPromise<RoadThrowObjectInfoVO[]> => {
  return request({
    url: '/biz/roadThrowObjectInfo/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询公路智能巡检抛洒物信息详细
 * @param id
 */
export const getRoadThrowObjectInfo = (id: string | number): AxiosPromise<RoadThrowObjectInfoVO> => {
  return request({
    url: '/biz/roadThrowObjectInfo/' + id,
    method: 'get'
  });
};

/**
 * 新增公路智能巡检抛洒物信息
 * @param data
 */
export const addRoadThrowObjectInfo = (data: RoadThrowObjectInfoForm) => {
  return request({
    url: '/biz/roadThrowObjectInfo',
    method: 'post',
    data: data
  });
};

/**
 * 修改公路智能巡检抛洒物信息
 * @param data
 */
export const updateRoadThrowObjectInfo = (data: RoadThrowObjectInfoForm) => {
  return request({
    url: '/biz/roadThrowObjectInfo',
    method: 'put',
    data: data
  });
};

/**
 * 删除公路智能巡检抛洒物信息
 * @param id
 */
export const delRoadThrowObjectInfo = (id: string | number | Array<string | number>) => {
  return request({
    url: '/biz/roadThrowObjectInfo/' + id,
    method: 'delete'
  });
};

/**
 * 查询公路巡检抛洒物统计信息
 * @param taskResultId
 * @returns {*}
 */
export const getThrowObjectStatistics = (taskResultId?: string | number): AxiosPromise<BizRoadThrowObjectStatisticsVo[]> => {
  return request({
    url: '/biz/roadThrowObjectInfo/statistics',
    method: 'get',
    params: { taskResultId }
  });
};
