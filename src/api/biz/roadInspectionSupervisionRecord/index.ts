import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import {
  RoadInspectionSupervisionRecordVO,
  RoadInspectionSupervisionRecordForm,
  RoadInspectionSupervisionRecordQuery
} from '@/api/biz/roadInspectionSupervisionRecord/types';
import { FlightTaskResultQuery, FlightTaskResultVO } from '@/api/biz/flightTaskResult/types';
import { BizRoadInspectionSupervisionStatisticsVo } from '@/api/biz/roadInspectionSupervisionRecord/types';
/**
 * 查询公路施工巡检监管记录列表
 * @param query
 * @returns {*}
 */

export const listSupervisionFlightTaskResult = (query?: FlightTaskResultQuery): AxiosPromise<FlightTaskResultVO[]> => {
  return request({
    url: '/biz/roadInspectionSupervisionRecord/listTask',
    method: 'get',
    params: query
  });
};

export const listRoadInspectionSupervisionRecord = (
  query?: RoadInspectionSupervisionRecordQuery
): AxiosPromise<RoadInspectionSupervisionRecordVO[]> => {
  return request({
    url: '/biz/roadInspectionSupervisionRecord/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询公路施工巡检监管记录详细
 * @param id
 */
export const getRoadInspectionSupervisionRecord = (id: string | number): AxiosPromise<RoadInspectionSupervisionRecordVO> => {
  return request({
    url: '/biz/roadInspectionSupervisionRecord/' + id,
    method: 'get'
  });
};

/**
 * 新增公路施工巡检监管记录
 * @param data
 */
export const addRoadInspectionSupervisionRecord = (data: RoadInspectionSupervisionRecordForm) => {
  return request({
    url: '/biz/roadInspectionSupervisionRecord',
    method: 'post',
    data: data
  });
};

/**
 * 修改公路施工巡检监管记录
 * @param data
 */
export const updateRoadInspectionSupervisionRecord = (data: RoadInspectionSupervisionRecordForm) => {
  return request({
    url: '/biz/roadInspectionSupervisionRecord',
    method: 'put',
    data: data
  });
};

/**
 * 删除公路施工巡检监管记录
 * @param id
 */
export const delRoadInspectionSupervisionRecord = (id: string | number | Array<string | number>) => {
  return request({
    url: '/biz/roadInspectionSupervisionRecord/' + id,
    method: 'delete'
  });
};

/**
 * 查询公路巡检监管记录统计信息
 * @param taskResultId
 * @returns {*}
 */
export const getSupervisionStatistics = (taskResultId?: string | number): AxiosPromise<BizRoadInspectionSupervisionStatisticsVo[]> => {
  return request({
    url: '/biz/roadInspectionSupervisionRecord/statistics',
    method: 'get',
    params: { taskResultId }
  });
};
