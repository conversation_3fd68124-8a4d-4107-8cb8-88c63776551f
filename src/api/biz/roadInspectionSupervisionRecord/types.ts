export interface RoadInspectionSupervisionRecordVO {
  /**
   * 监管记录ID，主键
   */
  id: string | number;

  /**
   * 关联的施工巡检任务结果ID
   */
  taskResultId: string | number;

  /**
   * 工地纬度
   */
  siteLatitude: number;

  /**
   * 工地经度
   */
  siteLongitude: number;

  /**
   * 安全事件类型
   */
  evenType: string;

  /**
   * 是否穿戴安全装备
   */
  isWearingSafetyEquipment: string;

  /**
   * 图片URL
   */
  imageUrl: string;

  /**
   * 图片URLUrl
   */
  imageUrlUrl: string;
  /**
   * 置信度，范围0-100%
   */
  confidence: string | number;

  /**
   * 识别结果是否确认
   */
  recognitionResultConfirmed: string;
}

export interface RoadInspectionSupervisionRecordForm extends BaseEntity {
  /**
   * 监管记录ID，主键
   */
  id?: string | number;

  /**
   * 关联的施工巡检任务结果ID
   */
  taskResultId?: string | number;

  /**
   * 工地纬度
   */
  siteLatitude?: number;

  /**
   * 工地经度
   */
  siteLongitude?: number;

  /**
   * 安全事件类型
   */
  evenType?: string;

  /**
   * 是否穿戴安全装备
   */
  isWearingSafetyEquipment?: string;

  /**
   * 图片URL
   */
  imageUrl?: string;

  /**
   * 置信度，范围0-100%
   */
  confidence?: string | number;

  /**
   * 识别结果是否确认
   */
  recognitionResultConfirmed?: string;
}

export interface RoadInspectionSupervisionRecordQuery extends PageQuery {
  /**
   * 关联的施工巡检任务结果ID
   */
  taskResultId?: string | number;

  /**
   * 工地纬度
   */
  siteLatitude?: number;

  /**
   * 工地经度
   */
  siteLongitude?: number;

  /**
   * 安全事件类型
   */
  evenType?: string;

  /**
   * 是否穿戴安全装备
   */
  isWearingSafetyEquipment?: string;

  /**
   * 图片URL
   */
  imageUrl?: string;

  /**
   * 置信度，范围0-100%
   */
  confidence?: string | number;

  /**
   * 识别结果是否确认
   */
  recognitionResultConfirmed?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}

/**
 * 施工监管统计信息VO
 */
export interface BizRoadInspectionSupervisionStatisticsVo {
  /**
   * 事件类型
   */
  evenType: string;

  /**
   * 事件数量
   */
  count: number;
}
