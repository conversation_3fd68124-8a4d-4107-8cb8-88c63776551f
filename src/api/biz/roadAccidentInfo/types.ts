export interface RoadAccidentInfoVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 巡检任务id
   */
  taskResultId: string | number;

  /**
   * 巡检任务名称
   */
  taskResultName: string;

  /**
   * 事故时间
   */
  eventTime: string;

  /**
   * 事件类型
   */
  evenType: string;

  /**
   * 纬度
   */
  latitude: number;

  /**
   * 经度
   */
  longitude: number;

  /**
   * 项目名称
   */
  projectName: string;

  /**
   * 桩号
   */
  pileNo: string;

  /**
   * 对象存储id
   */
  ossId: string | number;

  /**
   * 对象存储idUrl
   */
  ossIdUrl: string;
  /**
   * 图片URL
   */
  imageUrl: string;

  /**
   * 置信度
   */
  confidence: string | number;

  /**
   * 是否确认
   */
  isConfirmed: string;

  /**
   * 是否推送
   */
  isPush: string;
}

export interface RoadAccidentInfoForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 巡检任务id
   */
  taskResultId?: string | number;

  /**
   * 巡检任务名称
   */
  taskResultName?: string;

  /**
   * 事故时间
   */
  eventTime?: string;

  /**
   * 事件类型
   */
  evenType?: string;

  /**
   * 纬度
   */
  latitude?: number;

  /**
   * 经度
   */
  longitude?: number;

  /**
   * 项目名称
   */
  projectName?: string;

  /**
   * 桩号
   */
  pileNo?: string;

  /**
   * 对象存储id
   */
  ossId?: string | number;

  /**
   * 图片URL
   */
  imageUrl?: string;

  /**
   * 置信度
   */
  confidence?: string | number;

  /**
   * 是否确认
   */
  isConfirmed?: string;

  /**
   * 是否推送
   */
  isPush?: string;
}

export interface RoadAccidentInfoQuery extends PageQuery {
  /**
   * 巡检任务id
   */
  taskResultId?: string | number;

  /**
   * 巡检任务名称
   */
  taskResultName?: string;

  /**
   * 事故时间
   */
  eventTime?: string;

  /**
   * 事件类型
   */
  evenType?: string;

  /**
   * 纬度
   */
  latitude?: number;

  /**
   * 经度
   */
  longitude?: number;

  /**
   * 项目名称
   */
  projectName?: string;

  /**
   * 桩号
   */
  pileNo?: string;

  /**
   * 对象存储id
   */
  ossId?: string | number;

  /**
   * 图片URL
   */
  imageUrl?: string;

  /**
   * 置信度
   */
  confidence?: string | number;

  /**
   * 是否确认
   */
  isConfirmed?: string;

  /**
   * 是否推送
   */
  isPush?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}

/**
 * 交通事故统计信息VO
 */
export interface BizRoadAccidentStatisticsVo {
  /**
   * 事件类型
   */
  evenType: string;

  /**
   * 数量
   */
  count: number;
}
