import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { RoadAccidentInfoVO, RoadAccidentInfoForm, RoadAccidentInfoQuery } from '@/api/biz/roadAccidentInfo/types';
import { FlightTaskResultQuery, FlightTaskResultVO } from '@/api/biz/flightTaskResult/types';
import { BizRoadAccidentStatisticsVo } from '@/api/biz/roadAccidentInfo/types';

/**
 * 查询交通事故信息列表
 * @param query
 * @returns {*}
 */

export const listAccidentFlightTaskResult = (query?: FlightTaskResultQuery): AxiosPromise<FlightTaskResultVO[]> => {
  return request({
    url: '/biz/roadAccidentInfo/listTask',
    method: 'get',
    params: query
  });
};

export const listRoadAccidentInfo = (query?: RoadAccidentInfoQuery): AxiosPromise<RoadAccidentInfoVO[]> => {
  return request({
    url: '/biz/roadAccidentInfo/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询交通事故信息详细
 * @param id
 */
export const getRoadAccidentInfo = (id: string | number): AxiosPromise<RoadAccidentInfoVO> => {
  return request({
    url: '/biz/roadAccidentInfo/' + id,
    method: 'get'
  });
};

/**
 * 新增交通事故信息
 * @param data
 */
export const addRoadAccidentInfo = (data: RoadAccidentInfoForm) => {
  return request({
    url: '/biz/roadAccidentInfo',
    method: 'post',
    data: data
  });
};

/**
 * 修改交通事故信息
 * @param data
 */
export const updateRoadAccidentInfo = (data: RoadAccidentInfoForm) => {
  return request({
    url: '/biz/roadAccidentInfo',
    method: 'put',
    data: data
  });
};

/**
 * 删除交通事故信息
 * @param id
 */
export const delRoadAccidentInfo = (id: string | number | Array<string | number>) => {
  return request({
    url: '/biz/roadAccidentInfo/' + id,
    method: 'delete'
  });
};

/**
 * 发送短信通知
 * @param id
 */
export const sendSmsNotify = (id: string | number) => {
  return request({
    url: '/biz/roadAccidentInfo/sendLevel2SmsNotify/' + id,
    method: 'get'
  });
};

/**
 * 查询公路巡检交通事故统计信息
 * @param taskResultId
 * @param evenType
 * @returns {*}
 */
export const getAccidentStatistics = (taskResultId?: string | number, evenType?: string): AxiosPromise<BizRoadAccidentStatisticsVo[]> => {
  return request({
    url: '/biz/roadAccidentInfo/statistics',
    method: 'get',
    params: { taskResultId, evenType }
  });
};
