export interface FlightWaypointVO {
  /**
   * 航点ID，主键
   */
  id: string | number;

  /**
   * 关联的航线ID
   */
  routeId: string | number;

  /**
   * 航点名称
   */
  waypointName: string;

  /**
   * 飞行高度（米）
   */
  flightAltitude: number;

  /**
   * 航点动作
   */
  waypointAction: string;

  /**
   * 云台俯仰角（度）
   */
  gimbalPitchAngle: number;

  /**
   * 云台平移角（度）
   */
  gimbalRollAngle: number;

  /**
   * 经度
   */
  longitude: number;

  /**
   * 纬度
   */
  latitude: number;

  /**
   * 是否为返航点
   */
  isReturnPoint: string;

  /**
   * 垂直降落高度（米）
   */
  verticalLandingHeight: number;

}

export interface FlightWaypointForm extends BaseEntity {
  /**
   * 航点ID，主键
   */
  id?: string | number;

  /**
   * 关联的航线ID
   */
  routeId?: string | number;

  /**
   * 航点名称
   */
  waypointName?: string;

  /**
   * 飞行高度（米）
   */
  flightAltitude?: number;

  /**
   * 航点动作
   */
  waypointAction?: string;

  /**
   * 云台俯仰角（度）
   */
  gimbalPitchAngle?: number;

  /**
   * 云台平移角（度）
   */
  gimbalRollAngle?: number;

  /**
   * 经度
   */
  longitude?: number;

  /**
   * 纬度
   */
  latitude?: number;

  /**
   * 是否为返航点
   */
  isReturnPoint?: string;

  /**
   * 垂直降落高度（米）
   */
  verticalLandingHeight?: number;

}

export interface FlightWaypointQuery extends PageQuery {

  /**
   * 关联的航线ID
   */
  routeId?: string | number;

  /**
   * 航点名称
   */
  waypointName?: string;

  /**
   * 飞行高度（米）
   */
  flightAltitude?: number;

  /**
   * 航点动作
   */
  waypointAction?: string;

  /**
   * 云台俯仰角（度）
   */
  gimbalPitchAngle?: number;

  /**
   * 云台平移角（度）
   */
  gimbalRollAngle?: number;

  /**
   * 经度
   */
  longitude?: number;

  /**
   * 纬度
   */
  latitude?: number;

  /**
   * 是否为返航点
   */
  isReturnPoint?: string;

  /**
   * 垂直降落高度（米）
   */
  verticalLandingHeight?: number;

    /**
     * 日期范围参数
     */
    params?: any;
}



