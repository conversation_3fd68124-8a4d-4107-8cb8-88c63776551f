import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { FlightWaypointVO, FlightWaypointForm, FlightWaypointQuery } from '@/api/biz/flightWaypoint/types';

/**
 * 查询航点列表
 * @param query
 * @returns {*}
 */

export const listFlightWaypoint = (query?: FlightWaypointQuery): AxiosPromise<FlightWaypointVO[]> => {
  return request({
    url: '/biz/flightWaypoint/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询航点详细
 * @param id
 */
export const getFlightWaypoint = (id: string | number): AxiosPromise<FlightWaypointVO> => {
  return request({
    url: '/biz/flightWaypoint/' + id,
    method: 'get'
  });
};

/**
 * 新增航点
 * @param data
 */
export const addFlightWaypoint = (data: FlightWaypointForm) => {
  return request({
    url: '/biz/flightWaypoint',
    method: 'post',
    data: data
  });
};

/**
 * 修改航点
 * @param data
 */
export const updateFlightWaypoint = (data: FlightWaypointForm) => {
  return request({
    url: '/biz/flightWaypoint',
    method: 'put',
    data: data
  });
};

/**
 * 删除航点
 * @param id
 */
export const delFlightWaypoint = (id: string | number | Array<string | number>) => {
  return request({
    url: '/biz/flightWaypoint/' + id,
    method: 'delete'
  });
};
