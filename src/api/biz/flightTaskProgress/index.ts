import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { FlightTaskProgressVO, FlightTaskProgressForm, FlightTaskProgressQuery } from '@/api/biz/flightTaskProgress/types';

/**
 * 查询任务进度列表
 * @param query
 * @returns {*}
 */

export const listFlightTaskProgress = (query?: FlightTaskProgressQuery): AxiosPromise<FlightTaskProgressVO[]> => {
  return request({
    url: '/biz/flightTaskProgress/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询任务进度详细
 * @param id
 */
export const getFlightTaskProgress = (id: string | number): AxiosPromise<FlightTaskProgressVO> => {
  return request({
    url: '/biz/flightTaskProgress/' + id,
    method: 'get'
  });
};

/**
 * 新增任务进度
 * @param data
 */
export const addFlightTaskProgress = (data: FlightTaskProgressForm) => {
  return request({
    url: '/biz/flightTaskProgress',
    method: 'post',
    data: data
  });
};

/**
 * 修改任务进度
 * @param data
 */
export const updateFlightTaskProgress = (data: FlightTaskProgressForm) => {
  return request({
    url: '/biz/flightTaskProgress',
    method: 'put',
    data: data
  });
};

/**
 * 删除任务进度
 * @param id
 */
export const delFlightTaskProgress = (id: string | number | Array<string | number>) => {
  return request({
    url: '/biz/flightTaskProgress/' + id,
    method: 'delete'
  });
};
