export interface FlightTaskProgressVO {
  /**
   * 进度ID，主键
   */
  id: string | number;

  /**
   * 关联的任务结果ID
   */
  taskId: string | number;

  /**
   * 进度时间
   */
  progressTime: string;

  /**
   * 进度状态
   */
  progressStatus: string;

  /**
   * 进度详细信息
   */
  progressDetails: string;

}

export interface FlightTaskProgressForm extends BaseEntity {
  /**
   * 进度ID，主键
   */
  id?: string | number;

  /**
   * 关联的任务结果ID
   */
  taskId?: string | number;

  /**
   * 进度时间
   */
  progressTime?: string;

  /**
   * 进度状态
   */
  progressStatus?: string;

  /**
   * 进度详细信息
   */
  progressDetails?: string;

}

export interface FlightTaskProgressQuery extends PageQuery {

  /**
   * 关联的任务结果ID
   */
  taskId?: string | number;

  /**
   * 进度时间
   */
  progressTime?: string;

  /**
   * 进度状态
   */
  progressStatus?: string;

  /**
   * 进度详细信息
   */
  progressDetails?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



