export interface RoadRightOccupationInfoVO {
  /**
   * 侵占路权记录ID，主键
   */
  id: string | number;

  /**
   * 关联的智能巡检任务结果ID
   */
  taskResultId: string | number;

  /**
   * 侵占类型
   */
  occupationType: string;

  /**
   * 纬度
   */
  latitude: number;

  /**
   * 经度
   */
  longitude: number;

  /**
   * 图片URL
   */
  imageUrl: string;

  /**
   * 置信度，范围0-100%
   */
  confidence: string | number;

  /**
   * 侵占确认，1为已确认，0为未确认
   */
  occupationConfirmed: string;

  /**
   * 发生时间
   */
  eventTime: string;

  /**
   * 桩号
   */
  pileNo: string;

  /**
   * 任务记录编号
   */
  taskResultNo: string;

  /**
   * 路线编号
   */
  roadNum: string;

  /**
   * 备注
   */
  remark: string;
}

export interface RoadRightOccupationInfoForm extends BaseEntity {
  /**
   * 侵占路权记录ID，主键
   */
  id?: string | number;

  /**
   * 关联的智能巡检任务结果ID
   */
  taskResultId?: string | number;

  /**
   * 侵占类型
   */
  occupationType?: string;

  /**
   * 纬度
   */
  latitude?: number;

  /**
   * 经度
   */
  longitude?: number;

  /**
   * 图片URL
   */
  imageUrl?: string;

  /**
   * 置信度，范围0-100%
   */
  confidence?: string | number;

  /**
   * 侵占确认，1为已确认，0为未确认
   */
  occupationConfirmed?: string;

  /**
   * 发生时间
   */
  eventTime?: string;

  /**
   * 桩号
   */
  pileNo?: string;

  /**
   * 任务记录编号
   */
  taskResultNo?: string;

  /**
   * 路线编号
   */
  roadNum?: string;

  /**
   * 备注
   */
  remark?: string;
}

export interface RoadRightOccupationInfoQuery extends PageQuery {
  /**
   * 侵占类型
   */
  occupationType?: string;

  /**
   * 置信度，范围0-100%
   */
  confidence?: string | number;

  /**
   * 侵占确认，1为已确认，0为未确认
   */
  occupationConfirmed?: string;

  /**
   * 发生时间
   */
  eventTime?: string;

  /**
   * 桩号
   */
  pileNo?: string;

  /**
   * 任务记录编号
   */
  taskResultNo?: string;

  /**
   * 路线编号
   */
  roadNum?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}

/**
 * 侵占路权统计信息VO
 */
export interface BizRoadRightOccupationStatisticsVo {
  /**
   * 侵占类型
   */
  occupationType: string;

  /**
   * 侵占数量
   */
  count: number;
}
