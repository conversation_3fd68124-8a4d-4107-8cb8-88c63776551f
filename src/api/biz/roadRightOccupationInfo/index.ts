import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { RoadRightOccupationInfoVO, RoadRightOccupationInfoForm, RoadRightOccupationInfoQuery } from '@/api/biz/roadRightOccupationInfo/types';
import { BizRoadRightOccupationStatisticsVo } from '@/api/biz/roadRightOccupationInfo/types';

/**
 * 查询公路智能巡检侵占路权信息列表
 * @param query
 * @returns {*}
 */

export const listRoadRightOccupationInfo = (query?: RoadRightOccupationInfoQuery): AxiosPromise<RoadRightOccupationInfoVO[]> => {
  return request({
    url: '/biz/roadRightOccupationInfo/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询公路智能巡检侵占路权信息详细
 * @param id
 */
export const getRoadRightOccupationInfo = (id: string | number): AxiosPromise<RoadRightOccupationInfoVO> => {
  return request({
    url: '/biz/roadRightOccupationInfo/' + id,
    method: 'get'
  });
};

/**
 * 新增公路智能巡检侵占路权信息
 * @param data
 */
export const addRoadRightOccupationInfo = (data: RoadRightOccupationInfoForm) => {
  return request({
    url: '/biz/roadRightOccupationInfo',
    method: 'post',
    data: data
  });
};

/**
 * 修改公路智能巡检侵占路权信息
 * @param data
 */
export const updateRoadRightOccupationInfo = (data: RoadRightOccupationInfoForm) => {
  return request({
    url: '/biz/roadRightOccupationInfo',
    method: 'put',
    data: data
  });
};

/**
 * 删除公路智能巡检侵占路权信息
 * @param id
 */
export const delRoadRightOccupationInfo = (id: string | number | Array<string | number>) => {
  return request({
    url: '/biz/roadRightOccupationInfo/' + id,
    method: 'delete'
  });
};

/**
 * 查询公路巡检侵占路权统计信息
 * @param taskResultId
 * @returns {*}
 */
export const getOccupationStatistics = (taskResultId?: string | number): AxiosPromise<BizRoadRightOccupationStatisticsVo[]> => {
  return request({
    url: '/biz/roadRightOccupationInfo/statistics',
    method: 'get',
    params: { taskResultId }
  });
};
