export interface RoadHazardPointVO {
  /**
   * 隐患点ID，主键
   */
  id: string | number;

  /**
   * 项目ID
   */
  projectId?: string | number;

  /**
   * 项目名称
   */
  projectName?: string;

  /**
   * 桩号
   */
  pileNo: string;

  /**
   * 隐患点类型
   */
  hazardType: string;

  /**
   * 隐患点名称
   */
  hazardName: string;

  /**
   * 最新巡检时间
   */
  latestInspectTime?: string;

}

export interface RoadHazardPointForm extends BaseEntity {
  /**
   * 隐患点ID，主键
   */
  id?: string | number;

  /**
   * 项目ID
   */
  projectId?: string | number;

  /**
   * 项目名称
   */
  projectName?: string;

  /**
   * 桩号
   */
  pileNo?: string;

  /**
   * 隐患点类型
   */
  hazardType?: string;

  /**
   * 隐患点名称
   */
  hazardName?: string;

}

export interface RoadHazardPointQuery extends PageQuery {

  /**
   * 项目名称
   */
  projectName?: string;

  /**
   * 桩号
   */
  pileNo?: string;

  /**
   * 隐患点类型
   */
  hazardType?: string;

  /**
   * 隐患点名称
   */
  hazardName?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}

/**
 * 隐患点巡检记录VO
 */
export interface HazardPointInspectRecordVO {
  /**
   * 巡检记录ID
   */
  recordId: string | number;

  /**
   * 任务名称
   */
  taskName?: string;

  /**
   * 实际开始时间
   */
  actualStartTime?: string;

  /**
   * 实际结束时间
   */
  actualEndTime?: string;

  /**
   * 航线名称
   */
  routeName?: string;

  /**
   * 整个巡检记录是否有风险
   */
  recordHasRisk?: string;

  /**
   * 该隐患点在本次巡检中的风险条目数
   */
  riskItemCount?: number;

  /**
   * 该隐患点在本次巡检中的总条目数
   */
  totalItemCount?: number;

  /**
   * 该隐患点在本次巡检中是否有风险 (Y/N)
   */
  pointHasRisk?: string;
}



