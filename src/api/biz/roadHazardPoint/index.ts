import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { RoadHazardPointVO, RoadHazardPointForm, RoadHazardPointQuery } from '@/api/biz/roadHazardPoint/types';

/**
 * 查询公路隐患点列表
 * @param query
 * @returns {*}
 */

export const listRoadHazardPoint = (query?: RoadHazardPointQuery): AxiosPromise<RoadHazardPointVO[]> => {
  return request({
    url: '/biz/roadHazardPoint/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询公路隐患点详细
 * @param id
 */
export const getRoadHazardPoint = (id: string | number): AxiosPromise<RoadHazardPointVO> => {
  return request({
    url: '/biz/roadHazardPoint/' + id,
    method: 'get'
  });
};

/**
 * 新增公路隐患点
 * @param data
 */
export const addRoadHazardPoint = (data: RoadHazardPointForm) => {
  return request({
    url: '/biz/roadHazardPoint',
    method: 'post',
    data: data
  });
};

/**
 * 修改公路隐患点
 * @param data
 */
export const updateRoadHazardPoint = (data: RoadHazardPointForm) => {
  return request({
    url: '/biz/roadHazardPoint',
    method: 'put',
    data: data
  });
};

/**
 * 删除公路隐患点
 * @param id
 */
export const delRoadHazardPoint = (id: string | number | Array<string | number>) => {
  return request({
    url: '/biz/roadHazardPoint/' + id,
    method: 'delete'
  });
};

/**
 * 获取隐患点的历史巡检记录（分页）
 * @param hazardPointId 隐患点ID
 * @param pageQuery 分页参数
 */
export const getInspectRecordsByHazardPointId = (hazardPointId: string | number, pageQuery?: any): AxiosPromise<any> => {
  return request({
    url: `/biz/roadHazardPoint/${hazardPointId}/inspectRecords`,
    method: 'get',
    params: pageQuery
  });
};
