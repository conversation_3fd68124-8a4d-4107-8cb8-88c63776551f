export interface FlightTaskResultVO {
  /**
   * 任务执行结果ID，主键
   */
  id: string | number;

  /**
   * 任务名称
   */
  taskName: string;

  /**
   * 任务编号
   */
  taskNo: string;

  /**
   * 任务类型
   */
  taskType: string;

  /**
   * 任务频率
   */
  taskFrequency: string;

  /**
   * 任务计划开始时间
   */
  plannedStartTime: string;

  /**
   * 任务计划结束时间
   */
  plannedEndTime: string;

  /**
   * 每天定时开始时间
   */
  dailyScheduledStartTime: string;

  /**
   * 每天定时结束时间
   */
  dailyScheduledEndTime: string;

  /**
   * 电池容量达到
   */
  batteryCapacityReached: number;

  /**
   * 储存容量达到
   */
  storageCapacityReached: number;

  /**
   * 执行航线id
   */
  routeId: string | number;

  /**
   * 执行航线名称
   */
  routeName: string;

  /**
   * 执行设备id
   */
  executionDeviceId: string | number;

  /**
   * 执行设备名称
   */
  executionDeviceName: string;

  /**
   * 执行人员
   */
  executor: string;

  /**
   * 预计载重（0-40kg）
   */
  estimatedPayload: number;

  /**
   * 备注
   */
  notes: string;

  /**
   * 任务状态
   */
  taskStatus: string;

  /**
   * 实际开始时间
   */
  actualStartTime: string;

  /**
   * 实际结束时间
   */
  actualEndTime: string;

  /**
   * 返航高度
   */
  returnAltitude: number;

  /**
   * 矢量操作
   */
  vectorOperation: string;

  /**
   * 机场名称
   */
  dockName: string;

  /**
   * 机场序列号
   */
  dockSn: string;

  /**
   * 机场版本
   */
  dockVersion: string;

  /**
   * 无人机名称
   */
  droneName: string;

  /**
   * 无人机序列号
   */
  droneSn: string;

  /**
   * 无人机版本
   */
  droneVersion: string;

  /**
   * 操作人账号
   */
  flightAccount: string;

  /**
   * 起飞高度
   */
  takeoffAltitude: number;

  /**
   * 起飞纬度
   */
  takeoffLat: number;

  /**
   * 起飞经度
   */
  takeoffLng: number;

  /**
   * 飞控飞行记录id
   */
  flightId: string;

  /**
   * 飞行计划名称
   */
  flightPlanName: string;

  /**
   * 飞控工作区间id
   */
  workspaceId: string;

  /**
   * 飞控航线id
   */
  waylineId: string;

}

export interface FlightTaskResultForm extends BaseEntity {
  /**
   * 任务执行结果ID，主键
   */
  id?: string | number;

  /**
   * 任务计划id
   */
  taskId?: string | number;

  /**
   * 任务名称
   */
  taskName?: string;

  /**
   * 任务编号
   */
  taskNo?: string;

  /**
   * 任务类型
   */
  taskType?: string;

  /**
   * 任务频率
   */
  taskFrequency?: string;

  /**
   * 任务计划开始时间
   */
  plannedStartTime?: string;

  /**
   * 任务计划结束时间
   */
  plannedEndTime?: string;

  /**
   * 每天定时开始时间
   */
  dailyScheduledStartTime?: string;

  /**
   * 每天定时结束时间
   */
  dailyScheduledEndTime?: string;

  /**
   * 电池容量达到
   */
  batteryCapacityReached?: number;

  /**
   * 储存容量达到
   */
  storageCapacityReached?: number;

  /**
   * 执行航线id
   */
  routeId?: string | number;

  /**
   * 执行航线名称
   */
  routeName?: string;

  /**
   * 执行设备id
   */
  executionDeviceId?: string | number;

  /**
   * 执行设备名称
   */
  executionDeviceName?: string;

  /**
   * 执行人员
   */
  executor?: string;

  /**
   * 预计载重（0-40kg）
   */
  estimatedPayload?: number;

  /**
   * 备注
   */
  notes?: string;

  /**
   * 任务状态
   */
  taskStatus?: string;

  /**
   * 实际开始时间
   */
  actualStartTime?: string;

  /**
   * 实际结束时间
   */
  actualEndTime?: string;

  /**
   * 返航高度
   */
  returnAltitude?: number;

  /**
   * 矢量操作
   */
  vectorOperation?: string;

  /**
   * 机场名称
   */
  dockName?: string;

  /**
   * 机场序列号
   */
  dockSn?: string;

  /**
   * 机场版本
   */
  dockVersion?: string;

  /**
   * 无人机名称
   */
  droneName?: string;

  /**
   * 无人机序列号
   */
  droneSn?: string;

  /**
   * 无人机版本
   */
  droneVersion?: string;

  /**
   * 操作人账号
   */
  flightAccount?: string;

  /**
   * 起飞高度
   */
  takeoffAltitude?: number;

  /**
   * 起飞纬度
   */
  takeoffLat?: number;

  /**
   * 起飞经度
   */
  takeoffLng?: number;

  /**
   * 飞控飞行记录id
   */
  flightId?: string;

  /**
   * 飞行计划名称
   */
  flightPlanName?: string;

  /**
   * 飞控工作区间id
   */
  workspaceId?: string;

  /**
   * 飞控航线id
   */
  waylineId?: string;

}

export interface FlightTaskResultQuery extends PageQuery {

  /**
   * 任务计划id
   */
  taskId?: string | number;

  /**
   * 任务名称
   */
  taskName?: string;

  /**
   * 任务编号
   */
  taskNo?: string;

  /**
   * 任务类型
   */
  taskType?: string;

  /**
   * 任务频率
   */
  taskFrequency?: string;

  /**
   * 任务计划开始时间
   */
  plannedStartTime?: string;

  /**
   * 任务计划结束时间
   */
  plannedEndTime?: string;

  /**
   * 每天定时开始时间
   */
  dailyScheduledStartTime?: string;

  /**
   * 每天定时结束时间
   */
  dailyScheduledEndTime?: string;

  /**
   * 电池容量达到
   */
  batteryCapacityReached?: number;

  /**
   * 储存容量达到
   */
  storageCapacityReached?: number;

  /**
   * 执行航线id
   */
  routeId?: string | number;

  /**
   * 执行航线名称
   */
  routeName?: string;

  /**
   * 执行设备id
   */
  executionDeviceId?: string | number;

  /**
   * 执行设备名称
   */
  executionDeviceName?: string;

  /**
   * 执行人员
   */
  executor?: string;

  /**
   * 预计载重（0-40kg）
   */
  estimatedPayload?: number;

  /**
   * 备注
   */
  notes?: string;

  /**
   * 任务状态
   */
  taskStatus?: string;

  /**
   * 实际开始时间
   */
  actualStartTime?: string;

  /**
   * 实际结束时间
   */
  actualEndTime?: string;

  /**
   * 返航高度
   */
  returnAltitude?: number;

  /**
   * 矢量操作
   */
  vectorOperation?: string;

  /**
   * 机场名称
   */
  dockName?: string;

  /**
   * 机场序列号
   */
  dockSn?: string;

  /**
   * 机场版本
   */
  dockVersion?: string;

  /**
   * 无人机名称
   */
  droneName?: string;

  /**
   * 无人机序列号
   */
  droneSn?: string;

  /**
   * 无人机版本
   */
  droneVersion?: string;

  /**
   * 操作人账号
   */
  flightAccount?: string;

  /**
   * 起飞高度
   */
  takeoffAltitude?: number;

  /**
   * 起飞纬度
   */
  takeoffLat?: number;

  /**
   * 起飞经度
   */
  takeoffLng?: number;

  /**
   * 飞控飞行记录id
   */
  flightId?: string;

  /**
   * 飞行计划名称
   */
  flightPlanName?: string;

  /**
   * 飞控工作区间id
   */
  workspaceId?: string;

  /**
   * 飞控航线id
   */
  waylineId?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



