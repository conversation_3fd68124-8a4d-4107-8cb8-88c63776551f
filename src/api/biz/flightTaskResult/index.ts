import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { FlightTaskResultVO, FlightTaskResultForm, FlightTaskResultQuery } from '@/api/biz/flightTaskResult/types';
import { FlightRouteInfoQuery, FlightRouteInfoVO } from '@/api/biz/flightRouteInfo/types';

/**
 * 任务条数及总时长统计
 * @param query
 * @returns {*}
 */

export const flightTaskCountStatistics = (query?: FlightRouteInfoQuery): AxiosPromise<FlightRouteInfoVO[]> => {
  return request({
    url: '/biz/flightTaskResult/statistics',
    method: 'get',
    params: query
  });
};

/**
 * 查询飞行任务结果记录列表
 * @param query
 * @returns {*}
 */

export const listFlightTaskResult = (query?: FlightTaskResultQuery): AxiosPromise<FlightTaskResultVO[]> => {
  return request({
    url: '/biz/flightTaskResult/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询飞行任务结果记录详细
 * @param id
 */
export const getFlightTaskResult = (id: string | number): AxiosPromise<FlightTaskResultVO> => {
  return request({
    url: '/biz/flightTaskResult/' + id,
    method: 'get'
  });
};

/**
 * 新增飞行任务结果记录
 * @param data
 */
export const addFlightTaskResult = (data: FlightTaskResultForm) => {
  return request({
    url: '/biz/flightTaskResult',
    method: 'post',
    data: data
  });
};

/**
 * 修改飞行任务结果记录
 * @param data
 */
export const updateFlightTaskResult = (data: FlightTaskResultForm) => {
  return request({
    url: '/biz/flightTaskResult',
    method: 'put',
    data: data
  });
};

/**
 * 删除飞行任务结果记录
 * @param id
 */
export const delFlightTaskResult = (id: string | number | Array<string | number>) => {
  return request({
    url: '/biz/flightTaskResult/' + id,
    method: 'delete'
  });
};
