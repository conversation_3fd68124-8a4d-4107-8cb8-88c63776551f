export interface FlightDeviceVO {
  /**
   * 主键ID
   */
  id: string | number;

  /**
   * 飞行设备名称
   */
  deviceName: string;

  /**
   * 设备状态
   */
  status: string;

  /**
   * 控制设备名称
   */
  controller: string;

  /**
   * 租户ID
   */
  tenantId?: string | number;

  /**
   * 设备编号
   */
  deviceSn?: string;

  /**
   * 设备昵称
   */
  nickname?: string;

  /**
   * 域：0-机场，3-飞机
   */
  domain?: number;

  /**
   * 权限类型
   */
  permissionType?: string;

  /**
   * 共享状态：0-未授权，1-已授权，2-已撤销
   */
  shareStatus?: string;

  /**
   * 被授权方租户ID（用于共享出去的设备）
   */
  targetTenantId?: string | number;

  /**
   * 被授权方租户名称（用于共享出去的设备，前端处理后添加）
   */
  targetTenantName?: string;

  /**
   * 拥有者租户ID（用于共享给我的设备）
   */
  ownerTenantId?: string | number;

  /**
   * 拥有者租户名称（用于共享给我的设备，前端处理后添加）
   */
  ownerTenantName?: string;

  /**
   * 共享开始时间
   */
  shareStartTime?: string;

  /**
   * 共享结束时间
   */
  shareEndTime?: string;

  /**
   * 共享备注
   */
  shareRemark?: string;

  /**
   * 授权共享权限ID
   */
  sharePermissionId?: string | number;
}

export interface FlightDeviceDetailStatVO {
  /**
   * 设备ID
   */
  deviceId: number;

  /**
   * 设备名称
   */
  deviceName: string;

  /**
   * 设备状态
   */
  status: string;

  /**
   * 控制设备名称
   */
  controller: string;

  /**
   * 电量百分比 (0-100)
   */
  batteryLevel: number;

  /**
   * 今日飞行次数
   */
  todayFlightCount: number;

  /**
   * 今日飞行里程 (公里)
   */
  todayDistance: number;

  /**
   * 今日飞行时长 (小时)
   */
  todayDuration: number;

  /**
   * 累计飞行次数
   */
  totalFlightCount: number;

  /**
   * 累计飞行里程 (公里)
   */
  totalDistance: number;

  /**
   * 累计飞行时长 (小时)
   */
  totalDuration: number;
}

export interface FlightDeviceForm extends BaseEntity {
  /**
   * 主键ID
   */
  id?: string | number;

  /**
   * 飞行设备名称
   */
  deviceName?: string;

  /**
   * 设备状态
   */
  status?: string;

  /**
   * 控制设备名称
   */
  controller?: string;

  /**
   * 设备归属部门ID
   */
  deptId?: string | number;
}

export interface FlightDeviceQuery extends PageQuery {
  /**
   * 飞行设备名称
   */
  deviceName?: string;

  /**
   * 设备状态
   */
  status?: string;

  /**
   * 控制设备名称
   */
  controller?: string;

  /**
   * 部门ID
   */
  deptId?: string | number;

  /**
   * 域ID：0-机场，3-飞机
   */
  domain?: number;

  /**
   * 租户ID
   */
  tenantId?: string | number;

  /**
   * 日期范围参数
   */
  params?: any;
}
