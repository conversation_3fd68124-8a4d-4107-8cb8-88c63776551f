import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { FlightDeviceVO, FlightDeviceForm, FlightDeviceQuery, FlightDeviceDetailStatVO } from '@/api/biz/flightDevice/types';

/**
 * 飞行设备统计
 * @param query
 * @returns {*}
 */

export const flightDeviceStatistics = (query?: FlightDeviceQuery): AxiosPromise<FlightDeviceVO[]> => {
  return request({
    url: '/biz/flightDevice/statistics',
    method: 'get',
    params: query
  });
};

/**
 * 查询飞行设备列表
 * @param query
 * @returns {*}
 */

export const listFlightDevice = (query?: FlightDeviceQuery): AxiosPromise<FlightDeviceVO[]> => {
  return request({
    url: '/biz/flightDevice/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询共享出去的设备列表
 * @param query
 * @returns {*}
 */
export const listSharedOutDevices = (query?: FlightDeviceQuery): AxiosPromise<FlightDeviceVO[]> => {
  return request({
    url: '/biz/flightDevice/sharedOut',
    method: 'get',
    params: query
  });
};

/**
 * 查询共享给我的设备列表
 * @param query
 * @returns {*}
 */
export const listSharedInDevices = (query?: FlightDeviceQuery): AxiosPromise<FlightDeviceVO[]> => {
  return request({
    url: '/biz/flightDevice/sharedIn',
    method: 'get',
    params: query
  });
};

/**
 * 按类型查询设备列表
 * @param type 设备类型: own-自己的设备, shared_out-共享出去的设备, shared_in-共享进来的设备
 * @param query
 * @returns {*}
 */
export const listDevicesByType = (type: string, query?: FlightDeviceQuery): AxiosPromise<FlightDeviceVO[]> => {
  return request({
    url: '/biz/flightDevice/listByType',
    method: 'get',
    params: {
      type,
      ...query
    }
  });
};

/**
 * 查询飞行设备详细
 * @param id
 */
export const getFlightDevice = (id: string | number): AxiosPromise<FlightDeviceVO> => {
  return request({
    url: '/biz/flightDevice/' + id,
    method: 'get'
  });
};

/**
 * 新增飞行设备
 * @param data
 */
export const addFlightDevice = (data: FlightDeviceForm) => {
  return request({
    url: '/biz/flightDevice',
    method: 'post',
    data: data
  });
};

/**
 * 修改飞行设备
 * @param data
 */
export const updateFlightDevice = (data: FlightDeviceForm) => {
  return request({
    url: '/biz/flightDevice',
    method: 'put',
    data: data
  });
};

/**
 * 删除飞行设备
 * @param id
 */
export const delFlightDevice = (id: string | number | Array<string | number>) => {
  return request({
    url: '/biz/flightDevice/' + id,
    method: 'delete'
  });
};

/**
 * 获取指定设备的详细统计信息
 * @param deviceId 设备ID
 */
export const getFlightDeviceDetailStatistics = (deviceId: string | number): AxiosPromise<FlightDeviceDetailStatVO> => {
  return request({
    url: '/biz/flightDevice/detailStatistics/' + deviceId,
    method: 'get'
  });
};

/**
 * 查询飞行设备详情统计（Dashboard专用）
 * @param deviceId
 * @returns {*}
 */
export const getDashboardFlightDeviceDetailStatistics = (deviceId: string | number): AxiosPromise<FlightDeviceDetailStatVO> => {
  return request({
    url: '/biz/flightDevice/dashboard/detailStatistics/' + deviceId,
    method: 'get'
  });
};

/**
 * 获取部门树列表（用于设备管理页面的部门分组）
 * @param dept
 */
export const deptTree = (dept?: any) => {
  return request({
    url: '/biz/flightDevice/deptTree',
    method: 'get',
    params: dept
  });
};
