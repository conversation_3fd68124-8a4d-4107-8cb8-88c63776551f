import { BaseEntity } from '@/api/types';

export interface FlightDeviceSharePermissionVO {
  /**
   * 主键ID
   */
  id: string | number;

  /**
   * 资源拥有者租户ID
   */
  ownerTenantId: string | number;

  /**
   * 资源被授权方租户ID
   */
  targetTenantId: string | number;

  /**
   * 设备ID
   */
  deviceId: string | number;

  /**
   * 授权类型字典(1:只读,2:控制,3:完全控制)
   */
  permissionType: string;

  /**
   * 授权状态字典(1:有效,2:无效,3:已过期)
   */
  status: string;

  /**
   * 授权开始时间
   */
  startTime: string;

  /**
   * 授权结束时间
   */
  endTime: string;

  /**
   * 备注信息
   */
  remark: string;

  /**
   * 资源拥有者租户公司名（可选，前端用来显示）
   */
  ownerTenantCompanyName?: string;

  /**
   * 资源被授权方租户公司名（可选，前端用来显示）
   */
  targetTenantCompanyName?: string;
}

export interface FlightDeviceSharePermissionForm extends BaseEntity {
  /**
   * 主键ID
   */
  id?: string | number;

  /**
   * 资源拥有者租户ID
   */
  ownerTenantId?: string | number;

  /**
   * 资源被授权方租户ID
   */
  targetTenantId?: string | number;

  /**
   * 设备ID
   */
  deviceId?: string | number;

  /**
   * 授权类型字典(1:只读,2:控制,3:完全控制)
   */
  permissionType?: string;

  /**
   * 授权状态字典(1:有效,2:无效,3:已过期)
   */
  status?: string;

  /**
   * 授权开始时间
   */
  startTime?: string;

  /**
   * 授权结束时间
   */
  endTime?: string;

  /**
   * 备注信息
   */
  remark?: string;

  ownerTenantCompanyName?: string;
  targetTenantCompanyName?: string;

  targetTenantIds?: string[]; // 批量授权目标租户ID列表
}

export interface FlightDeviceSharePermissionQuery extends BaseEntity {
  /**
   * 资源拥有者租户ID
   */
  ownerTenantId?: string;

  /**
   * 资源被授权方租户ID
   */
  targetTenantId?: string;

  /**
   * 设备ID
   */
  deviceId?: string | number;

  /**
   * 授权类型字典(1:只读,2:控制,3:完全控制)
   */
  permissionType?: string;

  /**
   * 授权状态字典(1:有效,2:无效,3:已过期)
   */
  status?: string;

  /**
   * 授权开始时间
   */
  startTime?: Date;

  /**
   * 授权结束时间
   */
  endTime?: Date;

  pageNum: number;
  pageSize: number;
}
