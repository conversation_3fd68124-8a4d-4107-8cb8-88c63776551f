import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import {
  FlightDeviceSharePermissionVO,
  FlightDeviceSharePermissionForm,
  FlightDeviceSharePermissionQuery
} from '@/api/biz/flightDeviceSharePermission/types';

/**
 * 查询飞行设备授权共享列表
 * @param query
 * @returns {*}
 */

export const listFlightDeviceSharePermission = (query?: FlightDeviceSharePermissionQuery): AxiosPromise<FlightDeviceSharePermissionVO[]> => {
  return request({
    url: '/biz/flightDeviceSharePermission/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询飞行设备授权共享详细
 * @param id
 */
export const getFlightDeviceSharePermission = (id: string | number): AxiosPromise<FlightDeviceSharePermissionVO> => {
  return request({
    url: '/biz/flightDeviceSharePermission/' + id,
    method: 'get'
  });
};

/**
 * 新增飞行设备授权共享
 * @param data
 */
export const addFlightDeviceSharePermission = (data: FlightDeviceSharePermissionForm) => {
  return request({
    url: '/biz/flightDeviceSharePermission',
    method: 'post',
    data: data
  });
};

/**
 * 修改飞行设备授权共享
 * @param data
 */
export const updateFlightDeviceSharePermission = (data: FlightDeviceSharePermissionForm) => {
  return request({
    url: '/biz/flightDeviceSharePermission',
    method: 'put',
    data: data
  });
};

/**
 * 删除飞行设备授权共享
 * @param id
 */
export const delFlightDeviceSharePermission = (id: string | number | Array<string | number>) => {
  return request({
    url: '/biz/flightDeviceSharePermission/' + id,
    method: 'delete'
  });
};

/**
 * 批量授权飞行设备共享
 * @param data
 */
export const batchGrantFlightDeviceSharePermission = (data: FlightDeviceSharePermissionForm) => {
  return request({
    url: '/biz/flightDeviceSharePermission/batchGrant',
    method: 'post',
    data: data
  });
};

/**
 * 查询设备的所有授权信息
 * @param deviceId
 */
export const listDeviceSharePermissions = (deviceId: string | number): AxiosPromise<FlightDeviceSharePermissionVO[]> => {
  return request({
    url: '/biz/flightDeviceSharePermission/list',
    method: 'get',
    params: {
      deviceId
    }
  });
};
