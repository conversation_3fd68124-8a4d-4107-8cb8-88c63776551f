import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import {
  RoadInspectionDiseaseInfoVO,
  RoadInspectionDiseaseInfoForm,
  RoadInspectionDiseaseInfoQuery
} from '@/api/biz/roadInspectionDiseaseInfo/types';
import { BizRoadInspectionDiseaseStatisticsVo } from '@/api/biz/roadInspectionDiseaseInfo/types';
import { FlightTaskResultQuery, FlightTaskResultVO } from '@/api/biz/flightTaskResult/types';

/**
 * 查询公路巡检病害信息列表
 * @param query
 * @returns {*}
 */

export const listRoadInspectionDiseaseInfo = (query?: RoadInspectionDiseaseInfoQuery): AxiosPromise<RoadInspectionDiseaseInfoVO[]> => {
  return request({
    url: '/biz/roadInspectionDiseaseInfo/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询公路巡检病害信息详细
 * @param id
 * @returns {*}
 */
export const getRoadInspectionDiseaseInfo = (id: string | number): AxiosPromise<RoadInspectionDiseaseInfoVO> => {
  return request({
    url: '/biz/roadInspectionDiseaseInfo/' + id,
    method: 'get'
  });
};

/**
 * 新增公路巡检病害信息
 * @param data
 * @returns {*}
 */
export const addRoadInspectionDiseaseInfo = (data: RoadInspectionDiseaseInfoForm) => {
  return request({
    url: '/biz/roadInspectionDiseaseInfo',
    method: 'post',
    data: data
  });
};

/**
 * 修改公路巡检病害信息
 * @param data
 * @returns {*}
 */
export const updateRoadInspectionDiseaseInfo = (data: RoadInspectionDiseaseInfoForm) => {
  return request({
    url: '/biz/roadInspectionDiseaseInfo',
    method: 'put',
    data: data
  });
};

/**
 * 删除公路巡检病害信息
 * @param id
 * @returns {*}
 */
export const delRoadInspectionDiseaseInfo = (id: string | Array<string | number>) => {
  return request({
    url: '/biz/roadInspectionDiseaseInfo/' + id,
    method: 'delete'
  });
};

/**
 * 查询公路巡检病害统计信息
 * @param taskResultId
 * @returns {*}
 */
export const getDiseaseStatistics = (taskResultId?: string | number): AxiosPromise<BizRoadInspectionDiseaseStatisticsVo[]> => {
  return request({
    url: '/biz/roadInspectionDiseaseInfo/statistics',
    method: 'get',
    params: { taskResultId }
  });
};

/**
 * 查询公路智能巡检病害信息列表
 * 查询公路巡检病害信息列表
 * @param query
 * @returns {*}
 */

export const listDiseaseFlightTaskResult = (query?: FlightTaskResultQuery): AxiosPromise<FlightTaskResultVO[]> => {
  return request({
    url: '/biz/roadInspectionDiseaseInfo/listTask',
    method: 'get',
    params: query
  });
};
