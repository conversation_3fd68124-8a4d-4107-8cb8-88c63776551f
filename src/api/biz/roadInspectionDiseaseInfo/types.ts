export interface RoadInspectionDiseaseInfoVO {
  /**
   * 病害信息ID，主键
   */
  id: string | number;

  /**
   * 关联的巡检任务结果ID
   */
  taskResultId: string | number;

  /**
   * 病害类型
   */
  diseaseType: string;

  /**
   * 纬度
   */
  latitude: number;

  /**
   * 经度
   */
  longitude: number;

  /**
   * 图片URL
   */
  imageUrl: string;

  /**
   * 图片URLUrl
   */
  imageUrlUrl: string;

  /**
   * 置信度，范围0-100%
   */
  confidence: string | number;

  /**
   * 病害是否确认
   */
  diseaseConfirmed: string;
}

export interface RoadInspectionDiseaseInfoForm extends BaseEntity {
  /**
   * 病害信息ID，主键
   */
  id?: string | number;

  /**
   * 关联的巡检任务结果ID
   */
  taskResultId?: string | number;

  /**
   * 病害类型
   */
  diseaseType?: string;

  /**
   * 纬度
   */
  latitude?: number;

  /**
   * 经度
   */
  longitude?: number;

  /**
   * 图片URL
   */
  imageUrl?: string;

  /**
   * 置信度，范围0-100%
   */
  confidence?: string | number;

  /**
   * 病害是否确认
   */
  diseaseConfirmed?: string;
}

export interface RoadInspectionDiseaseInfoQuery extends PageQuery {
  /**
   * 关联的巡检任务结果ID
   */
  taskResultId?: string | number;

  /**
   * 病害类型
   */
  diseaseType?: string;

  /**
   * 纬度
   */
  latitude?: number;

  /**
   * 经度
   */
  longitude?: number;

  /**
   * 图片URL
   */
  imageUrl?: string;

  /**
   * 置信度，范围0-100%
   */
  confidence?: string | number;

  /**
   * 病害是否确认
   */
  diseaseConfirmed?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}

/**
 * 病害统计信息VO
 */
export interface BizRoadInspectionDiseaseStatisticsVo {
  /**
   * 病害类型
   */
  diseaseType: string;

  /**
   * 病害数量
   */
  count: number;
}
