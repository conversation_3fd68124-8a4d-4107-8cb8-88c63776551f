import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { DroneDeviceVO, DroneDeviceForm, DroneDeviceQuery, DroneDeviceDetailStatVO } from '@/api/biz/droneDevice/types';

/**
 * 查询无人机设备列表
 * @param query
 * @returns {*}
 */

export const listDroneDevice = (query?: DroneDeviceQuery): AxiosPromise<DroneDeviceVO[]> => {
  return request({
    url: '/biz/droneDevice/list',
    method: 'get',
    params: query
  });
};

/**
 * 管理员查询无人机设备列表（不受数据权限限制）
 * @param query
 * @returns {*}
 */
export const adminListDroneDevice = (query?: DroneDeviceQuery): AxiosPromise<DroneDeviceVO[]> => {
  return request({
    url: '/biz/droneDevice/admin/list',
    method: 'get',
    params: query
  });
};

/**
 * 大屏专属查询无人机设备列表（管理员查询所有，普通用户查询权限范围内）
 * @param query
 * @returns {*}
 */
export const dashboardListDroneDevice = (query?: DroneDeviceQuery): AxiosPromise<DroneDeviceVO[]> => {
  return request({
    url: '/biz/droneDevice/dashboard/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询无人机设备详细
 * @param id
 */
export const getDroneDevice = (id: string | number): AxiosPromise<DroneDeviceVO> => {
  return request({
    url: '/biz/droneDevice/' + id,
    method: 'get'
  });
};

/**
 * 查询无人机设备详情统计
 * @param id
 */
export const getDroneDeviceDetailStatistics = (id: string | number): AxiosPromise<DroneDeviceDetailStatVO> => {
  return request({
    url: '/biz/droneDevice/detailStatistics/' + id,
    method: 'get'
  });
};

/**
 * 查询无人机设备详情统计（Dashboard专用）
 * @param deviceId
 * @returns {*}
 */
export const getDashboardDroneDeviceDetailStatistics = (deviceId: string | number): AxiosPromise<DroneDeviceDetailStatVO> => {
  return request({
    url: '/biz/droneDevice/dashboard/detailStatistics/' + deviceId,
    method: 'get'
  });
};

/**
 * 新增无人机设备
 * @param data
 */
export const addDroneDevice = (data: DroneDeviceForm) => {
  return request({
    url: '/biz/droneDevice',
    method: 'post',
    data: data
  });
};

/**
 * 修改无人机设备
 * @param data
 */
export const updateDroneDevice = (data: DroneDeviceForm) => {
  return request({
    url: '/biz/droneDevice',
    method: 'put',
    data: data
  });
};

/**
 * 删除无人机设备
 * @param id
 */
export const delDroneDevice = (id: string | number | Array<string | number>) => {
  return request({
    url: '/biz/droneDevice/' + id,
    method: 'delete'
  });
};

/**
 * 获取部门树列表（用于设备管理页面的部门分组）
 * @param dept
 */
export const deptTree = (dept?: any) => {
  return request({
    url: '/biz/droneDevice/deptTree',
    method: 'get',
    params: dept
  });
};
