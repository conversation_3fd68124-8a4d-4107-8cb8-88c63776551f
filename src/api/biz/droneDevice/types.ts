export interface DroneDeviceVO {
  /**
   * 主键ID
   */
  id: string | number;

  /**
   * 备注
   */
  remark: string;

  /**
   * 设备归属部门ID
   */
  deptId: string | number;

  /**
   * 关联机巢ID
   */
  dockId: string | number;

  /**
   * 设备序列号（飞控平台唯一标识）
   */
  deviceSn: string;

  /**
   * 设备名称
   */
  deviceName: string;

  /**
   * 设备型号
   */
  deviceType: string;

  /**
   * 设备昵称
   */
  nickname: string;
}

export interface DroneDeviceForm extends BaseEntity {
  /**
   * 主键ID
   */
  id?: string | number;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 设备归属部门ID
   */
  deptId?: string | number;

  /**
   * 关联机巢ID
   */
  dockId?: string | number;

  /**
   * 设备序列号（飞控平台唯一标识）
   */
  deviceSn?: string;

  /**
   * 设备名称
   */
  deviceName?: string;

  /**
   * 设备型号
   */
  deviceType?: string;
}

export interface DroneDeviceQuery extends PageQuery {
  /**
   * 设备归属部门ID
   */
  deptId?: string | number;

  /**
   * 关联机巢ID
   */
  dockId?: string | number;

  /**
   * 设备序列号（飞控平台唯一标识）
   */
  deviceSn?: string;

  /**
   * 设备名称
   */
  deviceName?: string;

  /**
   * 设备型号
   */
  deviceType?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}

export interface DroneDeviceDetailStatVO {
  /**
   * 设备ID
   */
  deviceId: number;

  /**
   * 设备名称
   */
  deviceName: string;

  /**
   * 设备状态
   */
  status: string;

  /**
   * 控制设备名称
   */
  controller: string;

  /**
   * 电量百分比 (0-100)
   */
  batteryLevel: number;

  /**
   * 今日飞行次数
   */
  todayFlightCount: number;

  /**
   * 今日飞行里程 (公里)
   */
  todayDistance: number;

  /**
   * 今日飞行时长 (小时)
   */
  todayDuration: number;

  /**
   * 累计飞行次数
   */
  totalFlightCount: number;

  /**
   * 累计飞行里程 (公里)
   */
  totalDistance: number;

  /**
   * 累计飞行时长 (小时)
   */
  totalDuration: number;
}
