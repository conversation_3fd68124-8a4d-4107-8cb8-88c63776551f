export interface FlightDeviceUseApplicationVO {
  /**
   * 主键 ID（雪花/序列）
   */
  id: string | number;

  /**
   * 申请使用的设备 ID，对应 biz_flight_device.id
   */
  deviceId: string | number;

  /**
   * 设备拥有方租户 ID（房东）
   */
  ownerTenantId: string | number;

  /**
   * 被授权 / 申请方租户 ID（房客）
   */
  targetTenantId: string | number;

  /**
   * 申请说明或备注
   */
  applyReason: string;

  /**
   * 申请占用开始时间
   */
  applyStartTime: string;

  /**
   * 申请占用结束时间
   */
  applyEndTime: string;

  /**
   * 审批状态：0=待审批 1=已通过 2=已拒绝
   */
  approveStatus: string;

  /**
   * 审批意见/备注
   */
  approveOpinion: string;

  /**
   * 审批人用户 ID
   */
  approveBy: number;

  /**
   * 审批通过/拒绝时间
   */
  approveTime: string;

  /**
   * 设备名称（前端扩展字段）
   */
  deviceName?: string;

  /**
   * 设备拥有方租户名称（前端扩展字段）
   */
  ownerTenantName?: string;

  /**
   * 申请方租户名称（前端扩展字段）
   */
  targetTenantName?: string;
}

export interface FlightDeviceUseApplicationForm extends BaseEntity {
  /**
   * 主键 ID（雪花/序列）
   */
  id?: string | number;

  /**
   * 申请使用的设备 ID，对应 biz_flight_device.id
   */
  deviceId?: string | number;

  /**
   * 设备拥有方租户 ID（房东）
   */
  ownerTenantId?: string | number;

  /**
   * 被授权 / 申请方租户 ID（房客）
   */
  targetTenantId?: string | number;

  /**
   * 申请说明或备注
   */
  applyReason?: string;

  /**
   * 申请占用开始时间
   */
  applyStartTime?: string;

  /**
   * 申请占用结束时间
   */
  applyEndTime?: string;

  /**
   * 审批状态：0=待审批 1=已通过 2=已拒绝
   */
  approveStatus?: string;

  /**
   * 审批意见/备注
   */
  approveOpinion?: string;
}

export interface FlightDeviceUseApplicationQuery extends PageQuery {
  /**
   * 主键 ID（用于精确查找特定申请）
   */
  id?: string | number;

  /**
   * 申请使用的设备 ID，对应 biz_flight_device.id
   */
  deviceId?: string | number;

  /**
   * 设备拥有方租户 ID（房东）
   */
  ownerTenantId?: string | number;

  /**
   * 被授权 / 申请方租户 ID（房客）
   */
  targetTenantId?: string | number;

  /**
   * 申请说明或备注
   */
  applyReason?: string;

  /**
   * 申请占用开始时间
   */
  applyStartTime?: string;

  /**
   * 申请占用结束时间
   */
  applyEndTime?: string;

  /**
   * 审批状态：0=待审批 1=已通过 2=已拒绝
   */
  approveStatus?: string;

  /**
   * 审批意见/备注
   */
  approveOpinion?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
