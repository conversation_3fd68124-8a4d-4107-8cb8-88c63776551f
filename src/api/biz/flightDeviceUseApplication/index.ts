import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import {
  FlightDeviceUseApplicationVO,
  FlightDeviceUseApplicationForm,
  FlightDeviceUseApplicationQuery
} from '@/api/biz/flightDeviceUseApplication/types';

/**
 * 查询设备使用申请（AUTHORIZED_SHARED）：记录租户对共享设备的使用申请及审批流程列表
 * @param query
 * @returns {*}
 */

export const listFlightDeviceUseApplication = (query?: FlightDeviceUseApplicationQuery): AxiosPromise<FlightDeviceUseApplicationVO[]> => {
  return request({
    url: '/biz/flightDeviceUseApplication/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询设备使用申请（AUTHORIZED_SHARED）：记录租户对共享设备的使用申请及审批流程详细
 * @param id
 */
export const getFlightDeviceUseApplication = (id: string | number): AxiosPromise<FlightDeviceUseApplicationVO> => {
  return request({
    url: '/biz/flightDeviceUseApplication/' + id,
    method: 'get'
  });
};

/**
 * 新增设备使用申请（AUTHORIZED_SHARED）：记录租户对共享设备的使用申请及审批流程
 * @param data
 */
export const addFlightDeviceUseApplication = (data: FlightDeviceUseApplicationForm) => {
  return request({
    url: '/biz/flightDeviceUseApplication',
    method: 'post',
    data: data
  });
};

/**
 * 修改设备使用申请（AUTHORIZED_SHARED）：记录租户对共享设备的使用申请及审批流程
 * @param data
 */
export const updateFlightDeviceUseApplication = (data: FlightDeviceUseApplicationForm) => {
  return request({
    url: '/biz/flightDeviceUseApplication',
    method: 'put',
    data: data
  });
};

/**
 * 删除设备使用申请（AUTHORIZED_SHARED）：记录租户对共享设备的使用申请及审批流程
 * @param id
 */
export const delFlightDeviceUseApplication = (id: string | number | Array<string | number>) => {
  return request({
    url: '/biz/flightDeviceUseApplication/' + id,
    method: 'delete'
  });
};

/**
 * 查询我发出的申请列表
 * @param query
 * @returns {*}
 */
export const listMyApplications = (query?: FlightDeviceUseApplicationQuery): AxiosPromise<FlightDeviceUseApplicationVO[]> => {
  return request({
    url: '/biz/flightDeviceUseApplication/myApplications',
    method: 'get',
    params: query
  });
};

/**
 * 查询需要我审批的申请列表
 * @param query
 * @returns {*}
 */
export const listPendingApprovals = (query?: FlightDeviceUseApplicationQuery): AxiosPromise<FlightDeviceUseApplicationVO[]> => {
  return request({
    url: '/biz/flightDeviceUseApplication/pendingApprovals',
    method: 'get',
    params: query
  });
};

/**
 * 查询已通过的申请列表
 * @param query
 * @returns {*}
 */
export const listApprovedApplications = (query?: FlightDeviceUseApplicationQuery): AxiosPromise<FlightDeviceUseApplicationVO[]> => {
  return request({
    url: '/biz/flightDeviceUseApplication/approvedApplications',
    method: 'get',
    params: query
  });
};
