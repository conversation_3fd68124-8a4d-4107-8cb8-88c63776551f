import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { FlightTaskInfoVO, FlightTaskInfoForm, FlightTaskInfoQuery } from '@/api/biz/flightTaskInfo/types';

/**
 * 查询飞行任务计划管理列表
 * @param query
 * @returns {*}
 */

export const listFlightTaskInfo = (query?: FlightTaskInfoQuery): AxiosPromise<FlightTaskInfoVO[]> => {
  return request({
    url: '/biz/flightTaskInfo/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询飞行任务计划管理详细
 * @param id
 */
export const getFlightTaskInfo = (id: string | number): AxiosPromise<FlightTaskInfoVO> => {
  return request({
    url: '/biz/flightTaskInfo/' + id,
    method: 'get'
  });
};

/**
 * 新增飞行任务计划管理
 * @param data
 */
export const addFlightTaskInfo = (data: FlightTaskInfoForm) => {
  return request({
    url: '/biz/flightTaskInfo',
    method: 'post',
    data: data
  });
};

/**
 * 新增飞行任务计划管理（支持飞控同步）
 * @param data
 */
export const createFlightTaskWithFlightControl = (data: FlightTaskInfoForm) => {
  return request({
    url: '/biz/flightTaskInfo/createWithFlightControl',
    method: 'post',
    data: data
  });
};

/**
 * 修改飞行任务计划管理
 * @param data
 */
export const updateFlightTaskInfo = (data: FlightTaskInfoForm) => {
  return request({
    url: '/biz/flightTaskInfo',
    method: 'put',
    data: data
  });
};

/**
 * 删除飞行任务计划管理
 * @param id
 */
export const delFlightTaskInfo = (id: string | number | Array<string | number>) => {
  return request({
    url: '/biz/flightTaskInfo/' + id,
    method: 'delete'
  });
};

/**
 * 查询我发起的任务列表
 * @param query
 * @returns {*}
 */
export const listMyTasks = (query?: FlightTaskInfoQuery): AxiosPromise<FlightTaskInfoVO[]> => {
  return request({
    url: '/biz/flightTaskInfo/myTasks',
    method: 'get',
    params: query
  });
};

/**
 * 查询分配给我的任务列表（我拥有的设备被其他租户申请使用的任务）
 * @param query
 * @returns {*}
 */
export const listAssignedTasks = (query?: FlightTaskInfoQuery): AxiosPromise<FlightTaskInfoVO[]> => {
  return request({
    url: '/biz/flightTaskInfo/assignedTasks',
    method: 'get',
    params: query
  });
};

/**
 * 应急飞行到指定点
 * @param deviceSn 设备序列号
 * @param params 飞行参数
 */
export const takeoffToPoint = (deviceSn: string, params: {
  targetType: number;
  projectCode?: string;
  pileNo?: string;
  targetLongitude?: number;
  targetLatitude?: number;
}) => {
  return request({
    url: `/biz/flightTaskInfo/takeoffToPoint/${deviceSn}`,
    method: 'post',
    params: params
  });
};
