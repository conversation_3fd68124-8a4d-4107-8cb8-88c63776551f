export interface FlightTaskInfoVO {
  /**
   * 任务ID，主键
   */
  id: string | number;

  /**
   * 任务名称
   */
  taskName: string;

  /**
   * 任务编号
   */
  taskNo: string;

  /**
   * 任务类型
   */
  taskType: string;

  /**
   * 任务频率
   */
  taskFrequency: string;

  /**
   * 任务计划开始时间
   */
  plannedStartTime: string;

  /**
   * 任务计划结束时间
   */
  plannedEndTime: string;

  /**
   * 备注
   */
  notes: string;

  /**
   * 返航高度
   */
  returnAltitude: number;

  /**
   * 矢量操作
   */
  vectorOperation: string;

  /**
   * 设备拥有方租户ID（房东）
   */
  ownerTenantId?: string;

  /**
   * 任务执行方租户ID（房客）
   */
  targetTenantId?: string;

  /**
   * 关联的设备使用申请ID
   */
  useApplicationId?: string | number;

  /**
   * 执行设备名称（联表查询字段）
   */
  executionDeviceName?: string;

  /**
   * 执行航线名称（联表查询字段）
   */
  routeName?: string;

  /**
   * 设备拥有方租户名称（前端处理字段）
   */
  ownerTenantName?: string;

  /**
   * 任务执行方租户名称（前端处理字段）
   */
  targetTenantName?: string;
}

export interface FlightTaskInfoForm extends BaseEntity {
  /**
   * 任务ID，主键
   */
  id?: string | number;

  /**
   * 任务名称
   */
  taskName?: string;

  /**
   * 任务编号
   */
  taskNo?: string;

  /**
   * 任务类型
   */
  taskType?: string;

  /**
   * 任务频率
   */
  taskFrequency?: string;

  /**
   * 任务计划开始时间
   */
  plannedStartTime?: string;

  /**
   * 任务计划结束时间
   */
  plannedEndTime?: string;

  /**
   * 每天定时开始时间
   */
  dailyScheduledStartTime?: string;

  /**
   * 每天定时结束时间
   */
  dailyScheduledEndTime?: string;

  /**
   * 电池容量达到
   */
  batteryCapacityReached?: number;

  /**
   * 储存容量达到
   */
  storageCapacityReached?: number;

  /**
   * 执行航线id
   */
  routeId?: string | number;

  /**
   * 执行设备id
   */
  executionDeviceId?: string | number;

  /**
   * 执行人员
   */
  executor?: string;

  /**
   * 预计载重（0-40kg）
   */
  estimatedPayload?: number;

  /**
   * 备注
   */
  notes?: string;

  /**
   * 任务状态
   */
  taskStatus?: string;

  /**
   * 实际开始时间
   */
  actualStartTime?: string;

  /**
   * 实际结束时间
   */
  actualEndTime?: string;

  /**
   * 返航高度
   */
  returnAltitude?: number;

  /**
   * 矢量操作
   */
  vectorOperation?: string;

  /**
   * 设备拥有方租户ID（房东）
   */
  ownerTenantId?: string;

  /**
   * 任务执行方租户ID（房客）
   */
  targetTenantId?: string;

  /**
   * 关联的设备使用申请ID
   */
  useApplicationId?: string | number;
}

export interface FlightTaskInfoQuery extends PageQuery {
  /**
   * 任务名称
   */
  taskName?: string;

  /**
   * 任务编号
   */
  taskNo?: string;

  /**
   * 任务类型
   */
  taskType?: string;

  /**
   * 任务频率
   */
  taskFrequency?: string;

  /**
   * 任务计划开始时间
   */
  plannedStartTime?: string;

  /**
   * 任务计划结束时间
   */
  plannedEndTime?: string;

  /**
   * 每天定时开始时间
   */
  dailyScheduledStartTime?: string;

  /**
   * 每天定时结束时间
   */
  dailyScheduledEndTime?: string;

  /**
   * 电池容量达到
   */
  batteryCapacityReached?: number;

  /**
   * 储存容量达到
   */
  storageCapacityReached?: number;

  /**
   * 执行航线id
   */
  routeId?: string | number;

  /**
   * 执行设备id
   */
  executionDeviceId?: string | number;

  /**
   * 执行人员
   */
  executor?: string;

  /**
   * 预计载重（0-40kg）
   */
  estimatedPayload?: number;

  /**
   * 备注
   */
  notes?: string;

  /**
   * 任务状态
   */
  taskStatus?: string;

  /**
   * 实际开始时间
   */
  actualStartTime?: string;

  /**
   * 实际结束时间
   */
  actualEndTime?: string;

  /**
   * 返航高度
   */
  returnAltitude?: number;

  /**
   * 矢量操作
   */
  vectorOperation?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
