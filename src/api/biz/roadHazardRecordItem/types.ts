export interface RoadHazardRecordItemVO {
  /**
   * 条目ID，主键
   */
  id: string | number;

  /**
   * 关联的隐患排查记录ID
   */
  inspectRecordId: string | number;

  /**
   * 关联的隐患点ID
   */
  hazardPointId: string | number;

  /**
   * 拍摄点纬度
   */
  latitude: number;

  /**
   * 拍摄点经度
   */
  longitude: number;

  /**
   * 桩号
   */
  pileNo: string;

  /**
   * 路线编号
   */
  roadNum: string;

  /**
   * 是否存在风险：Y=是 N=否
   */
  hasRisk: string;

  /**
   * 风险备注
   */
  riskNote: string;

  /**
   * AI识别置信度
   */
  confidence: string | number;

  /**
   * 识别结果是否确认
   */
  recognitionResultConfirmed: string;

  /**
   * 事件发生/图片拍摄时间
   */
  eventTime: string;

  /**
   * 隐患类型
   */
  hazardType: string;

  /**
   * 原图URL
   */
  imageUrl: string;

  /**
   * 原图OSS文件ID
   */
  ossId: number;

  /**
   * 压缩图URL
   */
  webpUrl: string;

  /**
   * 压缩图OSS文件ID
   */
  webpOssId: number;

}

export interface RoadHazardRecordItemForm extends BaseEntity {
  /**
   * 条目ID，主键
   */
  id?: string | number;

  /**
   * 关联的隐患排查记录ID
   */
  inspectRecordId?: string | number;

  /**
   * 关联的隐患点ID
   */
  hazardPointId?: string | number;

  /**
   * 拍摄点纬度
   */
  latitude?: number;

  /**
   * 拍摄点经度
   */
  longitude?: number;

  /**
   * 桩号
   */
  pileNo?: string;

  /**
   * 路线编号
   */
  roadNum?: string;

  /**
   * 是否存在风险：Y=是 N=否
   */
  hasRisk?: string;

  /**
   * 风险备注
   */
  riskNote?: string;

  /**
   * AI识别置信度
   */
  confidence?: string | number;

  /**
   * 识别结果是否确认
   */
  recognitionResultConfirmed?: string;

  /**
   * 事件发生/图片拍摄时间
   */
  eventTime?: string;

  /**
   * 隐患类型
   */
  hazardType?: string;

}

export interface RoadHazardRecordItemQuery extends PageQuery {

  /**
   * 关联的隐患排查记录ID
   */
  inspectRecordId?: string | number;

  /**
   * 关联的隐患点ID
   */
  hazardPointId?: string | number;

  /**
   * 拍摄点纬度
   */
  latitude?: number;

  /**
   * 拍摄点经度
   */
  longitude?: number;

  /**
   * 桩号
   */
  pileNo?: string;

  /**
   * 路线编号
   */
  roadNum?: string;

  /**
   * 是否存在风险：Y=是 N=否
   */
  hasRisk?: string;

  /**
   * 风险备注
   */
  riskNote?: string;

  /**
   * AI识别置信度
   */
  confidence?: string | number;

  /**
   * 识别结果是否确认
   */
  recognitionResultConfirmed?: string;

  /**
   * 事件发生/图片拍摄时间
   */
  eventTime?: string;

  /**
   * 隐患类型
   */
  hazardType?: string;

  /**
   * 事件时间查询开始时间
   */
  eventTimeStart?: string;

  /**
   * 事件时间查询结束时间
   */
  eventTimeEnd?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



