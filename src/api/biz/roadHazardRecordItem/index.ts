import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { RoadHazardRecordItemVO, RoadHazardRecordItemForm, RoadHazardRecordItemQuery } from '@/api/biz/roadHazardRecordItem/types';

/**
 * 查询公路隐患排查条目列表
 * @param query
 * @returns {*}
 */

export const listRoadHazardRecordItem = (query?: RoadHazardRecordItemQuery): AxiosPromise<RoadHazardRecordItemVO[]> => {
  return request({
    url: '/biz/roadHazardRecordItem/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询公路隐患排查条目详细
 * @param id
 */
export const getRoadHazardRecordItem = (id: string | number): AxiosPromise<RoadHazardRecordItemVO> => {
  return request({
    url: '/biz/roadHazardRecordItem/' + id,
    method: 'get'
  });
};

/**
 * 新增公路隐患排查条目
 * @param data
 */
export const addRoadHazardRecordItem = (data: RoadHazardRecordItemForm) => {
  return request({
    url: '/biz/roadHazardRecordItem',
    method: 'post',
    data: data
  });
};

/**
 * 修改公路隐患排查条目
 * @param data
 */
export const updateRoadHazardRecordItem = (data: RoadHazardRecordItemForm) => {
  return request({
    url: '/biz/roadHazardRecordItem',
    method: 'put',
    data: data
  });
};

/**
 * 删除公路隐患排查条目
 * @param id
 */
export const delRoadHazardRecordItem = (id: string | number | Array<string | number>) => {
  return request({
    url: '/biz/roadHazardRecordItem/' + id,
    method: 'delete'
  });
};
