import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { FlightRouteInfoVO, FlightRouteInfoForm, FlightRouteInfoQuery } from '@/api/biz/flightRouteInfo/types';


/**
 * 查询航线管理列表
 * @param query
 * @returns {*}
 */

export const listFlightRouteInfo = (query?: FlightRouteInfoQuery): AxiosPromise<FlightRouteInfoVO[]> => {
  return request({
    url: '/biz/flightRouteInfo/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询航线管理详细
 * @param id
 */
export const getFlightRouteInfo = (id: string | number): AxiosPromise<FlightRouteInfoVO> => {
  return request({
    url: '/biz/flightRouteInfo/' + id,
    method: 'get'
  });
};

/**
 * 新增航线管理
 * @param data
 */
export const addFlightRouteInfo = (data: FlightRouteInfoForm) => {
  return request({
    url: '/biz/flightRouteInfo',
    method: 'post',
    data: data
  });
};

/**
 * 修改航线管理
 * @param data
 */
export const updateFlightRouteInfo = (data: FlightRouteInfoForm) => {
  return request({
    url: '/biz/flightRouteInfo',
    method: 'put',
    data: data
  });
};

/**
 * 删除航线管理
 * @param id
 */
export const delFlightRouteInfo = (id: string | number | Array<string | number>) => {
  return request({
    url: '/biz/flightRouteInfo/' + id,
    method: 'delete'
  });
};
