export interface FlightRouteInfoVO {
  /**
   * 航线id: 主键
   */
  id: string | number;

  /**
   * 航线编号
   */
  routeNo: string;

  /**
   * 航线名称
   */
  routeName: string;

  /**
   * KML文件路径
   */
  kmlFileUrl: string;

  /**
   * 起点
   */
  startPoint: string;

  /**
   * 终点
   */
  endPoint: string;

  /**
   * 垂直起飞高度（米）
   */
  verticalTakeoffHeight: number;

  /**
   * 飞行高度（米）
   */
  flightAltitude: number;

  /**
   * 飞行速度（米/秒）
   */
  flightSpeed: number;

  /**
   * 任务动作
   */
  taskAction: string;

  /**
   * 总里程（公里）
   */
  totalDistance: number;

  /**
   * 飞行时长
   */
  flightDuration: string;

  /**
   * 路段名称
   */
  roadName: string;

  /**
   * 起止桩号
   */
  startEndPileNo: string;

  /**
   * 创建时间
   */
  createTime: string;

  /**
   * 更新时间
   */
  updateTime: string;

  /**
   * 备注
   */
  remark: string;

}

export interface FlightRouteInfoForm extends BaseEntity {
  /**
   * 航线id: 主键
   */
  id?: string | number;

  /**
   * 航线编号
   */
  routeNo?: string;

  /**
   * 航线名称
   */
  routeName?: string;

  /**
   * KML文件路径
   */
  kmlFileUrl?: string;

  /**
   * 起点
   */
  startPoint?: string;

  /**
   * 终点
   */
  endPoint?: string;

  /**
   * 垂直起飞高度（米）
   */
  verticalTakeoffHeight?: number;

  /**
   * 飞行高度（米）
   */
  flightAltitude?: number;

  /**
   * 飞行速度（米/秒）
   */
  flightSpeed?: number;

  /**
   * 任务动作
   */
  taskAction?: string;

  /**
   * 云台俯仰角（度）
   */
  gimbalPitchAngle?: number;

  /**
   * 云台平移角（度）
   */
  gimbalRollAngle?: number;

  /**
   * 总里程（公里）
   */
  totalDistance?: number;

  /**
   * 飞行时长
   */
  flightDuration?: string;

  /**
   * 路段名称
   */
  roadName?: string;

  /**
   * 起止桩号
   */
  startEndPileNo?: string;

  /**
   * 备注
   */
  remark?: string;
}

export interface FlightRouteInfoQuery extends PageQuery {

  /**
   * 航线编号
   */
  routeNo?: string;

  /**
   * 航线名称
   */
  routeName?: string;

  /**
   * KML文件路径
   */
  kmlFileUrl?: string;

  /**
   * 起点
   */
  startPoint?: string;

  /**
   * 终点
   */
  endPoint?: string;

  /**
   * 垂直起飞高度（米）
   */
  verticalTakeoffHeight?: number;

  /**
   * 飞行高度（米）
   */
  flightAltitude?: number;

  /**
   * 飞行速度（米/秒）
   */
  flightSpeed?: number;

  /**
   * 任务动作
   */
  taskAction?: string;

  /**
   * 云台俯仰角（度）
   */
  gimbalPitchAngle?: number;

  /**
   * 云台平移角（度）
   */
  gimbalRollAngle?: number;

  /**
   * 总里程（公里）
   */
  totalDistance?: number;

  /**
   * 飞行时长
   */
  flightDuration?: string;

  /**
   * 路段名称
   */
  roadName?: string;

  /**
   * 起止桩号
   */
  startEndPileNo?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



