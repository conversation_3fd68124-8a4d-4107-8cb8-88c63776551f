export interface RoadAiLabelInfoVO {
  /**
   * 图片ID，主键
   */
  id: string | number;

  /**
   * 标签类型
   */
  type: string;

  /**
   * 宽度
   */
  width: string | number;

  /**
   * 宽度
   */
  height: number;

  /**
   * x坐标
   */
  x: number;

  /**
   * y坐标
   */
  y: number;

  /**
   * 记录时间
   */
  recordTime: string;

  /**
   * 关联图片URL
   */
  imageUrl: string;

  /**
   * 标签id
   */
  labelId: string | number;

  /**
   * 置信度
   */
  confidence: string | number;

  /**
   * 图片名称
   */
  pictureName: string;

  /**
   * 图片宽度
   */
  imageWidth: string | number;

  /**
   * 图片高度
   */
  imageHeight: number;

  /**
   * 识别结果确认状态
   */
  diseaseConfirmed: string | number;
}

export interface RoadAiLabelInfoForm extends BaseEntity {
  /**
   * 图片ID，主键
   */
  id?: string | number;

  /**
   * 标签类型
   */
  type?: string;

  /**
   * 宽度
   */
  width?: string | number;

  /**
   * 宽度
   */
  height?: number;

  /**
   * x坐标
   */
  x?: number;

  /**
   * y坐标
   */
  y?: number;

  /**
   * 记录时间
   */
  recordTime?: string;

  /**
   * 关联图片URL
   */
  imageUrl?: string;

  /**
   * 标签id
   */
  labelId?: string | number;

  /**
   * 置信度
   */
  confidence?: string | number;

  /**
   * 图片名称
   */
  pictureName?: string;

  /**
   * 图片宽度
   */
  imageWidth?: string | number;

  /**
   * 图片高度
   */
  imageHeight?: number;

  /**
   * 识别结果确认状态
   */
  diseaseConfirmed: string | number;

}

export interface RoadAiLabelInfoQuery extends PageQuery {

  /**
   * 标签类型
   */
  type?: string;

  /**
   * 宽度
   */
  width?: string | number;

  /**
   * 宽度
   */
  height?: number;

  /**
   * x坐标
   */
  x?: number;

  /**
   * y坐标
   */
  y?: number;

  /**
   * 记录时间
   */
  recordTime?: string;

  /**
   * 关联图片URL
   */
  imageUrl?: string;

  /**
   * 标签id
   */
  labelId?: string | number;

  /**
   * 置信度
   */
  confidence?: string | number;

  /**
   * 图片名称
   */
  pictureName?: string;

  /**
   * 图片宽度
   */
  imageWidth?: string | number;

  /**
   * 图片高度
   */
  imageHeight?: number;

  /**
   * 日期范围参数
   */
  params?: any;
}


