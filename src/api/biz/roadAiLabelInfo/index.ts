import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { RoadAiLabelInfoVO, RoadAiLabelInfoForm, RoadAiLabelInfoQuery } from '@/api/biz/roadAiLabelInfo/types';

/**
 * 查询公路ai图片标签信息列表
 * @param query
 * @returns {*}
 */

export const listRoadAiLabelInfo = (query?: RoadAiLabelInfoQuery): AxiosPromise<RoadAiLabelInfoVO[]> => {
  return request({
    url: '/biz/roadAiLabelInfo/list',
    method: 'get',
    params: query
  });
};


// 根据图片URL获取标签列表
export const listLabelByImageUrl = (imageUrl: string, labelTypes: string): AxiosPromise<RoadAiLabelInfoVO[]> => {
  return request({
    url: '/biz/roadAiLabelInfo/listByImageUrl',
    method: 'post',
    data: { imageUrl, labelTypes }
  });
};

/**
 * 根据条件查询公路ai图片标签信息列表
 * @param query
 * @returns {*}
 */

export const listLabelInfoCondition = (query?: RoadAiLabelInfoQuery): AxiosPromise<RoadAiLabelInfoVO[]> => {
  return request({
    url: '/biz/roadAiLabelInfo/listByCondition',
    method: 'get',
    params: query
  });
};

/**
 * 查询公路ai图片标签信息详细
 * @param id
 */
export const getRoadAiLabelInfo = (id: string | number): AxiosPromise<RoadAiLabelInfoVO> => {
  return request({
    url: '/biz/roadAiLabelInfo/' + id,
    method: 'get'
  });
};

/**
 * 新增公路ai图片标签信息
 * @param data
 */
export const addRoadAiLabelInfo = (data: RoadAiLabelInfoForm) => {
  return request({
    url: '/biz/roadAiLabelInfo',
    method: 'post',
    data: data
  });
};

/**
 * 修改公路ai图片标签信息
 * @param data
 */
export const updateRoadAiLabelInfo = (data: RoadAiLabelInfoForm) => {
  return request({
    url: '/biz/roadAiLabelInfo',
    method: 'put',
    data: data
  });
};

/**
 * 删除公路ai图片标签信息
 * @param id
 */
export const delRoadAiLabelInfo = (id: string | number | Array<string | number>) => {
  return request({
    url: '/biz/roadAiLabelInfo/' + id,
    method: 'delete'
  });
};

/**
 * 根据主键Id修改识别结果状态
 * @param data
 */
export const updateRoadAiLabelInfoById = (data: RoadAiLabelInfoForm) => {
  return request({
    url: '/biz/roadAiLabelInfo/updateRoadAiLabelInfoById',
    method: 'put',
    data: data
  });
};
