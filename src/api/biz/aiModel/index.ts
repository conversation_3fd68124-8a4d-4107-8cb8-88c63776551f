import request from '@/utils/request';
import { BizAiModelVo } from './types';

/**
 * 获取AI模型列表
 * @param projectId 项目ID（可选，支持字符串或数字）
 * @param page 页码，默认1
 * @param pageSize 页大小，默认50
 * @returns AI模型列表
 */
export function getAiModelList(projectId?: string | number, page: number = 1, pageSize: number = 50) {
  return request<BizAiModelVo[]>({
    url: '/biz/flightControlApi/aiModels',
    method: 'get',
    params: {
      projectId,
      page,
      pageSize
    }
  });
}