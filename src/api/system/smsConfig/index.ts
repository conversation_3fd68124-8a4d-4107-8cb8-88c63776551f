import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { SmsConfigVO, SmsConfigForm, SmsConfigQuery } from '@/api/system/smsConfig/types';

/**
 * 查询短信配置列表
 * @param query
 * @returns {*}
 */

export const listSmsConfig = (query?: SmsConfigQuery): AxiosPromise<SmsConfigVO[]> => {
  return request({
    url: '/system/smsConfig/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询短信配置详细
 * @param id
 */
export const getSmsConfig = (id: string | number): AxiosPromise<SmsConfigVO> => {
  return request({
    url: '/system/smsConfig/' + id,
    method: 'get'
  });
};

/**
 * 新增短信配置
 * @param data
 */
export const addSmsConfig = (data: SmsConfigForm) => {
  return request({
    url: '/system/smsConfig',
    method: 'post',
    data: data
  });
};

/**
 * 修改短信配置
 * @param data
 */
export const updateSmsConfig = (data: SmsConfigForm) => {
  return request({
    url: '/system/smsConfig',
    method: 'put',
    data: data
  });
};

/**
 * 删除短信配置
 * @param id
 */
export const delSmsConfig = (id: string | number | Array<string | number>) => {
  return request({
    url: '/system/smsConfig/' + id,
    method: 'delete'
  });
};
