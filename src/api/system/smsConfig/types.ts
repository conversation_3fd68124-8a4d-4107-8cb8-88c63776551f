export interface SmsConfigVO {
  /**
   * 主键ID
   */
  id: string | number;

  /**
   * 租户ID
   */
  tenantId: string;

  /**
   * 场景类型
   */
  sceneType: string;

  /**
   * 短信模板ID
   */
  templateId: string | number;

  /**
   * 一级通知人手机号（逗号分隔）
   */
  level1Phones: string;

  /**
   * 二级通知人手机号（逗号分隔）
   */
  level2Phones: string;

  /**
   * 短信服务商标识
   */
  smsProvider: string;

  /**
   * 状态（0正常 1停用）
   */
  status: string;

  /**
   * 备注
   */
  remark: string;
}

export interface SmsConfigForm extends BaseEntity {
  /**
   * 主键ID
   */
  id?: string | number;

  /**
   * 租户ID
   */
  tenantId?: string;

  /**
   * 场景类型
   */
  sceneType?: string;

  /**
   * 短信模板ID
   */
  templateId?: string | number;

  /**
   * 一级通知人手机号（逗号分隔）
   */
  level1Phones?: string;

  /**
   * 二级通知人手机号（逗号分隔）
   */
  level2Phones?: string;

  /**
   * 短信服务商标识
   */
  smsProvider?: string;

  /**
   * 状态（0正常 1停用）
   */
  status?: string;

  /**
   * 备注
   */
  remark?: string;
}

export interface SmsConfigQuery extends PageQuery {
  /**
   * 租户ID
   */
  tenantId?: string | number;

  /**
   * 场景类型
   */
  sceneType?: string;

  /**
   * 短信模板ID
   */
  templateId?: string | number;

  /**
   * 一级通知人手机号（逗号分隔）
   */
  level1Phones?: string;

  /**
   * 二级通知人手机号（逗号分隔）
   */
  level2Phones?: string;

  /**
   * 状态（0正常 1停用）
   */
  status?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
