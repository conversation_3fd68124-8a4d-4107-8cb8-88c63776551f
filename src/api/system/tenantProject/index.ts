import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { TenantProjectVO, TenantProjectForm, TenantProjectQuery } from '@/api/system/tenantProject/types';

/**
 * 查询租户项目关联列表
 * @param query
 * @returns {*}
 */

export const listTenantProject = (query?: TenantProjectQuery): AxiosPromise<TenantProjectVO[]> => {
  return request({
    url: '/system/tenantProject/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询租户项目关联详细
 * @param id
 */
export const getTenantProject = (id: string | number): AxiosPromise<TenantProjectVO> => {
  return request({
    url: '/system/tenantProject/' + id,
    method: 'get'
  });
};

/**
 * 新增租户项目关联
 * @param data
 */
export const addTenantProject = (data: TenantProjectForm) => {
  return request({
    url: '/system/tenantProject',
    method: 'post',
    data: data
  });
};

/**
 * 修改租户项目关联
 * @param data
 */
export const updateTenantProject = (data: TenantProjectForm) => {
  return request({
    url: '/system/tenantProject',
    method: 'put',
    data: data
  });
};

/**
 * 删除租户项目关联
 * @param id
 */
export const delTenantProject = (id: string | number | Array<string | number>) => {
  return request({
    url: '/system/tenantProject/' + id,
    method: 'delete'
  });
};
