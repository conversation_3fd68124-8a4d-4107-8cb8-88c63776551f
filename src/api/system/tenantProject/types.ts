export interface TenantProjectVO {
  /**
   * 主键ID
   */
  id: string | number;

  /**
   * 项目ID
   */
  projectId: string | number;

  /**
   * 是否为项目管理者（0否 1是）
   */
  isManager: string;

  /**
   * 备注
   */
  remark: string;

}

export interface TenantProjectForm extends BaseEntity {
  /**
   * 主键ID
   */
  id?: string | number;

  /**
   * 项目ID
   */
  projectId?: string | number;

  /**
   * 是否为项目管理者（0否 1是）
   */
  isManager?: string;

  /**
   * 备注
   */
  remark?: string;

}

export interface TenantProjectQuery extends PageQuery {

  /**
   * 项目ID
   */
  projectId?: string | number;

  /**
   * 是否为项目管理者（0否 1是）
   */
  isManager?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



