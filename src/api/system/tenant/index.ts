import request from '@/utils/request';
import { TenantForm, TenantQuery, TenantVO, TenantPermissionBatchForm, TenantPermissionVO } from './types';
import { AxiosPromise } from 'axios';

// 查询租户列表
export function listTenant(query: TenantQuery): AxiosPromise<TenantVO[]> {
  return request({
    url: '/system/tenant/list',
    method: 'get',
    params: query
  });
}

// 查询租户详细
export function getTenant(id: string | number): AxiosPromise<TenantVO> {
  return request({
    url: '/system/tenant/' + id,
    method: 'get'
  });
}

// 新增租户
export function addTenant(data: TenantForm) {
  return request({
    url: '/system/tenant',
    method: 'post',
    headers: {
      isEncrypt: true,
      repeatSubmit: false
    },
    data: data
  });
}

// 修改租户
export function updateTenant(data: TenantForm) {
  return request({
    url: '/system/tenant',
    method: 'put',
    data: data
  });
}

// 租户状态修改
export function changeTenantStatus(id: string | number, tenantId: string | number, status: string) {
  const data = {
    id,
    tenantId,
    status
  };
  return request({
    url: '/system/tenant/changeStatus',
    method: 'put',
    data: data
  });
}

// 删除租户
export function delTenant(id: string | number | Array<string | number>) {
  return request({
    url: '/system/tenant/' + id,
    method: 'delete'
  });
}

// 动态切换租户
export function dynamicTenant(tenantId: string | number) {
  return request({
    url: '/system/tenant/dynamic/' + tenantId,
    method: 'get'
  });
}

// 清除动态租户
export function dynamicClear() {
  return request({
    url: '/system/tenant/dynamic/clear',
    method: 'get'
  });
}

// 批量授权租户管理权限
export function batchGrantTenantPermission(data: TenantPermissionBatchForm) {
  return request({
    url: '/system/tenantPermission/batchGrant',
    method: 'post',
    data: data
  });
}

// 查询指定上级租户的管理权限关系
export function listTenantPermissionByParent(parentTenantId: string | number): AxiosPromise<TenantPermissionVO[]> {
  return request({
    url: '/system/tenantPermission/listByParent/' + parentTenantId,
    method: 'get'
  });
}

// 获取当前用户可管理的租户列表（用于租户切换）
export function getManageableTenants(): AxiosPromise<TenantVO[]> {
  return request({
    url: '/system/tenantPermission/manageableTenants',
    method: 'get'
  });
}

// 管理员切换到被管理的租户（带权限检查）
export function switchToManagedTenant(tenantId: string | number) {
  return request({
    url: '/system/tenantPermission/switch/' + tenantId,
    method: 'post'
  });
}

// 清除管理员的租户切换（回到原租户）
export function clearManagedTenantSwitch() {
  return request({
    url: '/system/tenantPermission/switchBack',
    method: 'post'
  });
}

// 获取当前租户信息（包括动态租户状态）
export function getCurrentTenantInfo() {
  return request({
    url: '/system/tenantPermission/currentTenant',
    method: 'get'
  });
}

// 同步租户套餐
export function syncTenantPackage(tenantId: string | number, packageId: string | number) {
  const data = {
    tenantId,
    packageId
  };
  return request({
    url: '/system/tenant/syncTenantPackage',
    method: 'get',
    params: data
  });
}

// 同步租户字典
export function syncTenantDict() {
  return request({
    url: '/system/tenant/syncTenantDict',
    method: 'get'
  });
}
