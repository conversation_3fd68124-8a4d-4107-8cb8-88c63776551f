export interface TenantVO extends BaseEntity {
  id: number | string;
  tenantId: number | string;
  username: string;
  contactUserName: string;
  contactPhone: string;
  companyName: string;
  licenseNumber: string;
  address: string;
  domain: string;
  intro: string;
  remark: string;
  packageId: string | number;
  expireTime: string;
  accountCount: number;
  status: string;
}

export interface TenantQuery extends PageQuery {
  tenantId: string | number;

  contactUserName: string;

  contactPhone: string;

  companyName: string;
}

export interface TenantForm {
  id: number | string | undefined;
  tenantId: number | string | undefined;
  username: string;
  password: string;
  contactUserName: string;
  contactPhone: string;
  companyName: string;
  licenseNumber: string;
  domain: string;
  address: string;
  intro: string;
  remark: string;
  packageId: string | number;
  expireTime: string;
  accountCount: number;
  status: string;
}

/**
 * 租户权限关系
 */
export interface TenantPermissionVO {
  id: string | number;
  parentTenantId: string | number;
  childTenantId: string | number;
  permissionType: string;
  status: string;
  remark: string;
  parentTenantName?: string;
  childTenantName?: string;
}

/**
 * 批量授权表单
 */
export interface TenantPermissionBatchForm {
  parentTenantId: string | number;
  childTenantIds: string[];
  permissionType: string;
  status: string;
  remark?: string;
}
