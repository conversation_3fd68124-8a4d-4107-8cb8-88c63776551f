import { ref } from 'vue';
import { listLabelByImageUrl } from '@/api/biz/roadAiLabelInfo';
import type { RoadAiLabelInfoVO } from '@/api/biz/roadAiLabelInfo/types';

// 标签数据接口
export interface LabelData {
  id: number;
  label: string;
  x: number;
  y: number;
  width: number;
  height: number;
  confidence: number;
  longitude?: number;
  latitude?: number;
  file_size?: number;
  file_name?: string;
  absolute_altitude?: number;
  relative_altitude?: number;
  gimbal_yaw_degree?: number;
  image_width?: number;
  image_height?: number;
}

/**
 * 图片标签加载 Hook
 */
export function useImageLabels() {
  const currentLabels = ref<LabelData[]>([]);
  const labelLoading = ref(false);

  /**
   * 加载图片标签
   * @param imageUrl 图片URL
   * @param labelType 标签类型(可选)
   */
  const loadLabels = async (imageUrl: string, labelType?: string) => {
    labelLoading.value = true;
    try {
      const res = await listLabelByImageUrl(imageUrl, labelType);
      if (res.data && res.data.length > 0) {
        // 转换数据格式以适配PreviewModal组件
        currentLabels.value = res.data.map((item: RoadAiLabelInfoVO, index: number) => ({
          id: item.id || index,
          label: item.type || '未知',
          x: Number(item.x) || 0,
          y: Number(item.y) || 0,
          width: Number(item.width) || 0,
          height: Number(item.height) || 0,
          confidence: Number(item.confidence) || 0,
          longitude: Number(item.longitude) || 0,
          latitude: Number(item.latitude) || 0,
          file_size: Number(item.fileSize) || 0,
          file_name: item.pictureName || '',
          absolute_altitude: Number(item.absoluteAltitude) || 0,
          relative_altitude: Number(item.relativeAltitude) || 0,
          gimbal_yaw_degree: Number(item.gimbalYawDegree) || 0,
          image_width: Number(item.imageWidth) || 0,
          image_height: Number(item.imageHeight) || 0
        }));
      } else {
        currentLabels.value = [];
      }
    } catch (error) {
      console.error('加载图片标签失败:', error);
      currentLabels.value = [];
    } finally {
      labelLoading.value = false;
    }
  };

  /**
   * 清空标签
   */
  const clearLabels = () => {
    currentLabels.value = [];
  };

  return {
    currentLabels,
    labelLoading,
    loadLabels,
    clearLabels
  };
}