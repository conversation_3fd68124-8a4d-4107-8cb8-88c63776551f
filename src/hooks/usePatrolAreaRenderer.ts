import { onUnmounted, shallowRef, watchEffect, Ref } from 'vue';
import * as DC from '@dvgis/dc-sdk';

/**
 * Interface for patrol area data.
 */
export interface PatrolAreaInfo {
  area_id: string | number;
  name: string;
  type: string; // e.g., 'dfence'
  content: {
    properties?: {
      color?: string;
      clampToGround?: boolean;
      [key: string]: any; // Allow other properties
    };
    geometry: {
      type: 'Circle' | 'Polygon' | 'Point' | string; // Support Circle, extendable
      coordinates: number[] | number[][] | number[][][]; // Flexible for different geometry types
      radius?: number; // Specific to Circle
    };
  };
  status?: boolean;
  username?: string;
  create_time?: number;
  update_time?: number;
}

// Helper for area calculation and formatting
const calculateAndFormatArea = (radius: number): string => {
  const areaMetersSquared = Math.PI * Math.pow(radius, 2);
  // Convert to square kilometers for better readability if areas are large
  const areaKilometersSquared = areaMetersSquared / 1000000;
  if (areaKilometersSquared >= 1) {
    return areaKilometersSquared.toFixed(2) + ' km²';
  }
  return areaMetersSquared.toFixed(2) + ' m²';
};

/**
 * Vue 3 composable hook to render patrol areas with radar effect on a Cesium map.
 * @param viewerRef Ref to the DC.Viewer instance.
 * @param patrolAreasDataRef Ref to an array of PatrolAreaInfo.
 */
export function usePatrolAreaRenderer(viewerRef: Ref<DC.Viewer | null>, patrolAreasDataRef: Ref<PatrolAreaInfo[]>) {
  const patrolLayer = shallowRef<DC.VectorLayer | null>(null);

  const clearAndRemoveLayer = () => {
    const viewer = viewerRef.value;
    if (patrolLayer.value) {
      patrolLayer.value.removeAll(); // Clear all overlays from the layer
      if (viewer) {
        viewer.removeLayer(patrolLayer.value); // Remove the layer from the viewer
      }
      patrolLayer.value = null; // Reset the layer reference
    }
  };

  watchEffect(() => {
    const viewer = viewerRef.value;

    // 4.a & 4.b: Handle invalid viewer
    if (!viewer) {
      clearAndRemoveLayer();
      return;
    }

    const patrolAreasData = patrolAreasDataRef.value;

    // 4.c: Handle invalid or empty patrolAreasData
    if (!patrolAreasData || patrolAreasData.length === 0) {
      if (patrolLayer.value) {
        patrolLayer.value.removeAll(); // Clear overlays if layer exists but no data
      }
      // Do not remove the layer itself here, it might be reused.
      // clearAndRemoveLayer(); // This would remove the layer, which might not be desired if data is just temporarily empty.
      return;
    }

    // 4.d: Ensure DC.VectorLayer instance
    if (!patrolLayer.value) {
      patrolLayer.value = new DC.VectorLayer('patrolAreaLayer');
      viewer.addLayer(patrolLayer.value);
    }

    // 4.e: Clear existing overlays from the layer
    patrolLayer.value.removeAll();

    // 4.f: Process patrolAreasData
    patrolAreasData.forEach((areaInfo) => {
      // 4.f.i: Check geometry type
      if (areaInfo.content?.geometry?.type !== 'Circle') {
        console.warn('Skipping patrol area: Not a Circle or geometry undefined.', areaInfo);
        return;
      }

      const geometry = areaInfo.content.geometry;
      // 4.f.ii & 4.f.iii: Extract coordinates and radius
      const coordinates = geometry.coordinates as number[]; // Assuming [longitude, latitude]
      const radius = geometry.radius;

      // 4.f.iv: Validate extracted data
      if (
        !coordinates ||
        coordinates.length < 2 ||
        typeof coordinates[0] !== 'number' ||
        typeof coordinates[1] !== 'number' ||
        radius === undefined ||
        typeof radius !== 'number' ||
        radius <= 0
      ) {
        console.warn('Skipping patrol area due to invalid/missing coordinates or radius:', areaInfo);
        return;
      }
      const longitude = coordinates[0];
      const latitude = coordinates[1];

      // 4.f.v: Create center position
      const centerPosition = DC.Position.fromDegrees(longitude, latitude, 0);

      // 4.f.vi & 4.f.vii: Create Radar Circle and set style
      const circle = new DC.Circle(centerPosition, radius);
      circle.setStyle({
        material: new DC.RadarLineMaterialProperty({
          color: new DC.Color(0, 1.0, 1.0, 0.8), // Cyan color with some transparency
          speed: 8.0
        })
        // fill: false, // No fill for radar line effect
        // outline: true,
        // outlineColor: DC.Color.CYAN.withAlpha(0.5),
        // outlineWidth: 2
      });
      // 4.f.viii: Add circle to layer
      patrolLayer.value?.addOverlay(circle);

      // 4.f.ix: Calculate Area
      const areaString = calculateAndFormatArea(radius);

      // 4.f.x & 4.f.xi: Create Label
      const labelPosition = DC.Position.fromDegrees(longitude, latitude, 100); // Elevate label slightly
      const label = new DC.Label(labelPosition);
      label.text = `${areaInfo.name}\n面积: ${areaString}`;
      label.font = '16px Microsoft YaHei';
      label.fillColor = DC.Color.WHITE;
      label.outlineColor = DC.Color.BLACK;
      label.outlineWidth = 2;
      label.pixelOffset = new DC.Cartesian2(0, -20);
      label.showBackground = true;
      label.backgroundColor = new DC.Color(0.16, 0.16, 0.16, 0.75);
      label.backgroundPadding = new DC.Cartesian2(8, 6);
      label.eyeOffset = new DC.Cartesian3(0, 0, -10);
      label.horizontalOrigin = DC.HorizontalOrigin.CENTER;
      label.verticalOrigin = DC.VerticalOrigin.BOTTOM;

      // 4.f.xii: Add label to layer
      patrolLayer.value?.addOverlay(label);
    });
  });

  onUnmounted(() => {
    clearAndRemoveLayer();
  });

  // The hook can return methods to manually trigger refresh or clear if needed in the future.
  // For now, it manages its lifecycle internally via watchEffect and onUnmounted.
}

/**
 * Example defaultRoute data structure.
 * This can be fetched from an API in a real application.
 */
export const defaultPatrolAreas: PatrolAreaInfo[] = [
  {
    area_id: 'default-area-1',
    name: '示例机场A',
    type: 'dfence',
    content: {
      properties: {
        color: '#00FFFF', // Cyan, though radar material has its own color
        clampToGround: false
      },
      geometry: {
        type: 'Circle',
        coordinates: [108.787248, 22.027119], // Lng, Lat
        radius: 10000 // 10 km radius
      }
    },
    status: true,
    username: 'system',
    create_time: Date.now(),
    update_time: Date.now()
  },
  {
    area_id: 'default-area-2',
    name: '示例机场B',
    type: 'dfence',
    content: {
      properties: {
        color: '#00FFFF',
        clampToGround: false
      },
      geometry: {
        type: 'Circle',
        coordinates: [108.95, 22.15],
        radius: 15000 // 15 km radius
      }
    },
    status: true,
    username: 'system',
    create_time: Date.now(),
    update_time: Date.now()
  }
];

// Placeholder for asynchronous data fetching logic
export async function fetchPatrolAreaData(): Promise<PatrolAreaInfo[]> {
  // Simulate API call delay
  await new Promise((resolve) => setTimeout(resolve, 1000));
  // In a real app, you would fetch this data from an API:
  // const response = await fetch('/api/patrol-areas');
  // const data = await response.json();
  // return data;
  return Promise.resolve(defaultPatrolAreas);
}
