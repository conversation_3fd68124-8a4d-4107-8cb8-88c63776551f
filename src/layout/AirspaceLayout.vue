<template>
  <div class="airspace-layout">
    <!-- 主体区域 -->
    <main class="airspace-main">
      <router-view />
    </main>
  </div>
</template>

<script setup lang="ts">
// TODO: 集成 AirspaceHeader、ControlPanel、MapContainer、DataPanel 等专用组件
</script>

<style scoped>
.airspace-layout {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  background: #0b1b2a;
  color: #ffffff;
}

.airspace-main {
  flex: 1;
  overflow: hidden;
  position: relative;
}
</style>
