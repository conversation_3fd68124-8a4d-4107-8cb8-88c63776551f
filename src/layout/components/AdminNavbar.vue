<template>
  <div class="navbar">
    <hamburger id="hamburger-container" :is-active="appStore.sidebar.opened" class="hamburger-container" @toggle-click="toggleSideBar" />
    <!-- <breadcrumb v-if="!settingsStore.topNav" id="breadcrumb-container" class="breadcrumb-container" /> -->
    <!-- <top-nav id="topmenu-container" class="topmenu-container" /> -->

    <div class="right-menu flex align-center">
      <template v-if="appStore.device !== 'mobile'">
        <!-- 当前租户信息显示 -->
        <div class="current-tenant-info">
          <el-tag type="info" size="small" effect="plain" class="mr-2">
            <svg-icon icon-class="company" class="mr-1" />
            当前租户：{{ getCurrentDisplayTenantName() }}
          </el-tag>
        </div>
        
        <!-- 超级管理员的租户切换 -->
        <el-select
          v-if="userId === 1 && tenantEnabled"
          v-model="companyName"
          class="min-w-244px"
          clearable
          filterable
          reserve-keyword
          :placeholder="proxy.$t('navbar.selectTenant')"
          @change="dynamicTenantEvent"
          @clear="dynamicClearEvent"
        >
          <el-option v-for="item in tenantList" :key="item.tenantId" :label="item.companyName" :value="item.tenantId"> </el-option>
          <template #prefix><svg-icon icon-class="company" class="el-input__icon input-icon" /></template>
        </el-select>
        
        <!-- 非超管用户的租户切换 -->
        <el-select
          v-else-if="userId !== 1 && hasTenantPermission && manageableTenantEnabled"
          v-model="managedTenantId"
          class="min-w-244px"
          clearable
          filterable
          reserve-keyword
          :placeholder="isInSwitchedMode ? '当前在管辖租户中' : '切换到管辖租户'"
          @change="switchToManagedTenantEvent"
          @clear="clearManagedTenantEvent"
        >
          <!-- 当前租户选项（用于切换回来） -->
          <el-option 
            :key="originalTenantInfo.tenantId" 
            :label="`${originalTenantInfo.companyName}${isInSwitchedMode ? '（原租户）' : '（当前租户）'}`" 
            :value="originalTenantInfo.tenantId"
          />
          <!-- 可管理的租户选项 -->
          <el-option 
            v-for="item in manageableTenantList" 
            :key="item.tenantId" 
            :label="item.companyName" 
            :value="item.tenantId"
          />
          <template #prefix><svg-icon icon-class="company" class="el-input__icon input-icon" /></template>
        </el-select>
        
        <!-- 切换状态提示（仅在切换状态时显示） -->
        <el-tag v-if="hasTenantPermission && isInSwitchedMode" type="warning" size="small" class="ml-2">
          <svg-icon icon-class="switch" class="mr-1" />
          已切换
        </el-tag>

        <search-menu ref="searchMenuRef" />
        <el-tooltip content="搜索" effect="dark" placement="bottom">
          <div class="right-menu-item hover-effect" @click="openSearchMenu">
            <svg-icon class-name="search-icon" icon-class="search" />
          </div>
        </el-tooltip>
        <!-- 消息 -->
        <el-tooltip :content="proxy.$t('navbar.message')" effect="dark" placement="bottom">
          <div>
            <el-popover placement="bottom" trigger="click" transition="el-zoom-in-top" :width="300" :persistent="false">
              <template #reference>
                <el-badge :value="newNotice > 0 ? newNotice : ''" :max="99">
                  <div class="right-menu-item hover-effect" style="display: block"><svg-icon icon-class="message" /></div>
                </el-badge>
              </template>
              <template #default>
                <notice></notice>
              </template>
            </el-popover>
          </div>
        </el-tooltip>

        <el-tooltip :content="proxy.$t('navbar.full')" effect="dark" placement="bottom">
          <screenfull id="screenfull" class="right-menu-item hover-effect" />
        </el-tooltip>
        <el-tooltip :content="proxy.$t('navbar.layoutSize')" effect="dark" placement="bottom">
          <size-select id="size-select" class="right-menu-item hover-effect" />
        </el-tooltip>
      </template>
      <div class="avatar-container">
        <el-dropdown class="right-menu-item hover-effect" trigger="click" @command="handleCommand">
          <div class="avatar-wrapper">
            <img :src="userStore.avatar" class="user-avatar" />
            <el-icon><caret-bottom /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <router-link v-if="!dynamic" to="/user/profile">
                <el-dropdown-item>{{ proxy.$t('navbar.personalCenter') }}</el-dropdown-item>
              </router-link>
              <el-dropdown-item v-if="settingsStore.showSettings" command="setLayout">
                <span>{{ proxy.$t('navbar.layoutSetting') }}</span>
              </el-dropdown-item>
              <el-dropdown-item divided command="logout">
                <span>{{ proxy.$t('navbar.logout') }}</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import SearchMenu from './TopBar/search.vue';
import { useAppStore } from '@/store/modules/app';
import { useUserStore } from '@/store/modules/user';
import { useSettingsStore } from '@/store/modules/settings';
import { useNoticeStore } from '@/store/modules/notice';
import { getTenantList } from '@/api/login';
import { dynamicClear, dynamicTenant, getManageableTenants, switchToManagedTenant, clearManagedTenantSwitch, getCurrentTenantInfo } from '@/api/system/tenant';
import { TenantVO } from '@/api/types';
import { checkPermi } from '@/utils/permission';
import notice from './notice/index.vue';
import router from '@/router';
import { ElMessageBoxOptions } from 'element-plus/es/components/message-box/src/message-box.type';

const appStore = useAppStore();
const userStore = useUserStore();
const settingsStore = useSettingsStore();
const noticeStore = storeToRefs(useNoticeStore());
const newNotice = ref(<number>0);

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const userId = ref(userStore.userId);
const companyName = ref(undefined);
const tenantList = ref<TenantVO[]>([]);
// 是否切换了租户
const dynamic = ref(false);
// 租户开关
const tenantEnabled = ref(true);
// 搜索菜单
const searchMenuRef = ref<InstanceType<typeof SearchMenu>>();

// 非超管用户的租户管理相关状态
const managedTenantId = ref<string | undefined>(undefined);
const manageableTenantList = ref<TenantVO[]>([]);
const manageableTenantEnabled = ref(false);
const originalTenantInfo = ref<TenantVO>({ tenantId: '', companyName: '' } as TenantVO);
const isInSwitchedMode = ref(false);
const currentTenantName = ref<string>(''); // 当前生效的租户名称

// 超管的登录租户信息
const superAdminLoginTenant = ref<TenantVO>({ tenantId: '', companyName: '' } as TenantVO);

// 检查用户是否有租户权限管理权限
const hasTenantPermission = computed(() => {
  return checkPermi(['system:tenantPermission:list']);
});

// 获取当前显示的租户名称
const getCurrentDisplayTenantName = () => {
  // 如果是超管用户
  if (userId.value === 1) {
    // 如果已选择了租户（处于动态切换状态），显示选中的租户名称
    if (dynamic.value && companyName.value && tenantList.value.length > 0) {
      const selectedTenant = tenantList.value.find(tenant => tenant.tenantId === companyName.value);
      return selectedTenant ? selectedTenant.companyName : '未知租户';
    }
    // 如果没有切换，显示登录租户的名称
    if (superAdminLoginTenant.value && superAdminLoginTenant.value.companyName) {
      return superAdminLoginTenant.value.companyName;
    }
    // 最后的备用方案
    return '系统管理员';
  }
  
  // 如果是非超管用户
  if (isInSwitchedMode.value && currentTenantName.value) {
    // 如果在切换状态，显示当前切换到的租户名称
    return currentTenantName.value;
  } else if (originalTenantInfo.value && originalTenantInfo.value.companyName) {
    // 显示原始租户名称
    return originalTenantInfo.value.companyName;
  }
  
  // 默认情况
  return '当前租户';
};

const openSearchMenu = () => {
  searchMenuRef.value?.openSearch();
};

// 动态切换
const dynamicTenantEvent = async (tenantId: string) => {
  if (companyName.value != null && companyName.value !== '') {
    await dynamicTenant(tenantId);
    dynamic.value = true;
    await proxy?.$router.push('/');
    await proxy?.$tab.closeAllPage();
    await proxy?.$tab.refreshPage();
  }
};

const dynamicClearEvent = async () => {
  await dynamicClear();
  dynamic.value = false;
  companyName.value = undefined; // 清除选中的租户
  await proxy?.$router.push('/');
  await proxy?.$tab.closeAllPage();
  await proxy?.$tab.refreshPage();
};

/** 租户列表 */
const initTenantList = async () => {
  const { data } = await getTenantList(true);
  tenantEnabled.value = data.tenantEnabled === undefined ? true : data.tenantEnabled;
  if (tenantEnabled.value) {
    tenantList.value = data.voList;
    
    // 超管用户需要检查当前动态租户状态
    if (userId.value === 1) {
      await initSuperAdminTenantStatus();
    }
  }
  
  // 非超管用户初始化
  if (userId.value !== 1) {
    if (hasTenantPermission.value) {
      // 有权限的用户使用完整的租户切换功能
      await initManageableTenantList();
    } else {
      // 没有权限的用户只初始化基本租户信息
      await initBasicTenantInfo();
    }
  }
};

/** 初始化超管的租户状态 */
const initSuperAdminTenantStatus = async () => {
  try {
    // 超管也可以使用 getCurrentTenantInfo 来获取动态租户状态
    const { data: tenantInfo } = await getCurrentTenantInfo();
    
    // 如果处于动态切换状态
    if (tenantInfo.isDynamic && tenantInfo.dynamicTenantId) {
      dynamic.value = true;
      companyName.value = tenantInfo.dynamicTenantId;
      console.log('超管恢复动态租户状态:', tenantInfo.dynamicTenantId);
    } else {
      dynamic.value = false;
      companyName.value = undefined;
      console.log('超管处于正常状态，无动态租户');
    }
    
    // 获取超管的登录租户信息（原始租户）
    if (tenantInfo.originalTenantInfo) {
      superAdminLoginTenant.value = tenantInfo.originalTenantInfo;
    } else if (userStore.tenantId) {
      // 如果没有详细信息，尝试从租户列表中找到对应的登录租户
      const loginTenant = tenantList.value.find(tenant => tenant.tenantId === userStore.tenantId);
      if (loginTenant) {
        superAdminLoginTenant.value = loginTenant;
      } else {
        // 如果在列表中找不到，使用基本信息
        superAdminLoginTenant.value = {
          tenantId: userStore.tenantId,
          companyName: tenantInfo.originalTenantName || '登录租户',
          contactUserName: '',
          contactPhone: '',
          status: '1',
          packageId: '',
          expireTime: '',
          accountCount: 0,
          createTime: '',
          id: userStore.tenantId
        };
      }
    }
  } catch (error) {
    console.warn('获取超管租户状态失败，可能没有相关权限:', error);
    // 如果获取失败，保持默认状态并尝试从租户列表获取登录租户信息
    dynamic.value = false;
    companyName.value = undefined;
    
    // 尝试从租户列表中找到登录租户
    if (userStore.tenantId && tenantList.value.length > 0) {
      const loginTenant = tenantList.value.find(tenant => tenant.tenantId === userStore.tenantId);
      if (loginTenant) {
        superAdminLoginTenant.value = loginTenant;
      }
    }
  }
};

/** 初始化可管理租户列表 */
const initManageableTenantList = async () => {
  // 检查权限，如果没有权限就不执行
  if (!hasTenantPermission.value) {
    console.log('用户没有租户权限管理权限，跳过租户切换功能初始化');
    return;
  }
  
  try {
    // 并行获取可管理租户列表和当前租户状态
    const [manageableTenantsResult, currentTenantInfoResult] = await Promise.allSettled([
      getManageableTenants(),
      getCurrentTenantInfo()
    ]);
    
    // 处理可管理租户列表
    if (manageableTenantsResult.status === 'fulfilled') {
      manageableTenantList.value = manageableTenantsResult.value.data || [];
      manageableTenantEnabled.value = manageableTenantList.value.length > 0;
    } else {
      console.error('获取可管理租户列表失败:', manageableTenantsResult.reason);
      manageableTenantEnabled.value = false;
      return;
    }
    
    // 处理当前租户状态信息
    if (currentTenantInfoResult.status === 'fulfilled') {
      const tenantInfo = currentTenantInfoResult.value.data;
      isInSwitchedMode.value = tenantInfo.isDynamic;
      
      // 如果当前处于切换状态，设置选中的租户ID
      if (tenantInfo.isDynamic && tenantInfo.dynamicTenantId) {
        managedTenantId.value = tenantInfo.dynamicTenantId;
      } else {
        managedTenantId.value = undefined;
      }
      
      // 使用后端返回的原始租户详细信息
      if (tenantInfo.originalTenantInfo) {
        originalTenantInfo.value = tenantInfo.originalTenantInfo;
      } else {
        // 如果没有详细信息，使用基本信息
        originalTenantInfo.value = {
          tenantId: tenantInfo.originalTenantId || '',
          companyName: tenantInfo.originalTenantName || '当前租户',
          contactUserName: '',
          contactPhone: '',
          status: '1',
          packageId: '',
          expireTime: '',
          accountCount: 0,
          createTime: '',
          id: tenantInfo.originalTenantId || ''
        };
      }
      
      // 保存当前生效的租户名称
      currentTenantName.value = tenantInfo.currentTenantName || tenantInfo.originalTenantName || '当前租户';
    } else {
      console.warn('获取当前租户状态失败:', currentTenantInfoResult.reason);
      // 降级处理：使用 userStore 中的信息
      originalTenantInfo.value = {
        tenantId: userStore.tenantId || '',
        companyName: '当前租户',
        contactUserName: '',
        contactPhone: '',
        status: '1',
        packageId: '',
        expireTime: '',
        accountCount: 0,
        createTime: '',
        id: userStore.tenantId || ''
      };
      currentTenantName.value = '当前租户';
    }
  } catch (error) {
    console.error('初始化可管理租户列表失败:', error);
    manageableTenantEnabled.value = false;
    currentTenantName.value = '当前租户';
  }
};

/** 初始化基本租户信息（没有租户切换权限的用户） */
const initBasicTenantInfo = async () => {
  try {
    const { data } = await getCurrentTenantInfo();
    originalTenantInfo.value = {
      tenantId: data.originalTenantId || '',
      companyName: data.originalTenantName || '当前租户',
      contactUserName: '',
      contactPhone: '',
      status: '1',
      packageId: '',
      expireTime: '',
      accountCount: 0,
      createTime: '',
      id: data.originalTenantId || ''
    };
    currentTenantName.value = originalTenantInfo.value.companyName;
  } catch (error) {
    console.warn('获取基本租户信息失败，尝试从租户列表获取:', error);
    
    // 尝试从租户列表中找到当前用户的租户信息
    let tenantName = '当前租户';
    if (userStore.tenantId && tenantList.value.length > 0) {
      const currentTenant = tenantList.value.find(tenant => tenant.tenantId === userStore.tenantId);
      if (currentTenant) {
        tenantName = currentTenant.companyName;
      }
    }
    
    originalTenantInfo.value = {
      tenantId: userStore.tenantId || '',
      companyName: tenantName,
      contactUserName: '',
      contactPhone: '',
      status: '1',
      packageId: '',
      expireTime: '',
      accountCount: 0,
      createTime: '',
      id: userStore.tenantId || ''
    };
    currentTenantName.value = tenantName;
  }
};

/** 切换到被管理的租户 */
const switchToManagedTenantEvent = async (tenantId: string) => {
  if (!tenantId) return;
  
  // 检查权限
  if (!hasTenantPermission.value) {
    proxy?.$modal.msgError('您没有租户管理权限');
    return;
  }
  
  try {
    // 如果选择的是当前租户，则清除切换状态
    if (tenantId === originalTenantInfo.value.tenantId) {
      await clearManagedTenantEvent();
      return;
    }
    
    // 切换到选中的租户
    await switchToManagedTenant(tenantId);
    isInSwitchedMode.value = true;
    
    // 刷新页面
    await proxy?.$router.push('/');
    await proxy?.$tab.closeAllPage();
    await proxy?.$tab.refreshPage();
    
    proxy?.$modal.msgSuccess('已切换到管辖租户');
  } catch (error) {
    console.error('切换租户失败:', error);
    proxy?.$modal.msgError('切换租户失败');
    managedTenantId.value = undefined;
  }
};

/** 清除租户切换（回到原租户） */
const clearManagedTenantEvent = async () => {
  // 检查权限
  if (!hasTenantPermission.value) {
    proxy?.$modal.msgError('您没有租户管理权限');
    return;
  }
  
  try {
    await clearManagedTenantSwitch();
    isInSwitchedMode.value = false;
    managedTenantId.value = undefined;
    
    // 刷新页面
    await proxy?.$router.push('/');
    await proxy?.$tab.closeAllPage();
    await proxy?.$tab.refreshPage();
    
    proxy?.$modal.msgSuccess('已回到原租户');
  } catch (error) {
    console.error('清除租户切换失败:', error);
    proxy?.$modal.msgError('清除租户切换失败');
  }
};

defineExpose({
  initTenantList
});

const toggleSideBar = () => {
  appStore.toggleSideBar(false);
};

const logout = async () => {
  await ElMessageBox.confirm('确定注销并退出系统吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  } as ElMessageBoxOptions);
  userStore.logout().then(() => {
    router.replace({
      path: '/login',
      query: {
        redirect: encodeURIComponent(router.currentRoute.value.fullPath || '/')
      }
    });
    proxy?.$tab.closeAllPage();
  });
};

const emits = defineEmits(['setLayout']);
const setLayout = () => {
  emits('setLayout');
};
// 定义Command方法对象 通过key直接调用方法
const commandMap: { [key: string]: any } = {
  setLayout,
  logout
};
const handleCommand = (command: string) => {
  // 判断是否存在该方法
  if (commandMap[command]) {
    commandMap[command]();
  }
};
//用深度监听 消息
watch(
  () => noticeStore.state.value.notices,
  (newVal) => {
    newNotice.value = newVal.filter((item: any) => !item.read).length;
  },
  { deep: true }
);
</script>

<style lang="scss" scoped>
:deep(.el-select .el-input__wrapper) {
  height: 30px;
}

:deep(.el-badge__content.is-fixed) {
  top: 12px;
}

.ml-2 {
  margin-left: 8px;
}

.mr-1 {
  margin-right: 4px;
}

.mr-2 {
  margin-right: 8px;
}

.current-tenant-info {
  display: flex;
  align-items: center;
}

.flex {
  display: flex;
}

.align-center {
  align-items: center;
}

.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  //background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .topmenu-container {
    position: absolute;
    left: 50px;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;
    display: flex;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      margin-right: 40px;

      .avatar-wrapper {
        margin-top: 5px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
          margin-top: 10px;
        }

        i {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
