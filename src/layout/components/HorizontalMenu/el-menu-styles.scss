// HorizontalMenu el-menu 样式系统
// 基于 TopNav 的 .topmenu-container 样式模板，重写 el-menu 相关样式
// 实现 CommonHeader 的 nav-menu 视觉效果

// 主容器样式 - 基于 TopNav 的 .topmenu-container 模板
.horizontal-menu-container {
  font-family: 'CustomFont';
  
  // el-menu 横向模式基础样式
  &.el-menu--horizontal {
    border-bottom: none !important;
    background: transparent !important;
    height: unset;
    
    .new-menu-title {
      display: inline-block;
      margin-right: 20px;
    }
    // 菜单项样式 - 基于 TopNav 模板但应用 CommonHeader 视觉效果
    .el-menu-item {
     
      // height: auto !important;
      // line-height: normal !important;
      color: #d0d8e2 !important;
      font-size: 15px !important;
      font-weight: 500 !important;
      padding: 12px 18px !important;
      margin: 0 4px !important;
      border-radius: 8px !important;
      border-bottom: none !important;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
      position: relative !important;
      overflow: hidden !important;
      line-height: 18px;
      height: unset;
      
      // 悬停光效 - CommonHeader 效果
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transition: left 0.5s;
      }
      
      &:hover::before {
        left: 100%;
      }
      
      // 悬停和激活状态 - CommonHeader 渐变效果
      &:hover,
      &.is-active {
        background: linear-gradient(135deg, #0a8494 0%, #0ea5e9 100%) !important;
        color: #ffffff !important;
        //transform: translateY(-1px) !important;
        box-shadow: 0 4px 12px rgba(10, 132, 148, 0.3) !important;
      }

      &:hover {
        transform: translateY(-1px) !important;
      }
      
      // 移除默认的焦点样式
      &:not(.is-disabled):focus {
        background: linear-gradient(135deg, #0a8494 0%, #0ea5e9 100%);
      }
    }
    
    // 子菜单样式 - 基于 TopNav 模板
    .el-sub-menu {
      .el-sub-menu__title {
        // float: left;
        // height: auto !important;
        // line-height: normal !important;
        color: #d0d8e2 !important;
        font-size: 15px !important;
        font-weight: 500 !important;
        padding: 12px 44px 12px 20px !important;
        margin: 0 4px !important;
        border-radius: 8px !important;
        border-bottom: none !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        position: relative !important;
        overflow: hidden !important;
        line-height: 18px;
        height: unset;
        
        // 悬停光效
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
          transition: left 0.5s;
        }
        
        &:hover::before {
          left: 100%;
        }
        
        // 悬停状态
        &:hover {
          background: linear-gradient(135deg, #0a8494 0%, #0ea5e9 100%) !important;
          color: #ffffff !important;
          transform: translateY(-1px) !important;
          box-shadow: 0 4px 12px rgba(10, 132, 148, 0.3) !important;
        }
        
        // 下拉箭头样式
        .el-sub-menu__icon-arrow {
          // margin-left: 6px !important;
          // font-size: 12px !important;
          transition: transform 0.3s ease !important;
        }
        
        &:hover .el-sub-menu__icon-arrow {
          transform: rotate(180deg) !important;
        }
      }
      
      // 激活状态
      &.is-active .el-sub-menu__title {
        background: linear-gradient(135deg, #0a8494 0%, #0ea5e9 100%) !important;
        color: #ffffff !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 12px rgba(10, 132, 148, 0.3) !important;
      }
    }
    
    // 图标样式 - 保持 TopNav 的间距设置
    .svg-icon {
      margin-right: 6px !important;
      font-size: 14px !important;
    }
  }
}

// 子菜单下拉面板样式 - 应用 CommonHeader 的下拉菜单效果
.el-popper {
  &.horizontal-menu-popper {
    border: none;
    background: transparent;
  }

  .el-menu--popup {
    min-width: 0;
  }
}
.el-menu--popup-container {
  &.horizontal-menu-popper {
    font-family: 'CustomFont' !important;
    background: rgba(0, 26, 46, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(10, 132, 148, 0.3) !important;
    border-radius: 8px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
    padding: 8px 0 !important;
    
    .el-menu {
      background: transparent !important;
      border: none !important;
      
      
    }
    .el-menu-item {
        color: #d0d8e2 !important;
        font-size: 14px !important;
        font-weight: 500 !important;
        padding: 12px 20px !important;
        margin: 2px 8px !important;
        border-radius: 6px !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        background: transparent;
        position: relative !important;
        overflow: hidden !important;
        line-height: 18px;
        height: unset;
        
        // 悬停光效
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
          transition: left 0.5s;
        }
        
        &:hover::before {
          left: 100%;
        }
        
        &:hover {
          background: linear-gradient(135deg, #0a8494 0%, #0ea5e9 100%) !important;
          color: #ffffff !important;
          transform: translateX(4px) !important;
        }
        
        &.is-active {
          background: linear-gradient(135deg, #0ea5e9 0%, #0a8494 100%) !important;
          color: #ffffff !important;
        }
      }

     // 子菜单样式 - 基于 TopNav 模板
     .el-sub-menu {
      .el-sub-menu__title {
        
        // height: auto !important;
        // line-height: normal !important;
        
        color: #d0d8e2 !important;
        font-size: 15px !important;
        font-weight: 500 !important;
        padding: 12px 44px 12px 20px !important;
        margin: 0 4px !important;
        border-radius: 8px !important;
        border-bottom: none !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        position: relative !important;
        overflow: hidden !important;
        line-height: 18px;
        height: unset;
        background: transparent;
        
        // 悬停光效
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
          transition: left 0.5s;
        }
        
        &:hover::before {
          left: 100%;
        }
        
        // 悬停状态
        &:hover {
          background: linear-gradient(135deg, #0a8494 0%, #0ea5e9 100%) !important;
          color: #ffffff !important;
          transform: translateY(-1px) !important;
          box-shadow: 0 4px 12px rgba(10, 132, 148, 0.3) !important;
        }
        
        // 下拉箭头样式
        .el-sub-menu__icon-arrow {
          transform: rotate(0deg) !important;
        }
        
        &:hover .el-sub-menu__icon-arrow {
          transform: rotate(180deg) !important;
        }
      }
      
      // 激活状态
      &.is-active .el-sub-menu__title {
        background: linear-gradient(135deg, #0a8494 0%, #0ea5e9 100%) !important;
        color: #ffffff !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 12px rgba(10, 132, 148, 0.3) !important;
      }
    }
  }
}

// // 响应式支持
// @media (max-width: 768px) {
//   .horizontal-menu-container.el-menu--horizontal {
//     > .el-menu-item,
//     > .el-sub-menu .el-sub-menu__title {
//       padding: 8px 12px !important;
//       font-size: 14px !important;
//       margin: 0 2px !important;
//     }
//   }
// }

// @media (max-width: 480px) {
//   .horizontal-menu-container.el-menu--horizontal {
//     > .el-menu-item,
//     > .el-sub-menu .el-sub-menu__title {
//       padding: 6px 8px !important;
//       font-size: 13px !important;
//       margin: 0 1px !important;
//     }
//   }
// } 