<template>
  <div v-if="!item.hidden">
    <!-- 单个菜单项或只有一个显示子项的情况 - 基于SidebarItem逻辑 -->
    <template v-if="hasOneShowingChild(item, item.children) && (!onlyOneChild.children || onlyOneChild.noShowingChildren) && !item.alwaysShow">
      <app-link v-if="onlyOneChild.meta" :to="resolvePath(onlyOneChild.path, props.basePath, onlyOneChild.query)">
        <el-menu-item :index="resolvePath(onlyOneChild.path, props.basePath)">
          <!-- <svg-icon v-if="onlyOneChild.meta.icon" :icon-class="onlyOneChild.meta.icon || (item.meta && item.meta.icon)" style="margin-right: 4px" /> -->
          <template #title>
            <span class="menu-title">{{ onlyOneChild.meta.title }}</span>
          </template>
        </el-menu-item>
      </app-link>
    </template>

    <!-- 多个子菜单的情况 - 使用el-sub-menu -->
    <el-sub-menu v-else :index="resolvePath(item.path, props.basePath)" popper-class="horizontal-menu-popper">
      <template v-if="item.meta" #title>
        {{ item.meta?.title }}
        <!-- <svg-icon v-if="item.meta.icon" :icon-class="item.meta ? item.meta.icon : ''" style="margin-right: 4px" /> -->
        <!-- <span class="new-menu-title" :title="hasTitle(item.meta?.title)">{{ item.meta?.title }}</span> -->
      </template>

      <!-- 递归渲染子菜单项 -->

      <second-menu-item
        v-for="(child, index) in item.children"
        :key="child.path + index"
        :is-nest="true"
        :item="child"
        :base-path="child.path"
        class="nest-menu"
      />
    </el-sub-menu>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { RouteRecordRaw } from 'vue-router';
import { useHorizontalMenu } from './useHorizontalMenu';
import AppLink from './Link.vue';
import SecondMenuItem from './SecondMenuItem.vue';
// 定义Props - 基于SidebarItem的Props结构
const props = defineProps({
  item: {
    type: Object as PropType<RouteRecordRaw>,
    required: true
  },
  isNest: {
    type: Boolean,
    default: false
  },
  basePath: {
    type: String,
    default: ''
  }
});

// 使用公共菜单逻辑组合函数
const { resolvePath, hasTitle } = useHorizontalMenu();

// 存储唯一显示的子项 - 复用SidebarItem逻辑
const onlyOneChild = ref<any>({});

// 本地实现hasOneShowingChild以支持onlyOneChild的设置
const hasOneShowingChild = (parent: RouteRecordRaw, children?: RouteRecordRaw[]) => {
  if (!children) {
    children = [];
  }
  const showingChildren = children.filter((item) => {
    if (item.hidden) {
      return false;
    }
    onlyOneChild.value = item;
    return true;
  });

  // When there is only one child router, the child router is displayed by default
  if (showingChildren.length === 1) {
    return true;
  }

  // Show parent if there are no child router to display
  if (showingChildren.length === 0) {
    onlyOneChild.value = { ...parent, path: '', noShowingChildren: true };
    return true;
  }

  return false;
};
</script>

<style lang="scss">
// 菜单项样式由主样式文件控制
.menu-title {
  display: inline-block;
}

.nest-menu {
  // 嵌套菜单的额外样式
}
</style>
