<template>
  <div ref="menuRef" style="width: 100%">
    <el-menu :default-active="activeMenu" mode="horizontal" :ellipsis="false" class="horizontal-menu-container">
      <!-- 可见菜单项 - 基于TopNav的显示逻辑 -->
      <template v-for="(item, index) in defaultMenuRoutes" :key="item.path + index">
        <horizontal-menu-item v-if="index < visibleNumber" :item="item" :base-path="item.path" class="horizontal-menu-item" />
      </template>

      <!-- 更多菜单 - 基于TopNav的折叠逻辑 -->
      <el-sub-menu v-if="defaultMenuRoutes.length > visibleNumber" index="more" popper-class="horizontal-menu-popper">
        <template #title>更多菜单</template>
        <template v-for="(item, index) in defaultMenuRoutes" :key="`more-${item.path}-${index}`">
          <horizontal-menu-item v-if="index >= visibleNumber" :item="item" :base-path="item.path" class="horizontal-menu-item-more" />
        </template>
      </el-sub-menu>
    </el-menu>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue';
import { RouteRecordRaw } from 'vue-router';
import { useHorizontalMenu } from './useHorizontalMenu';
import { useResponsiveMenu } from './useResponsiveMenu';
import HorizontalMenuItem from './HorizontalMenuItem.vue';
import { MenuItemClicked } from 'element-plus';

// 定义Props - 支持自定义配置
interface Props {
  // 是否启用响应式布局
  responsive?: boolean;
  // 最小可见菜单项数量
  minVisible?: number;
  // 自定义菜单数据（可选，默认使用权限系统数据）
  customMenus?: RouteRecordRaw[];
  // 是否显示图标
  showIcon?: boolean;
  // 自定义样式类名
  customClass?: string;
}

const props = withDefaults(defineProps<Props>(), {
  responsive: true,
  minVisible: 3,
  customMenus: undefined,
  showIcon: false,
  customClass: ''
});

// 定义Emits
interface Emits {
  (e: 'select', key: string): void;
  (e: 'menu-change', visibleCount: number, totalCount: number): void;
}

const emit = defineEmits<Emits>();

// 使用公共菜单逻辑组合函数
const { menuRoutes: defaultMenuRoutes, navigate, activeMenu, isActive, isActiveParent } = useHorizontalMenu();

// 使用响应式菜单逻辑组合函数
const { menuRef, visibleNumber } = useResponsiveMenu();

// 菜单数据 - 支持自定义菜单或使用默认权限数据
const menuRoutes = computed(() => {
  return props.customMenus || defaultMenuRoutes.value;
});

// 组件挂载后的初始化
onMounted(() => {});

// 暴露方法供父组件调用
defineExpose({
  // 菜单数据
  menuRoutes,

  // 状态
  activeMenu,
  visibleNumber,

  // 方法
  navigate,
  isActive,
  isActiveParent,

  // 响应式控制
  shouldShowMoreMenu: () => shouldShowMoreMenu(menuRoutes.value.length),

  // 容器引用
  menuRef
});
</script>

<style lang="scss">
// 导入样式文件
@use './el-menu-styles.scss';
@use './styles.scss';

// 组件特定样式
.horizontal-menu-item {
  // 菜单项样式由样式文件控制
}

.horizontal-menu-item-more {
  // 更多菜单中的菜单项样式
}

// 支持自定义样式类名
.horizontal-menu-container {
  width: 100%;
  &.custom-horizontal-menu {
    // 自定义样式可以在这里扩展
  }
}
</style>
