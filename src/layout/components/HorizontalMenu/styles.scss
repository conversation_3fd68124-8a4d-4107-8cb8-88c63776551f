// HorizontalMenu 横向菜单样式系统
// 基于 CommonHeader nav-menu 样式，确保视觉一致性

// 主容器样式
.horizontal-menu {
  font-family: 'CustomFont';
  flex: 1;
  z-index: 1;
  min-width: 0;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-right: 48px;

  ul {
    display: flex;
    gap: 8px;
    list-style: none;
    padding: 0;
    margin: 0;
    flex-direction: row;
    justify-content: flex-start;
  }
}

// 菜单项样式
.nav-item {
  position: relative;
  display: flex;
  align-items: center;
}

// 菜单链接样式
.nav-link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: #d0d8e2;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  padding: 12px 18px;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  // 悬停光效
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
  }

  &:hover::before {
    left: 100%;
  }

  // 悬停和激活状态
  &:hover,
  &.active {
    background: linear-gradient(135deg, #0a8494 0%, #0ea5e9 100%);
    color: #ffffff;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(10, 132, 148, 0.3);
  }
}

// 下拉图标样式
.dropdown-icon {
  margin-left: 6px;
  font-size: 12px;
  transition: transform 0.3s ease;

  .nav-link:hover & {
    transform: rotate(180deg);
  }
}

// 子菜单入口样式
.submenu-entry {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

// 更多图标样式
.more-icon {
  margin-left: 8px;
}

// Element-Plus 下拉菜单全局样式
:global(.nav-dropdown-popper) {
  font-family: 'CustomFont';
  background: rgba(0, 26, 46, 0.95) !important;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(10, 132, 148, 0.3) !important;
  border-radius: 8px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
  padding: 8px 0 !important;

  .el-dropdown-menu {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    padding: 0 !important;
  }

  .el-dropdown-menu__item {
    color: #d0d8e2 !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    padding: 12px 20px !important;
    margin: 2px 8px !important;
    border-radius: 6px !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    position: relative !important;
    overflow: hidden !important;

    // 悬停光效
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
      transition: left 0.5s;
    }

    &:hover::before {
      left: 100%;
    }

    &:hover {
      background: linear-gradient(135deg, #0a8494 0%, #0ea5e9 100%) !important;
      color: #ffffff !important;
      transform: translateX(4px) !important;
    }

    &.active {
      background: linear-gradient(135deg, #0ea5e9 0%, #0a8494 100%) !important;
      color: #ffffff !important;
    }

    // 子菜单项样式
    &.submenu-entry {
      border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
      margin-bottom: 4px !important;
      padding-bottom: 8px !important;
    }

    &.submenu-child {
      padding-left: 32px !important;
      font-size: 13px !important;
      opacity: 0.9 !important;

      .child-indent {
        position: relative;

        &::before {
          content: '└';
          position: absolute;
          left: -16px;
          color: rgba(255, 255, 255, 0.5);
          font-size: 12px;
        }
      }

      &:hover {
        padding-left: 36px !important;
        transition: padding-left 0.2s ease !important;
      }
    }
  }
}

// 响应式支持
@media (max-width: 768px) {
  .horizontal-menu {
    margin-right: 16px;

    .nav-link {
      padding: 8px 12px;
      font-size: 14px;
    }
  }
}

@media (max-width: 480px) {
  .horizontal-menu {
    margin-right: 8px;

    ul {
      gap: 4px;
    }

    .nav-link {
      padding: 6px 8px;
      font-size: 13px;
    }
  }
} 