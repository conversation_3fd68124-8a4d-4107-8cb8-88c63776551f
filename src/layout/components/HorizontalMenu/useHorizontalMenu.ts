import { computed, ref, toRaw } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { usePermissionStore } from '@/store/modules/permission';
import { isExternal } from '@/utils/validate';
import { getNormalPath } from '@/utils/ruoyi';
import { RouteRecordRaw } from 'vue-router';

/**
 * 公共菜单逻辑组合函数，抽象Sidebar中的核心菜单逻辑
 * 包括路由数据获取、菜单过滤、路径解析等功能，确保与Sidebar组件的数据逻辑完全一致
 */
export function useHorizontalMenu() {
  const route = useRoute();
  const router = useRouter();
  const permissionStore = usePermissionStore();

  // 获取侧边栏路由数据，与Sidebar保持一致
  const menuRoutes = computed<RouteRecordRaw[]>(() => {
    const routes = toRaw(permissionStore.getTopbarRoutes());
    routes.unshift({
      path: '/index',
      name: 'home',
      meta: {
        title: '首页'
      },
      redirect: 'noRedirect'
    });
    return routes;
  });

  // 复用SidebarItem中的hasOneShowingChild逻辑
  const hasOneShowingChild = (parent: RouteRecordRaw, children?: RouteRecordRaw[]) => {
    const onlyOneChild = ref<any>({});

    if (!children) {
      children = [];
    }
    const showingChildren = children.filter((item) => {
      if (item.hidden) {
        return false;
      }
      onlyOneChild.value = item;
      return true;
    });

    // When there is only one child router, the child router is displayed by default
    if (showingChildren.length === 1) {
      return { hasOne: true, child: onlyOneChild.value };
    }

    // Show parent if there are no child router to display
    if (showingChildren.length === 0) {
      onlyOneChild.value = { ...parent, path: '', noShowingChildren: true };
      return { hasOne: true, child: onlyOneChild.value };
    }

    return { hasOne: false, child: null };
  };

  // 复用SidebarItem中的resolvePath逻辑
  const resolvePath = (routePath: string, basePath: string = '', routeQuery?: string): any => {
    if (isExternal(routePath)) {
      return routePath;
    }
    if (isExternal(basePath)) {
      return basePath;
    }
    if (routeQuery) {
      const query = JSON.parse(routeQuery);
      return { path: getNormalPath(basePath + '/' + routePath), query: query };
    }
    return getNormalPath(basePath + '/' + routePath);
  };

  // 复用SidebarItem中的hasTitle逻辑
  const hasTitle = (title: string | undefined): string => {
    if (!title || title.length <= 5) {
      return '';
    }
    return title;
  };

  // 路由导航处理，参考CommonHeader实现
  const navigate = (path: string) => {
    if (isExternal(path)) {
      // 外部链接新窗口打开
      window.open(path, '_blank');
    } else {
      // 内部路由跳转
      router.push(path);
    }
  };

  // 活跃菜单计算逻辑
  const activeMenu = computed(() => {
    const { meta, path } = route;
    // 如果设置了activeMenu，直接使用
    if (meta?.activeMenu) {
      return meta.activeMenu as string;
    }
    return path;
  });

  // 判断菜单是否激活
  const isActive = (menuPath: string): boolean => {
    return activeMenu.value === menuPath;
  };

  // 判断父级菜单是否激活（有子菜单的情况）
  const isActiveParent = (menuItem: RouteRecordRaw): boolean => {
    if (!menuItem.children || menuItem.children.length === 0) {
      return false;
    }

    return menuItem.children.some((child) => {
      const childPath = resolvePath(child.path, menuItem.path);
      return isActive(typeof childPath === 'string' ? childPath : childPath.path);
    });
  };

  return {
    // 数据源
    menuRoutes,

    // 核心逻辑函数
    hasOneShowingChild,
    resolvePath,
    hasTitle,

    // 导航和状态
    navigate,
    activeMenu,
    isActive,
    isActiveParent
  };
}
