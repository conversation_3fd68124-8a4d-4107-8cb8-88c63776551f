import { ref, onMounted, onBeforeUnmount } from 'vue';
import { debounce } from '@/utils';
import { useRouter } from 'vue-router';
import { usePermissionStore } from '@/store/modules/permission';

/**
 * 响应式菜单逻辑组合函数
 * 直接复用TopNav组件的响应式布局系统，包括visibleNumber机制、setVisibleNumber函数、ResizeObserver监听等
 * 当菜单项超出容器宽度时使用el-sub-menu实现更多菜单功能
 */
export function useResponsiveMenu() {
  // 顶部栏初始数 - 复用TopNav的visibleNumber机制
  const visibleNumber = ref<number>(-1);
  const router = useRouter();
  // 菜单容器引用
  const menuRef = ref<HTMLElement>();

  const permissionStore = usePermissionStore();

  const allMenusLength = computed(() => {
    // 1是首页，默认添加的
    return permissionStore.getTopbarRoutes().length + 1;
  });

  /** 更新可见菜单项数量 */
  const updateVisibleNumber = () => {
    if (!menuRef.value) return;

    const containerWidth = menuRef.value.offsetWidth;
    const avgItemWidth = 86; // 每个菜单项的平均宽度 (px)
    const moreMenuWidth = 86; // "更多" 菜单的宽度 (px)

    if (allMenusLength.value * avgItemWidth <= containerWidth) {
      visibleNumber.value = allMenusLength.value;
    } else {
      const availableWidth = containerWidth - moreMenuWidth;
      const count = Math.floor(availableWidth / avgItemWidth);
      visibleNumber.value = Math.max(0, count);
    }
  };

  /** 防抖版本的updateVisibleNumber函数 */
  const debouncedUpdateVisibleNumber = debounce(updateVisibleNumber, 50, true);

  onMounted(() => {
    // 使用 nextTick 确保 DOM 渲染完成
    router.isReady().then(() => {
      updateVisibleNumber();
    });

    // 使用防抖版本的resize事件监听
    window.addEventListener('resize', debouncedUpdateVisibleNumber);
  });

  onBeforeUnmount(() => {
    // 清理resize事件监听（防抖版本）
    window.removeEventListener('resize', debouncedUpdateVisibleNumber);
  });
  return {
    // 核心状态
    visibleNumber,
    menuRef
  };
}
