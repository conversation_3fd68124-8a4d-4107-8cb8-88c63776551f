<template>
  <div v-if="!item.hidden">
    <!-- 单个菜单项或只有一个显示子项的情况 - 基于SidebarItem逻辑 -->
    <template v-if="hasOneShowingChild(item, item.children) && (!onlyOneChild.children || onlyOneChild.noShowingChildren) && !item.alwaysShow">
      <app-link v-if="onlyOneChild.meta" :to="resolvePath(onlyOneChild.path, props.basePath, onlyOneChild.query)">
        <el-menu-item :index="resolvePath(onlyOneChild.path, props.basePath)">
          <svg-icon v-if="onlyOneChild.meta.icon" :icon-class="onlyOneChild.meta.icon || (item.meta && item.meta.icon)" style="margin-right: 4px" />
          <template #title>
            <span class="menu-title">{{ onlyOneChild.meta.title }}</span>
          </template>
        </el-menu-item>
      </app-link>
    </template>

    <!-- 多个子菜单的情况 - 跳转到其第一个能访问的子菜单-->
    <template v-else>
      <!-- 如果找到了可访问的路由，显示可点击的菜单项 -->
      <template v-if="firstAccessibleRoute">
        <app-link :to="firstAccessibleRoute.to">
          <el-menu-item :index="firstAccessibleRoute.path">
            <template v-if="item.meta" #title>
              <svg-icon v-if="item.meta.icon" :icon-class="item.meta ? item.meta.icon : ''" style="margin-right: 4px" />
              <span class="menu-title" :title="hasTitle(item.meta?.title)">{{ item.meta?.title }}</span>
            </template>
          </el-menu-item>
        </app-link>
      </template>
      <!-- 如果没有找到可访问的路由，则显示普通菜单项（不可点击） -->
      <template v-else>
        <el-menu-item :index="resolvePath(item.path, props.basePath)" disabled>
          <template v-if="item.meta" #title>
            <svg-icon v-if="item.meta.icon" :icon-class="item.meta ? item.meta.icon : ''" style="margin-right: 4px" />
            <span class="menu-title" :title="hasTitle(item.meta?.title)">{{ item.meta?.title }}</span>
          </template>
        </el-menu-item>
      </template>
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { RouteRecordRaw } from 'vue-router';
import { useHorizontalMenu } from './useHorizontalMenu';
import AppLink from './Link.vue';

// 定义Props - 基于SidebarItem的Props结构
const props = defineProps({
  item: {
    type: Object as PropType<RouteRecordRaw>,
    required: true
  },
  isNest: {
    type: Boolean,
    default: false
  },
  basePath: {
    type: String,
    default: ''
  }
});

// 使用公共菜单逻辑组合函数
const { resolvePath, hasTitle } = useHorizontalMenu();

// 存储唯一显示的子项 - 复用SidebarItem逻辑
const onlyOneChild = ref<any>({});

// 本地实现hasOneShowingChild以支持onlyOneChild的设置
const hasOneShowingChild = (parent: RouteRecordRaw, children?: RouteRecordRaw[]) => {
  if (!children) {
    children = [];
  }
  const showingChildren = children.filter((item) => {
    if (item.hidden) {
      return false;
    }
    onlyOneChild.value = item;
    return true;
  });

  // When there is only one child router, the child router is displayed by default
  if (showingChildren.length === 1) {
    return true;
  }

  // Show parent if there are no child router to display
  if (showingChildren.length === 0) {
    onlyOneChild.value = { ...parent, path: '', noShowingChildren: true };
    return true;
  }

  return false;
};

/**
 * 获取第一个可访问的路由链接
 * 模仿 hasOneShowingChild 的方式，递归查找第一个可访问的具体页面
 */
const findFirstAccessibleRoute = (routeItem: RouteRecordRaw, basePath: string = ''): { path: string; to: any; query?: string } | null => {
  // 如果路由被隐藏，跳过

  if (routeItem.hidden === true) {
    return null;
  }

  // 如果有重定向，优先使用重定向路径
  if (routeItem.redirect && routeItem.redirect !== 'noRedirect') {
    const redirectPath = String(routeItem.redirect);
    return {
      path: redirectPath,
      to: redirectPath,
      query: routeItem.query
    };
  }

  // 如果没有子路由或子路由为空，当前路由就是目标页面
  if (!routeItem.children || routeItem.children.length === 0) {
    // 直接使用路由项的路径，不再通过 resolvePath 进行拼接
    const routePath = resolvePath(routeItem.path, basePath);
    const routeTo = routeItem.query ? { name: routeItem.name, query: JSON.parse(routeItem.query) } : routePath;

    return {
      path: routePath,
      to: routeTo,
      query: routeItem.query
    };
  }

  // 如果有子路由，递归查找第一个可访问的页面
  for (const child of routeItem.children) {
    // 跳过隐藏的子路由
    if (child.hidden === true) {
      continue;
    }

    // 递归查找子路由中的第一个可访问页面，不传递basePath以避免多层嵌套
    const childRoute = findFirstAccessibleRoute(child, props.basePath);
    if (childRoute) {
      return childRoute;
    }
  }

  // 如果没有找到任何可访问的子页面，返回null
  return null;
};

// 计算第一个可访问的路由
const firstAccessibleRoute = computed(() => {
  // 如果当前项目符合 hasOneShowingChild 条件，则不需要额外处理
  if (
    hasOneShowingChild(props.item, props.item.children) &&
    (!onlyOneChild.value.children || onlyOneChild.value.noShowingChildren) &&
    !props.item.alwaysShow
  ) {
    return null;
  }

  // 否则查找第一个可访问的路由，不使用 basePath 避免重复拼接
  return findFirstAccessibleRoute(props.item, '');
});
</script>

<style lang="scss">
// 菜单项样式由主样式文件控制
.menu-title {
  display: inline-block;
}

.nest-menu {
  // 嵌套菜单的额外样式
}
</style>
