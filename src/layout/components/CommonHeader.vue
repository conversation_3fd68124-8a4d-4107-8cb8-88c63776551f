<template>
  <header class="common-header">
    <!-- 左侧：Logo + 标题 -->
    <div class="left-area w-112.5 xl:w-112.5 2xl:w-125" @click="goHome">
      <img :src="Logo" alt="logo" class="logo" />
      <span class="title">北投低空一网统飞平台</span>
    </div>

    <!-- 中部：导航菜单 -->
    <nav class="nav-menu" style="display: none" ref="navMenuRef" :active-menu="activeMenu">
      <ul>
        <template v-for="(item, index) in navItems">
          <li v-if="index < visibleNumber" :key="item.path" class="nav-item">
            <!-- 有子菜单的项目使用下拉菜单 -->
            <el-dropdown
              v-if="item.children && item.children.length > 0"
              trigger="hover"
              placement="bottom-start"
              popper-class="nav-dropdown-popper"
              @command="navigate"
            >
              <span class="nav-link" :class="{ active: isActiveParent(item) }">
                {{ item.meta.title }}
                <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item v-for="child in item.children" :key="child.path" :command="child.path" :class="{ active: isActive(child.path) }">
                    {{ child.meta.title }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>

            <!-- 无子菜单的项目直接点击 -->
            <span v-else class="nav-link" :class="{ active: isActive(item.path) }" @click="navigate(item.path)">
              {{ item.meta.title }}
            </span>
          </li>
        </template>

        <!-- 超出部分的 "更多" 菜单 -->
        <li v-if="navItems.length > visibleNumber" class="nav-item">
          <el-dropdown trigger="hover" placement="bottom-start" popper-class="nav-dropdown-popper" @command="handleMoreMenuCommand">
            <span class="nav-link">
              更多
              <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <template v-for="(item, index) in navItems" :key="`more-${item.path}-${index}`">
                  <template v-if="index >= visibleNumber">
                    <!-- 在 "更多" 中有子菜单的项 -->
                    <template v-if="item.children && item.children.length > 0">
                      <el-dropdown-item :class="{ active: isActiveParent(item) }" class="submenu-entry" :command="`parent:${item.path}`">
                        <span>{{ item.meta.title }}</span>
                        <el-icon class="more-icon"><ArrowRight /></el-icon>
                      </el-dropdown-item>
                    </template>
                    <!-- 在 "更多" 中无子菜单的项 -->
                    <el-dropdown-item v-else :command="item.path" :class="{ active: isActive(item.path) }">
                      {{ item.meta.title }}
                    </el-dropdown-item>
                  </template>
                </template>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </li>
      </ul>
    </nav>
    <nav class="nav-menu">
      <HorizontalMenu />
    </nav>

    <!-- 右侧：环境/天气/时间信息 -->
    <div class="right-area">
      <div class="info-item aqi">
        <span class="label">空气指数</span>
        <span class="value">{{ airQuality }}</span>
      </div>
      <div class="divider"></div>
      <div class="info-item pm25">
        <span class="label">PM2.5</span>
        <span class="value">{{ pm25 }}</span>
      </div>
      <div class="divider"></div>
      <div class="info-item weather">
        <i class="iconfont icon-tianqi" />
        <span class="value">{{ temperature }}°</span>
      </div>
      <div class="divider"></div>
      <div class="info-item datetime">{{ currentDate }} {{ currentTime }}</div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { useRouter, useRoute, type RouteRecordRaw } from 'vue-router';
import { ArrowDown, ArrowRight } from '@element-plus/icons-vue';
import dayjs from 'dayjs';
import Logo from '@/assets/logo/jk-logo.png';
import { constantRoutes } from '@/router';
import { usePermissionStore } from '@/store/modules/permission';
import { isHttp } from '@/utils/validate';
import { debounce } from '@/utils';
import UnifiedLayout from '@/layout/UnifiedLayout.vue';
import ParentView from '@/components/ParentView/index.vue';
import { useAppStore } from '@/store/modules/app';
import HorizontalMenu from './HorizontalMenu/index.vue';
const router = useRouter();
const route = useRoute();
const permissionStore = usePermissionStore();
const appStore = useAppStore();

/** 导航菜单项接口 */
interface NavItem {
  name: string;
  path: string;
  meta?: object;
  hidden?: boolean;
  redirect?: string;
  component?: string;
  children?: NavItem[];
}

/**
 * 根据路由数据格式寻找某个父级路由下第一个可以访问的具体页面
 * @param routeItem 路由项目，包含你提供的数据格式
 * @param basePath 基础路径，用于拼接相对路径
 * @returns 返回第一个可访问页面的完整路由对象，如果没有找到则返回null
 */
function findFirstAccessiblePage(routeItem: any, basePath: string = ''): any | null {
  // 如果路由被隐藏，跳过
  if (routeItem.hidden === true) {
    return null;
  }

  // 计算当前路由的完整路径
  const currentPath = basePath ? `${basePath.replace(/\/$/, '')}/${routeItem.path}` : routeItem.path;

  // 如果有redirect，返回带有redirect信息的路由对象
  if (routeItem.redirect && routeItem.redirect !== 'noRedirect') {
    return {
      ...routeItem,
      fullPath: currentPath,
      redirectPath: routeItem.redirect
    };
  }

  // 判断是否为容器组件（UnifiedLayout 或 ParentView）
  const isContainer = (item: any) => {
    if (!item.component) return false;
    // The component name check is a heuristic based on the project's conventions.
    return item.component === UnifiedLayout || item.component === ParentView;
  };

  // 如果没有子路由或子路由为空，且不是容器组件，则当前路由就是目标页面
  if (!routeItem.children || routeItem.children.length === 0) {
    if (!isContainer(routeItem)) {
      return {
        ...routeItem,
        fullPath: currentPath
      };
    }
    // 如果是容器组件但没有子路由，返回null
    return null;
  }

  // 如果有子路由，递归查找第一个可访问的页面
  for (const child of routeItem.children) {
    // 跳过隐藏的子路由
    if (child.hidden === true) {
      continue;
    }

    // 递归查找子路由中的第一个可访问页面
    const childRoute = findFirstAccessiblePage(child, currentPath);
    if (childRoute) {
      return childRoute;
    }
  }

  // 如果没有找到任何可访问的子页面，返回null
  return null;
}

/**
 * 便捷函数：直接根据路由路径查找第一个可访问页面
 * @param routePath 路由路径，如 '/Traffic'
 * @returns 返回第一个可访问页面的完整路径，如果没有找到则返回null
 */
function findFirstAccessiblePageByPath(routePath: string): any {
  const targetRoute = topbarRoutes.value.find((route) => route.path === routePath);
  if (!targetRoute) {
    console.warn(`未找到路径为 ${routePath} 的路由`);
    return null;
  }

  return findFirstAccessiblePage(targetRoute);
}

const findFirstAccessiblePageBySecondMenu = (routePath: string): any => {
  const targetRoute = childrenMenus.value.find((route) => route.path === routePath);
  if (!targetRoute) {
    console.warn(`未找到路径为 ${routePath} 的路由`);
    return null;
  }
  return findFirstAccessiblePage(targetRoute);
};
const topbarRoutes = computed(() => {
  const useData = toRaw(permissionStore.getTopbarRoutes());
  useData.map((router) => {
    router.children?.forEach((item) => {
      if (item.parentPath === undefined) {
        if (router.path === '/') {
          item.path = '/' + item.path;
        } else {
          if (!isHttp(item.path)) {
            item.path = router.path + '/' + item.path;
          }
        }
        item.parentPath = router.path;
      }
    });
  });
  return useData;
});

const navItems = computed<NavItem[]>(() => {
  const topMenus: RouteRecordRaw[] = [];
  topbarRoutes.value.forEach((menu) => {
    if (menu.hidden !== true) {
      // 兼容顶部栏一级菜单内部跳转
      if (menu.path === '/' && menu.children) {
        topMenus.push(menu.children ? menu.children[0] : menu);
      } else {
        topMenus.push(menu);
      }
    }
  });
  topMenus.unshift({
    name: 'homeIndex',
    path: '/index',
    meta: {
      title: '首页'
    },
    component: () => {}
  });
  console.log('顶部渲染菜单', topMenus);
  return topMenus;
});

// 设置子路由
const childrenMenus = computed(() => {
  console.log('trigger子路由');
  const childrenMenus: RouteRecordRaw[] = [];
  const useData = toRaw(topbarRoutes.value);
  useData.map((router) => {
    router.children?.forEach((item) => {
      if (item.parentPath === undefined) {
        if (router.path === '/') {
          item.path = '/' + item.path;
        } else {
          if (!isHttp(item.path)) {
            item.path = router.path + '/' + item.path;
          }
        }
        item.parentPath = router.path;
      }
      childrenMenus.push(item);
    });
  });
  return constantRoutes.concat(childrenMenus);
});

/** 以下逻辑用于动态渲染后台页面的左侧菜单*/

// 隐藏侧边栏路由
const hideList = ['/index', '/user/profile'];

/**
 * @description: 根据当前路径递归查找其父级菜单对象
 * @param {RouteRecordRaw[]} menus - 当前遍历的菜单数组
 * @param {string} path - 目标路径
 * @param {RouteRecordRaw | null} parentMenu - 当前菜单项的父级菜单，初始调用时可不传或传null
 * @return {RouteRecordRaw | null} - 匹配到的父级菜单对象 (深拷贝)，未找到则返回 null
 */
function findParentMenuByPath(menus: RouteRecordRaw[], path: string, parentMenu: any): RouteRecordRaw | null {
  for (const menu of menus) {
    if (menu.name === path) {
      // 如果直接在当前层级找到目标路径，则返回其父级菜单
      // 注意：如果目标路径是一级菜单，parentMenu会是null，按需处理或返回
      return parentMenu ? parentMenu : null;
    }
    if (menu.children && menu.children.length > 0) {
      // 如果当前菜单有子菜单，则递归查找子菜单
      // 在递归调用时，将当前菜单作为父菜单传递下去
      const foundParent = findParentMenuByPath(menu.children, path, menu);
      if (foundParent) {
        // 如果在子菜单中找到了目标路径的父菜单，则直接返回
        return foundParent; // 注意：这里返回的是子调用中深拷贝过的父菜单
      }
    }
  }

  // 遍历完所有菜单项及其子菜单后仍未找到，则返回 null
  return null;
}

const activeRoutes = (key: any) => {
  let routes;
  if (childrenMenus.value && childrenMenus.value.length > 0) {
    routes = childrenMenus.value.find((item) => item.path === key);
  }
  if (routes) {
    permissionStore.setSidebarRouters([routes]);
  } else {
    appStore.toggleSideBarHide(true);
  }
  return routes;
};

// // 默认激活的菜单
const activeMenu = computed(() => {
  let path = route.path;
  if (path === '/index') {
    path = '/system/user';
  }
  let activePath = path;
  if (path !== undefined && path.lastIndexOf('/') > 0 && hideList.indexOf(path) === -1) {
    const tmpPath = path.substring(1, path.length);
    if (!route.meta.link) {
      const list = tmpPath.split('/');
      if (list.length > 2) {
        activePath = '/' + [list[0], list[1]].join('/');
      } else {
        activePath = '/' + tmpPath.substring(0, tmpPath.indexOf('/'));
      }
      appStore.toggleSideBarHide(false);
    }
  } else if (!route.children) {
    activePath = path;
    appStore.toggleSideBarHide(true);
  }
  activeRoutes(activePath);
  return activePath;
});

/** 动态计算可见菜单项 */
const navMenuRef = ref<HTMLElement | null>(null);
const visibleNumber = ref(-1); // 初始值足够大，避免闪烁

const updateVisibleNumber = () => {
  if (!navMenuRef.value) return;

  const containerWidth = navMenuRef.value.offsetWidth;
  const avgItemWidth = 86; // 每个菜单项的平均宽度 (px)
  const moreMenuWidth = 86; // "更多" 菜单的宽度 (px)

  if (navItems.value.length * avgItemWidth <= containerWidth) {
    visibleNumber.value = navItems.value.length;
  } else {
    const availableWidth = containerWidth - moreMenuWidth;
    const count = Math.floor(availableWidth / avgItemWidth);
    visibleNumber.value = Math.max(0, count);
  }
};

/** 防抖版本的updateVisibleNumber函数 */
const debouncedUpdateVisibleNumber = debounce(updateVisibleNumber, 50, true);

/** 环境信息模拟，可替换为实际接口数据 */
const airQuality = ref(85);
const pm25 = ref(30);
const temperature = ref(22);

/** 日期时间 */
const currentDate = ref(dayjs().format('YYYY.MM.DD'));
const currentTime = ref(dayjs().format('HH:mm'));

let timer: number | undefined;

onMounted(() => {
  // 使用 nextTick 确保 DOM 渲染完成
  router.isReady().then(() => {
    updateVisibleNumber();
  });

  // 使用防抖版本的resize事件监听
  window.addEventListener('resize', debouncedUpdateVisibleNumber);
  // 添加浏览器可见性变化监听

  timer = window.setInterval(() => {
    currentDate.value = dayjs().format('YYYY.MM.DD');
    currentTime.value = dayjs().format('HH:mm');
  }, 1000 * 60); // 每分钟刷新一次
});

onUnmounted(() => {
  // 清理resize事件监听（防抖版本）
  window.removeEventListener('resize', debouncedUpdateVisibleNumber);
  // 清理浏览器可见性变化监听
  // 清理定时器
  window.clearInterval(timer);
});

/**
 * 导航到指定路径
 * @param path 路径
 */
const navigate = (path: string) => {
  const route = topbarRoutes.value.find((item) => item.path === path);
  // 需要处理的边缘情况包括，topbarRoutes是一级菜单，childrenMenus是二级菜单

  // 1. 如果访问的是首页，则跳转首页
  if (path === '/index') {
    router.push({ path: '/index' });
    return;
  }
  if (!path) return;
  // 2. 如果访问的是http(s):// 路径，则在新窗口打开
  if (isHttp(path)) {
    window.open(path, '_blank');
  } else if (!route) {
    const secondMenuRoute = findFirstAccessiblePageBySecondMenu(path);
    if (secondMenuRoute) {
      // 3. 如果访问页面不在一级路由中，且能在二级菜单路由下找到第一个可访问页面，记得携带query参数补全
      if (secondMenuRoute?.query) {
        const query = JSON.parse(secondMenuRoute.query);
        router.push({ name: secondMenuRoute.name, query: query });
      } else {
        router.push({ name: secondMenuRoute.name });
      }
    } else {
      // 4. 如果访问页面不在一级路由中，且不能在二级菜单路由下找到第一个可访问页面，则直接跳转(属于是二级目录，且还没注册页面)
      router.push(path);
    }
    // if (routeMenu.children && routeMenu.children.length > 0) {
    //   if (routeMenu.children[0].query) {
    //     const query = JSON.parse(routeMenu.children[0].query);
    //     router.push({ name: routeMenu.children[0].name, query: query });
    //   } else {
    //     router.push({ name: routeMenu.children[0].name });
    //   }
    // } else if (routeMenu.query) {
    //   const query = JSON.parse(routeMenu.query);
    //   router.push({ path: path, query: query });
    // } else {
    //   router.push({ path: path });
    // }
  } else {
    // 4. 如果访问的是没有子路由的路由，则直接跳转（等同于一级菜单是具体页面链接）
    router.push(path);
  }
};

/** 处理"更多"菜单的命令事件 */
const handleMoreMenuCommand = (command: string) => {
  // 如果是父级菜单项（带有parent:前缀），导航到该菜单下第一个能访问的页面
  // TODO 存在待解决的问题，在更多下一级菜单就没办法选中二级菜单了
  if (command.startsWith('parent:')) {
    // debugger;
    const parentPath = command.replace('parent:', '');
    // router.push(parentPath);
    const firstAccessiblePage = findFirstAccessiblePageByPath(parentPath);
    if (firstAccessiblePage.query) {
      const query = JSON.parse(firstAccessiblePage.query);
      router.push({ name: firstAccessiblePage.name, query: query });
    } else {
      router.push({ name: firstAccessiblePage.name });
    }
    return;
  }
  // 否则正常导航
  navigate(command);
};

const isActive = (path: string) => {
  return route.path === path;
};

/** 判断父级菜单是否激活（当前路由匹配父级或子级路径时） */
const isActiveParent = (item: NavItem) => {
  if (route.path === item.path) return true;
  if (item.children) {
    return item.children.some((child) => route.path === child.path);
  }
  return false;
};

const goHome = () => navigate('/index');
</script>

<style scoped>
.common-header {
  height: 90px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-image: url('@/assets/images/header-bg.png');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  padding: 0 24px;
  box-sizing: border-box;
  user-select: none;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 26, 46, 0.3);
}

.common-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('@/assets/images/header-bg.png') no-repeat center/cover;
  opacity: 0.1;
  pointer-events: none;
}

@keyframes gradientShift {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.left-area {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: transform 0.2s ease;
  z-index: 1;
  /* min-width: 400px;
  max-width: 400px; */
  flex-shrink: 0;
}

.left-area:hover {
  transform: scale(1.02);
}

.logo {
  width: 30px;
  height: 40px;
  margin-right: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title {
  font-family: 'CustomFont';
  font-size: 34px;
  font-weight: 400;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  letter-spacing: 0.5px;
}

.nav-menu {
  font-family: 'CustomFont';
  flex: 1;
  z-index: 1;
  min-width: 0;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-right: 48px;
}

.nav-menu ul {
  display: flex;
  gap: 8px;
  list-style: none;
  padding: 0;
  margin: 0;
  flex-direction: row;
  justify-content: flex-start;
}

.nav-item {
  position: relative;
  display: flex;
  align-items: center;
}

.nav-link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: #d0d8e2;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  padding: 12px 18px;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.nav-link:hover::before {
  left: 100%;
}

.nav-link:hover,
.nav-link.active {
  background: linear-gradient(135deg, #0a8494 0%, #0ea5e9 100%);
  color: #ffffff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(10, 132, 148, 0.3);
}

.dropdown-icon {
  margin-left: 6px;
  font-size: 12px;
  transition: transform 0.3s ease;
}

.nav-link:hover .dropdown-icon {
  transform: rotate(180deg);
}

.submenu-entry {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.more-icon {
  margin-left: 8px;
}

.right-area {
  display: flex;
  align-items: center;
  gap: 0;
  color: #ffffff;
  font-size: 14px;
  z-index: 1;
}

.info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 16px;
  transition: all 0.3s ease;
}

.info-item:hover {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  transform: translateY(-1px);
}

.info-item .label {
  font-size: 12px;
  opacity: 0.8;
  margin-bottom: 2px;
  color: #b0c4de;
}

.info-item .value {
  font-weight: 600;
  font-size: 16px;
  color: #ffffff;
}

.divider {
  width: 1px;
  height: 32px;
  background: linear-gradient(to bottom, transparent, rgba(255, 255, 255, 0.3), transparent);
  margin: 0 4px;
}

.weather {
  flex-direction: row !important;
  gap: 6px;
}

.iconfont {
  font-size: 20px;
  color: #0ea5e9;
}

.datetime {
  min-width: 120px;
  text-align: center;
  font-family: 'Courier New', monospace;
  font-weight: 600;
  letter-spacing: 0.5px;
}
</style>

<!-- Element-Plus 下拉菜单全局样式 -->
<style>
.nav-dropdown-popper {
  font-family: 'CustomFont';
  background: rgba(0, 26, 46, 0.95) !important;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(10, 132, 148, 0.3) !important;
  border-radius: 8px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
  padding: 8px 0 !important;
}

.nav-dropdown-popper .el-dropdown-menu {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
}

.nav-dropdown-popper .el-dropdown-menu__item {
  color: #d0d8e2 !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  padding: 12px 20px !important;
  margin: 2px 8px !important;
  border-radius: 6px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
  overflow: hidden !important;
}

.nav-dropdown-popper .el-dropdown-menu__item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.nav-dropdown-popper .el-dropdown-menu__item:hover::before {
  left: 100%;
}

.nav-dropdown-popper .el-dropdown-menu__item:hover {
  background: linear-gradient(135deg, #0a8494 0%, #0ea5e9 100%) !important;
  color: #ffffff !important;
  transform: translateX(4px) !important;
}

.nav-dropdown-popper .el-dropdown-menu__item.active {
  background: linear-gradient(135deg, #0ea5e9 0%, #0a8494 100%) !important;
  color: #ffffff !important;
}

/* 子菜单项样式 */
.nav-dropdown-popper .el-dropdown-menu__item.submenu-entry {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
  margin-bottom: 4px !important;
  padding-bottom: 8px !important;
}

.nav-dropdown-popper .el-dropdown-menu__item.submenu-child {
  padding-left: 32px !important;
  font-size: 13px !important;
  opacity: 0.9 !important;
}

.nav-dropdown-popper .el-dropdown-menu__item.submenu-child .child-indent {
  position: relative;
}

.nav-dropdown-popper .el-dropdown-menu__item.submenu-child .child-indent::before {
  content: '└';
  position: absolute;
  left: -16px;
  color: rgba(255, 255, 255, 0.5);
  font-size: 12px;
}

.nav-dropdown-popper .el-dropdown-menu__item.submenu-child:hover {
  padding-left: 36px !important;
  transition: padding-left 0.2s ease !important;
}
</style>
