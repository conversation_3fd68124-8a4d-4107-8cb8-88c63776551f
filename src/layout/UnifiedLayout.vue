<template>
  <div class="unified-layout-wrapper">
    <!-- 当布局类型为 airspace 时展示公共头部 -->
    <CommonHeader />
    <!-- 主体包裹，使滚动仅发生在 router-view 区域 -->
    <div class="unified-layout-body">
      <component :is="layoutComponent" class="layout-content" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRoute } from 'vue-router';

// 原有后台布局
import AdminLayout from './backIndex.vue';
// 低空监控专用布局
import AirspaceLayout from './AirspaceLayout.vue';
// 公共头部组件
import { CommonHeader } from './components';

/**
 * 根据当前路由 meta.layoutType 返回对应的布局组件。
 * 默认使用 admin 布局。
 */
const route = useRoute();

const layoutType = computed(() => (route.meta?.layoutType as string | undefined) ?? 'admin');

const layoutComponent = computed(() => {
  switch (layoutType.value) {
    case 'airspace':
      return AirspaceLayout;
    case 'admin':
    default:
      return AdminLayout;
  }
});

/**
 * 当布局类型为 airspace 时，需要展示公共头部。
 */
const showCommonHeader = computed(() => layoutType.value === 'airspace');
</script>

<style scoped>
.unified-layout-wrapper {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 顶层容器不出现滚动条 */
}

/* 主体区域占满剩余高度，只允许内部滚动 */
.unified-layout-body {
  flex: 1;
  min-height: 0; /* 修复 flex 子项计算高度溢出导致无法滚动的问题 */
  overflow: auto;
  /* overflow: hidden; 避免外层滚动 */
}

/* 内部 layout 组件充满高度，内部自行管理滚动 */
.layout-content {
  height: 100%;
  width: 100%;
}

/* 针对 admin 布局的 .app-main 区域开启滚动，使得仅 router-view 区域滚动 */
:deep(.app-main) {
  overflow: auto !important;
}
</style>
