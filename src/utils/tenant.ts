import { getCurrentTenantInfo } from '@/api/system/tenant';
import { useUserStore } from '@/store/modules/user';

/**
 * 获取当前有效的租户ID
 * 如果用户切换了租户，返回切换后的租户ID；否则返回登录时的租户ID
 * @returns Promise<string> 当前有效的租户ID
 */
export async function getCurrentEffectiveTenantId(): Promise<string> {
    try {
        const userStore = useUserStore();

        // 调用API获取当前租户信息
        const { data: tenantInfo } = await getCurrentTenantInfo();

        // 如果处于动态切换状态且有切换的租户ID，使用切换后的租户ID
        if (tenantInfo.isDynamic && tenantInfo.dynamicTenantId) {
            return String(tenantInfo.dynamicTenantId);
        }

        // 如果有原始租户ID，使用原始租户ID
        if (tenantInfo.originalTenantId) {
            return String(tenantInfo.originalTenantId);
        }

        // 降级处理：使用userStore中的租户ID
        return String(userStore.tenantId || '');
    } catch (error) {
        console.warn('获取当前有效租户ID失败，使用登录租户ID:', error);

        // 出错时降级处理：使用userStore中的租户ID
        const userStore = useUserStore();
        return String(userStore.tenantId || '');
    }
}

/**
 * 获取当前有效的租户信息（包含ID和名称）
 * @returns Promise<{tenantId: string, tenantName: string}> 当前有效的租户信息
 */
export async function getCurrentEffectiveTenantInfo(): Promise<{ tenantId: string, tenantName: string }> {
    try {
        const userStore = useUserStore();

        // 调用API获取当前租户信息
        const { data: tenantInfo } = await getCurrentTenantInfo();

        // 如果处于动态切换状态且有切换的租户ID，使用切换后的租户信息
        if (tenantInfo.isDynamic && tenantInfo.dynamicTenantId) {
            return {
                tenantId: String(tenantInfo.dynamicTenantId),
                tenantName: tenantInfo.currentTenantName || '切换租户'
            };
        }

        // 使用原始租户信息
        return {
            tenantId: String(tenantInfo.originalTenantId || userStore.tenantId || ''),
            tenantName: tenantInfo.originalTenantName || tenantInfo.currentTenantName || '当前租户'
        };
    } catch (error) {
        console.warn('获取当前有效租户信息失败，使用登录租户信息:', error);

        // 出错时降级处理：使用userStore中的租户信息
        const userStore = useUserStore();
        return {
            tenantId: String(userStore.tenantId || ''),
            tenantName: '当前租户'
        };
    }
}