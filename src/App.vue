<template>
  <el-config-provider :locale="appStore.locale" :size="appStore.size">
    <router-view />
  </el-config-provider>
</template>

<script setup lang="ts">
import { useSettingsStore } from '@/store/modules/settings';
import { handleThemeStyle } from '@/utils/theme';
import { useAppStore } from '@/store/modules/app';

const appStore = useAppStore();

onMounted(() => {
  nextTick(() => {
    // 初始化主题样式
    handleThemeStyle(useSettingsStore().theme);
  });
});
</script>
<style lang="scss">
@font-face {
  font-family: 'CustomFont';
  src: url('/font/PANGMENZHENGDAOBIAOTITIMIANFEIBAN-2.TTF') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Source Han Sans CN';
  src: url('/font/SourceHanSansSC-Regular.otf') format('opentype');
  font-weight: normal;
  font-style: normal;
}
</style>
