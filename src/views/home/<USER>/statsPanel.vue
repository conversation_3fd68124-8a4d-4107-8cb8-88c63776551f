<template>
  <panelComponent width="501px" height="296px" :bg="panelBg" headerTop="18px">
    <template #header> 无人机设备监控 </template>
    <div class="stats-panel">
      <div class="pie-chart-container">
        <div ref="chartRef" class="pie-chart"></div>
        <div class="total-number">{{ totalNumber.toLocaleString() }}</div>
        <div class="total-label">全部数量</div>
      </div>
      <div class="status-list">
        <div v-for="item in chartData" :key="item.name" class="status-item">
          <span class="status-dot" :style="{ background: colorList[chartData.indexOf(item) % colorList.length], opacity: 1 }"></span>
          <span class="status-name">{{ item.name }}</span>
          <span class="status-value">{{ item.value }}</span>
          <span class="status-percent">{{ ((item.value / totalNumber) * 100).toFixed(0) }}%</span>
        </div>
      </div>
    </div>
  </panelComponent>
</template>

<script setup lang="ts">
import panelComponent from '@/views/home/<USER>/panelComponent.vue';
import panelBg from '@/assets/images/statsPanelPanelBg.png';
import { ref, nextTick, onMounted, onUnmounted, toRefs, getCurrentInstance } from 'vue';
import { flightDeviceStatistics } from '@/api/biz/flightDevice/index';
import * as echarts from 'echarts';
import type { ComponentInternalInstance } from 'vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { device_status } = toRefs<any>(proxy?.useDict('device_status'));

const chartRef = ref<HTMLElement | null>(null);
let chartInstance: echarts.ECharts | null = null;

// // 模拟数据
// const chartData = [
//   { name: '飞行中', value: 300 },
//   { name: '待机', value: 500 },
//   { name: '离线', value: 200 }
// ];

// 获取飞行统计信息
const chartData = ref([]);

let totalNumber = 0;

// 根据字典获取状态显示文本
const getStatusLabel = (statusValue: string): string => {
  if (!device_status.value || device_status.value.length === 0) {
    // 字典未加载时的默认显示
    switch (statusValue) {
      case 'true': return '在线';
      case 'false': return '离线';
      default: return '未知';
    }
  }
  
  // 使用字典查找对应的标签
  const dictItem = device_status.value.find((item: any) => item.value === statusValue);
  return dictItem ? dictItem.label : '未知';
};

async function fetchData() {
  chartData.value = [];
  await nextTick(); // 等待DOM更新
  const response = await flightDeviceStatistics();
  
  // 修复API响应数据处理
  const data = response.data || response.rows || response;
  
  chartData.value = data.map((item: any) => ({
    value: item.deviceCount,
    name: getStatusLabel(item.deviceStatus)
  }));
  
  // chartData 前插 飞行中
  chartData.value.unshift({ name: '飞行中', value: 0 });
  
  // 计算总数
  totalNumber = chartData.value.reduce((per, cur) => per + cur.value, 0);

  // 生成图表数据
  generateChartData();
}

const colorList = ['#19FCDE', '#0AF0FF', '#0AF0FF'];

// 饼图数据
let pieData1 = [];
let pieData2 = [];

// 生成图表数据
function generateChartData() {
  // 计算间隔
  const gap = (1 * totalNumber) / 50;
  const gapData = {
    name: '',
    value: gap,
    itemStyle: {
      color: 'transparent'
    }
  };

  // 重置数据
  pieData1 = [];
  pieData2 = [];

  for (let i = 0; i < chartData.value.length; i++) {
    // 第一圈数据
    if (chartData.value[i].name === '离线') {
      // 离线，特殊颜色和无透明度
      pieData1.push({
        ...chartData.value[i],
        itemStyle: {
          color: '#A9ACB0'
        }
      });
    } else {
      // 其他，原有颜色和透明度
      pieData1.push({
        ...chartData.value[i],
        itemStyle: {
          color: colorList[i],
          opacity: 0.5
        }
      });
    }
    pieData1.push(gapData);

    // 第二圈数据
    if (chartData.value[i].name === '离线') {
      // 离线，特殊颜色
      pieData2.push({
        ...chartData.value[i],
        itemStyle: {
          color: '#283039'
        }
      });
    } else {
      // 其他，原有颜色和透明度
      pieData2.push({
        ...chartData.value[i],
        itemStyle: {
          color: colorList[i]
        }
      });
    }
    pieData2.push(gapData);
  }
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return;
  if (chartInstance) chartInstance.dispose();
  chartInstance = echarts.init(chartRef.value);

  chartInstance.setOption({
    tooltip: {
      show: true,
      backgroundColor: 'rgba(0, 0, 0,.8)',
      textStyle: {
        color: '#fff'
      }
    },
    grid: {
      top: 35,
      right: 30,
      bottom: 20,
      left: 30
    },
    color: colorList,
    series: [
      //外圈，阴影部分
      {
        name: '',
        type: 'pie',
        roundCap: true,
        radius: ['78%', '84%'],
        center: ['50%', '50%'],
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        data: pieData1
      },
      //内圈
      {
        type: 'pie',
        radius: ['65%', '78%'],
        center: ['50%', '50%'],
        gap: 1.71,
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        silent: true,
        data: pieData2
      },
      //内部装饰用背景饼
      {
        type: 'pie',
        center: ['50%', '50%'],
        radius: [0, '45.6%'],
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        itemStyle: {
          color: 'rgba(75, 126, 203,.1)'
        },
        silent: true,
        data: [
          {
            value: 100,
            name: ''
          }
        ]
      }
    ]
  });

  window.addEventListener('resize', () => chartInstance?.resize());
};

// 组件卸载时清理
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
});

// 初始化
onMounted(async () => {
  await fetchData();
  initChart();
});
</script>

<style scoped>
.pie-chart-container {
  position: relative;
  width: 40%;
  height: 160px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pie-chart {
  width: 100%;
  height: 100%;
}

.stats-panel {
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 160px;
  margin-top: 70px;
}

.total-number {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -60%);
  font-family: 'CustomFont';
  font-size: 30px;
  color: #0af0ff;
  font-weight: 400;
  pointer-events: none;
}

.total-label {
  position: absolute;
  bottom: -24px;
  left: 50%;
  transform: translate(-50%, 0);
  font-size: 16px;
  color: #0af0ff;
  pointer-events: none;
  font-weight: 400;
  font-family: 'Source Han Sans CN', Arial, sans-serif;
}

.status-list {
  /* margin-left: 40px; */
  display: flex;
  flex-direction: column;
  gap: 18px;
}

.status-item {
  display: flex;
  align-items: center;
  font-size: 18px;
  color: #fff;
  height: 37px;
  background: url('@/assets/images/statusItemBg.png') no-repeat;
  padding: 13px 0px 12px 15px;
}

.status-dot {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  margin-right: 12px;
  border: 2px solid #23272e;
}

.status-name {
  width: 43px;
  margin-right: 25px;
  font-size: 14px;
  font-family: 'Source Han Sans CN', Arial, sans-serif;
  font-weight: 400;
}

.status-value {
  text-align: right;
  margin-right: 29px;
  font-size: 14px;
  font-family: 'CustomFont', Arial, sans-serif;
  font-weight: 400;
}

.status-percent {
  text-align: right;
  color: #ffffff;
  font-size: 14px;
  font-family: 'CustomFont', Arial, sans-serif;
  font-weight: 400;
}
</style>
