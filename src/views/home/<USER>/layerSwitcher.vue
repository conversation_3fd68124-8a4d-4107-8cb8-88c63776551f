<template>
  <div class="layer-switcher-container">
    <div class="layer-button" @click="toggleLayerMenu" :class="{ active: showLayerMenu }">
      <svg class="button-icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M128 128h768v128H128V128z m0 192h768v128H128V320z m0 192h768v128H128V512z m0 192h768v128H128V704z"
          fill="currentColor"
        ></path>
      </svg>
    </div>

    <!-- 图层切换菜单 -->
    <div v-if="showLayerMenu" class="layer-menu">
      <div
        v-for="layer in layerTypes"
        :key="layer.value"
        class="layer-menu-item"
        :class="{ active: currentLayer === layer.value }"
        @click="switchLayer(layer.value)"
      >
        <svg class="layer-icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
          <path :d="layer.iconPath" fill="currentColor" />
        </svg>
        <span>{{ layer.name }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';

interface LayerType {
  name: string;
  value: string;
  iconPath: string;
}

const showLayerMenu = ref(false);
const currentLayer = ref('satellite');

const layerTypes: LayerType[] = [
  {
    name: '矢量地图',
    value: 'vector',
    iconPath: 'M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z'
  },
  {
    name: '卫星地图',
    value: 'satellite',
    iconPath: 'M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zM896 792H128V224h768v568zM304 456a88 88 0 1 0 0-176 88 88 0 0 0 0 176zm513.6 205.6l-125.4-125.3a16 16 0 0 0-22.6 0l-113 113.1-191.2-191.2a16.1 16.1 0 0 0-22.6 0L230.2 570.6c-6.7 6.7-6.7 17.6 0 24.3 6.7 6.7 17.6 6.7 24.3 0L370.8 478.6l191.2 191.2c6.2 6.2 16.4 6.2 22.6 0L697.8 556.5l125.4 125.3c6.7 6.7 17.6 6.7 24.3 0 6.7-6.7 6.7-17.5 0-24.2z'
  },
  {
    name: '地形地图',
    value: 'terrain',
    iconPath: 'M928 224H768v-56c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v56H548v-56c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v56H328v-56c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v56H96c-17.7 0-32 14.3-32 32v576c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V256c0-17.7-14.3-32-32-32z'
  }
];

// 切换图层菜单显示/隐藏
const toggleLayerMenu = (event: Event) => {
  event.stopPropagation();
  showLayerMenu.value = !showLayerMenu.value;
};

// 底图切换逻辑 - 改进版本
const switchLayer = async (layerValue: string) => {
  if (currentLayer.value !== layerValue) {
    const oldLayer = currentLayer.value;
    currentLayer.value = layerValue;
    showLayerMenu.value = false; // 选择后关闭菜单

    try {
      console.log(`🔄 开始从 ${oldLayer} 切换到 ${layerValue}...`);

      // 调用全局暴露的地图切换方法
      if (window.switchMapBaseLayer) {
        await window.switchMapBaseLayer(layerValue);

        // 获取当前底图信息用于调试
        if (window.getCurrentBasemapInfo) {
          const info = window.getCurrentBasemapInfo();
          console.log('📊 当前底图状态:', info);
        }

        console.log(`✅ 成功切换到图层: ${layerValue}`);
      } else {
        console.warn('⚠️ 地图切换方法未找到');
      }
    } catch (error) {
      console.error('❌ 图层切换失败:', error);
      // 切换失败时恢复到原来的图层
      currentLayer.value = oldLayer;
    }
  }
};

// 点击外部关闭菜单
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement;
  if (!target.closest('.layer-switcher-container')) {
    showLayerMenu.value = false;
  }
};

onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<style scoped>
.layer-switcher-container {
  position: relative;
  z-index: 200;
}

.layer-button {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, rgba(25, 252, 222, 0.2), rgba(10, 240, 255, 0.2));
  border: 2px solid rgba(25, 252, 222, 0.4);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(8px);
  box-shadow: 0 4px 16px rgba(25, 252, 222, 0.2);
}

.layer-button:hover {
  border-color: rgba(25, 252, 222, 0.8);
  box-shadow: 0 6px 24px rgba(25, 252, 222, 0.4);
  transform: translateY(-2px);
}

.layer-button.active {
  background: linear-gradient(135deg, rgba(25, 252, 222, 0.4), rgba(10, 240, 255, 0.4));
  border-color: rgba(25, 252, 222, 0.8);
  transform: rotate(180deg);
}

.button-icon {
  width: 20px;
  height: 20px;
  color: #19fcde;
  transition: transform 0.3s ease;
}

.layer-button.active .button-icon {
  transform: rotate(-180deg);
}

.layer-menu {
  position: absolute;
  top: 50%;
  left: calc(100% + 12px);
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  background: rgba(20, 30, 40, 0.95);
  border: 1px solid rgba(25, 252, 222, 0.3);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(16px);
  overflow: hidden;
  animation: menuFadeIn 0.3s ease-out;
  min-width: 120px;
}

@keyframes menuFadeIn {
  from {
    opacity: 0;
    transform: translateY(-50%) translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(-50%) translateX(0);
  }
}

.layer-menu-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  color: #fff;
  font-size: 13px;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  white-space: nowrap;
}

.layer-menu-item:last-child {
  border-bottom: none;
}

.layer-menu-item:hover {
  background: rgba(25, 252, 222, 0.15);
  color: #19fcde;
}

.layer-menu-item.active {
  background: rgba(25, 252, 222, 0.2);
  color: #19fcde;
  font-weight: 500;
}

.layer-icon {
  width: 14px;
  height: 14px;
  margin-right: 10px;
  color: #fff;
  transition: color 0.2s ease;
}

.layer-menu-item:hover .layer-icon,
.layer-menu-item.active .layer-icon {
  color: #19fcde;
}

/* 添加一个小三角箭头指向按钮 */
.layer-menu::before {
  content: '';
  position: absolute;
  top: 50%;
  left: -6px;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
  border-right: 6px solid rgba(25, 252, 222, 0.3);
}
</style>
