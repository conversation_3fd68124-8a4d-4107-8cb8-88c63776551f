<template>
  <panelComponent width="500px" height="444px" :bg="panelBg" headerTop="19px">
    <template #header>任务信息</template>
    <div class="task-list-main">
      <div class="task-table">
        <div class="task-table-header">
          <span>任务名称</span>
          <span style="margin-left: 69px">计划时间</span>
          <span style="margin-left: 69px">计划结束时间</span>
          <span style="margin-left: 32px">任务状态</span>
        </div>
        <div v-for="task in tasks" :key="task.name" class="task-table-row">
          <div>
            <img src="@/assets/images/taskListIcon.png" style="width: 12px; height: 12px; margin: 0 8px 0px 4px" />
            <span :title="task.name.length > 5 ? task.name : ''">{{ task.name.length > 5 ? task.name.substring(0, 5) + '...' : task.name }}</span>
          </div>
          <div class="task-row-right-content" :class="task.status === '待执行' ? 'pending' : 'done'">
            <span style="margin-left: 26px">{{ task.startTime }}</span>
            <span style="margin-left: 10px">~</span>
            <span style="margin-left: 10px">{{ task.endTime }}</span>
            <span style="margin-left: 20px" :class="['status', task.status === '任务完成' ? 'done' : 'pending']">
              {{ task.status }}
            </span>
          </div>
        </div>
      </div>
      <div class="pagination">
        <button @click="prevPage" :disabled="page === 1" class="circle-arrow-btn" style="background: none; border: none; padding: 0">
          <img :src="leftArrowImg" alt="上一页" style="width: 15px; height: 15px; display: block" />
        </button>
        <template v-for="p in visiblePages" :key="p.value">
          <span v-if="p.type === 'page'" class="pagination-text" :class="{ active: p.value === page }" @click="goPage(p.value)">{{ p.value }}</span>
          <span v-else-if="p.type === 'ellipsis'" class="pagination-text ellipsis">...</span>
        </template>
        <button @click="nextPage" :disabled="page === totalPages" class="circle-arrow-btn" style="background: none; border: none; padding: 0">
          <img :src="rightArrowImg" alt="下一页" style="width: 15px; height: 15px; display: block" />
        </button>
        <span style="color: #e5e5e5">跳至</span>
        <input
          v-model.number="jumpPage"
          type="number"
          min="1"
          :max="totalPages"
          @keyup.enter="goPage(jumpPage)"
          class="jump-input"
        />
        <span style="color: #e5e5e5">页</span>
      </div>
    </div>
  </panelComponent>
</template>

<script setup lang="ts">
import { ref, nextTick, computed, onMounted } from 'vue';
import panelComponent from '@/views/home/<USER>/panelComponent.vue';
import panelBg from '@/assets/images/taskListPanelBg.png';
import leftArrowActive from '@/assets/images/left-arrow-active.png';
import leftArrowDisabled from '@/assets/images/left-arrow-disabled.png';
import rightArrowActive from '@/assets/images/right-arrow-active.png';
import rightArrowDisabled from '@/assets/images/right-arrow-disabled.png';
import { listFlightTaskResult } from '@/api/biz/flightTaskResult/index';

const tasks = ref([]);
const page = ref(1);
const pageSize = 7;
const jumpPage = ref(1);
const total = ref(0);

const leftArrowImg = computed(() => (page.value === 1 ? leftArrowDisabled : leftArrowActive));
const rightArrowImg = computed(() => (page.value === totalPages.value ? rightArrowDisabled : rightArrowActive));

async function fetchData() {
  try {
    const response = await listFlightTaskResult({ pageNum: page.value, pageSize: pageSize });
    tasks.value = []; // 清空数据
    await nextTick(); // 等待DOM更新
    tasks.value = response.rows.map(item => ({
      name: item.taskName,
      startTime: formatTime(item.plannedStartTime),
      endTime: formatTime(item.plannedEndTime),
      status: (() => {
        switch (item.taskStatus) {
          case '1': return '待执行';
          case '2': return '执行中';
          case '3': return '任务完成';
          case '4': return '任务取消';
          case '5': return '任务失败';
          case '6': return '暂停';
          default: return '未知状态';
        }
      })()
    }));
    total.value = response.total;
  } catch (error) {
    console.error('Failed to fetch data:', error);
  }
}

function formatTime(timeString) {
  // 将时间字符串转换为 Date 对象
  const date = new Date(timeString);

  // 定义补零函数
  const padZero = (num: number): string => (num < 10 ? '0' + num : num.toString());

  // 格式化时间为 "YYYY.MM.DD HH:mm"
  const year = date.getFullYear();
  const month = padZero(date.getMonth() + 1); // 月份从0开始
  const day = padZero(date.getDate());
  const hours = padZero(date.getHours());
  const minutes = padZero(date.getMinutes());

  return `${year}.${month}.${day} ${hours}:${minutes}`;
}

// 在组件挂载时调用接口获取数据
onMounted(() => {
  fetchData();
});

const totalPages = computed(() => Math.ceil(total.value / pageSize));

// 计算可见的页码，包含省略号逻辑
const visiblePages = computed(() => {
  const total = totalPages.value;
  const current = page.value;
  const pages = [];
  
  if (total <= 7) {
    // 总页数小于等于7，显示所有页码
    for (let i = 1; i <= total; i++) {
      pages.push({ type: 'page', value: i });
    }
  } else {
    // 总页数大于7，需要省略号
    if (current <= 4) {
      // 当前页在前面，显示 1,2,3,4,5...total
      for (let i = 1; i <= 5; i++) {
        pages.push({ type: 'page', value: i });
      }
      pages.push({ type: 'ellipsis' });
      pages.push({ type: 'page', value: total });
    } else if (current >= total - 3) {
      // 当前页在后面，显示 1...total-4,total-3,total-2,total-1,total
      pages.push({ type: 'page', value: 1 });
      pages.push({ type: 'ellipsis' });
      for (let i = total - 4; i <= total; i++) {
        pages.push({ type: 'page', value: i });
      }
    } else {
      // 当前页在中间，显示 1...current-1,current,current+1...total
      pages.push({ type: 'page', value: 1 });
      pages.push({ type: 'ellipsis' });
      for (let i = current - 1; i <= current + 1; i++) {
        pages.push({ type: 'page', value: i });
      }
      pages.push({ type: 'ellipsis' });
      pages.push({ type: 'page', value: total });
    }
  }
  
  return pages;
});

function prevPage() {
  if (page.value > 1) {
    page.value--;
    fetchData();
  }
}
function nextPage() {
  if (page.value < totalPages.value) {
    page.value++;
    fetchData();
  }
}
function goPage(p: number) {
  if (p >= 1 && p <= totalPages.value) {
    page.value = p;
    fetchData();
  }
}
</script>

<style scoped>
.task-list-main {
  padding: 16px 18px 0 17px;
  margin-top: 57px;
}
.task-table {
  color: #fff;
  font-size: 15px;
}
.task-table-header,
.task-table-row {
  display: flex;
  /* justify-content: space-between; */
  font-family: 'Source Han Sans CN', Arial, sans-serif;
  align-items: center;
  font-size: 14px;
  padding: 15px 0px 4px 0px;
}
.task-table-header {
  background: #444b53;
  border-radius: 13px 13px 13px 13px;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  /* letter-spacing: 25px; */
  text-shadow: 0px 3px 6px rgba(0, 0, 0, 0.7);
  text-align: left;
  font-style: normal;
  text-transform: none;
  padding: 9px 19px;
}
.task-table-row {
  font-family: 'Source Han Sans CN', Arial, sans-serif;
  font-size: 14px;
  padding: 15px 0px 4px 0px;
  height: 37px;
  /* transition: all 0.3s ease; */
  margin-bottom: 4px;
  width: 100%;
}
.task-row-right-content {
  display: flex;
  align-items: center;
  height: 37px;
  margin-left: 8px;
  background-size: 100% 100%;
  background-position: center;
  /* width: 79%; */
  flex: 1;
}
.task-row-right-content.pending {
  background: url('@/assets/images/task-pending-bg.png') no-repeat;
}
.task-row-right-content.done {
  background: url('@/assets/images/task-done-bg.png') no-repeat;
}
.task-row-right-content:hover {
  background: url('@/assets/images/task-hover-bg.png') no-repeat;
  cursor: pointer;
}
.status.pending {
  color: #ff4d4f;
}
.status.done {
  color: #00e09e;
}
.pagination {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 21px;
  gap: 6px;
  font-weight: 500;
  height: 14px;
  font-family: 'Source Han Sans CN';
}
.pagination span.active {
  color: #333;
  background-color: #0af0ff;
  font-weight: bold;
  border-radius: 2px 2px 2px 2px;
  /* border-bottom: 2px solid #00e09e; */
  cursor: pointer;
}
.pagination span {
  cursor: pointer;
  padding: 0 4px;
  color: #8a8a8a;
}
.jump-input {
  width: 48px;
  height: 20px;
  margin: 0 6px;
  padding: 2px 6px;
  text-align: center;
  background: rgba(229, 229, 229, 0.32);
  border: 1px solid rgba(229, 229, 229, 0.5);
  border-radius: 4px;
  color: #fff;
  font-size: 12px;
  outline: none;
  transition: all 0.3s ease;
}
.jump-input:focus {
  border-color: #0af0ff;
  background: rgba(10, 240, 255, 0.1);
}
.jump-input::-webkit-outer-spin-button,
.jump-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.pagination-text {
  font-size: 12px;
}
.pagination-text.ellipsis {
  cursor: default;
  color: #8a8a8a;
}
.circle-arrow-btn {
  width: 14px;
  height: 14px;
  border: none;
  background: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 0;
}
.circle-arrow-btn:disabled {
  cursor: not-allowed;
}
</style>
