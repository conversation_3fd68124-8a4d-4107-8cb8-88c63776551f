<template>
  <div class="dashboard">
    <!-- 中央地图视野 -->
    <mapView />
    <!-- 左侧阴影遮罩 -->
    <!-- <div class="shadow-mask shadow-mask-left"></div> -->
    <!-- 右侧阴影遮罩 -->
    <!-- <div class="shadow-mask shadow-mask-right"></div> -->
    <!-- <v-scale-screen width="1920" height="1080" :boxStyle="boxStyle"> -->
    <div class="main-content" id="main-action-pannel">
      <img src="@/assets/images/line.png" style="width: 2px; height: 1014px; margin-left: 19px" />
      <div class="left-panel">
        <div class="statistics-section">
          <statistics />
          <quickActions />
          <layerSwitcher />
        </div>
        <img src="@/assets/images/panel-divider.png" style="width: 500px; height: 2px; margin-top: 13px; margin-bottom: 2px" />
        <statsPanel />
        <img src="@/assets/images/panel-divider.png" style="width: 500px; height: 2px; margin-top: 13px; margin-bottom: 2px" />
        <notification />
      </div>
      <div class="right-panel">
        <taskList />
        <img src="@/assets/images/panel-divider.png" style="width: 500px; height: 2px; margin-top: 11px; margin-bottom: 3px" />
        <deviceInfo />
      </div>
      <div class="shadow-mask shadow-mask-left" />
      <div class="shadow-mask shadow-mask-right" />
      <img src="@/assets/images/line.png" style="width: 2px; height: 1014px; margin-right: 19px; position: absolute; right: 0" />
    </div>
    <!-- </v-scale-screen> -->
    <!-- 右下角悬浮模块 -->
    <routeLegend />
  </div>
</template>

<script setup lang="ts">
import statsPanel from '@/views/home/<USER>/statsPanel.vue';
import statistics from '@/views/home/<USER>/statistics.vue';
import quickActions from '@/views/home/<USER>/quickActions.vue';
import layerSwitcher from '@/views/home/<USER>/layerSwitcher.vue';
import notification from '@/views/home/<USER>/notification.vue';
import mapView from '@/views/home/<USER>/mapView.vue';
import routeLegend from '@/views/home/<USER>/routeLegend.vue';
import taskList from '@/views/home/<USER>/taskList.vue';
import deviceInfo from '@/views/home/<USER>/deviceInfo.vue';
import autofit from 'autofit.js';
import VScaleScreen from 'v-scale-screen';

const boxStyle = {
  background: 'transparent',
  position: 'absolute',
  zIndex: 100,
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  pointerEvents: 'none'
};

onMounted(() => {
  autofit.init({
    dh: 1080,
    dw: 1920,
    el: '#main-action-pannel',
    resize: true
  });
});
</script>

<style scoped>
.dashboard {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  background: rgba(7, 10, 20, 1);
  /* background-image: url('@/assets/images/base-map-bg.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat; */
}
.main-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  display: flex;
  align-items: stretch;
  z-index: 100;
}
.left-panel,
.right-panel {
  /* width: 320px; */
  position: absolute;
  display: flex;
  flex-direction: column;
  background: transparent;
  pointer-events: auto;
  z-index: 70;
}

.left-panel {
  left: 0;
  top: 0;
  box-sizing: border-box;
  padding: 28px 0px 0px 42px;
  /* background: linear-gradient(to right, #090d17, transparent); */
}

.right-panel {
  right: 0;
  top: 0;
  box-sizing: border-box;
  padding: 28px 42px 0px 0px;
  /* background: linear-gradient(to left, #090d17, rgba(9, 13, 23, 0.5)); */
}

/* 阴影遮罩样式 */
.shadow-mask {
  position: absolute;
  top: 0;
  width: 35%;
  height: 100%;
  z-index: 50; /* 在地图之上，操作面板之下 */
  pointer-events: none;
}

.shadow-mask-left {
  left: 0;
  background: linear-gradient(to right, rgba(7, 10, 20, 1), rgba(13, 17, 27, 0.9), rgba(22, 35, 46, 0.6), transparent);
}

.shadow-mask-right {
  right: 0;
  background: linear-gradient(to left, rgba(7, 10, 20, 1), rgba(13, 17, 27, 0.9), rgba(22, 35, 46, 0.6), transparent);
}

.statistics-section {
  display: flex;
  align-items: flex-start;
  gap: 20px;
}
</style>
