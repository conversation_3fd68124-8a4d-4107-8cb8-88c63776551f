<template>
  <div class="panel-bg" :style="panelStyle">
    <div class="panel-header">
      <slot name="header" />
    </div>
    <div class="panel-content">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
interface Props {
  width?: string | number;
  height?: string | number;
  bg?: string;
  headerTop?: string | number;
}
const props = defineProps<Props>();

// 动态控制背景模块宽高和背景图
const defaultBg = new URL('@/assets/images/panelBg.png', import.meta.url).href;
const panelStyle = computed(() => {
  const style: Record<string, string> = {};
  if (props.width) {
    style.width = typeof props.width === 'number' ? props.width + 'px' : props.width;
  }
  if (props.height) {
    style.height = typeof props.height === 'number' ? props.height + 'px' : props.height;
  }
  if (props.headerTop !== undefined) {
    style['--header-top'] = typeof props.headerTop === 'number' ? props.headerTop + 'px' : props.headerTop;
  }
  style.background = `url('${props.bg || defaultBg}') no-repeat center center`;
  style.backgroundSize = '100% 100%';
  return style;
});
</script>

<style scoped>
.panel-bg {
  position: relative;
  width: 100%;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2);
  box-sizing: border-box;
}
.panel-header {
  position: absolute;
  top: var(--header-top, 28px);
  padding-left: 68px;
  font-family: 'CustomFont';
  font-weight: 400;
  font-size: 16px;
  color: #ffffff;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
</style>
