<template>
  <panelComponent width="501px" height="454px" :bg="panelBg" headerTop="19px">
    <template #header>设备信息</template>
    <div class="device-info-main">
      <!-- 无人机选择区域 -->
      <div class="drone-selector" @click="toggleDeviceList($event)">
        {{ getDeviceDisplayName(selectedDevice) }}
        <img src="@/assets/images/arrow-down.png" class="arrow-icon" :class="{ 'rotated': showDeviceList }" />
        <!-- 设备选择下拉列表 -->
        <div v-if="showDeviceList" class="device-dropdown">
          <div v-for="device in deviceList" :key="device.id" class="device-option" @click.stop="selectDevice(device)">
            {{ getDeviceDisplayName(device) }}
          </div>
          <div v-if="deviceList.length === 0" class="device-option" style="color: #999">暂无设备数据</div>
        </div>
      </div>
      <!-- 状态栏 -->
      <div class="status-row">
        <div class="icon-block">
          <img src="@/assets/images/drone-icon.png" alt="drone" class="icon" />
          <div class="status-text-container">
            <span class="status-text">状态：<DictTag :options="device_status" :value="deviceDetail?.status" /></span>
          </div>
        </div>
        <div class="battery-block ml20">
          <img src="@/assets/images/battery-icon.png" alt="battery" class="icon" />
          <div class="battery-text">
            <span class="battery-percent">{{ deviceDetail?.batteryLevel || 0 }}%</span>
          </div>
        </div>
      </div>
      <!-- 控制器信息 -->
      <div class="controller-row">
        <img src="@/assets/images/controller-icon.png" style="width: 16px; height: 16px" />
        <span>{{ deviceDetail?.controller || '未知控制器' }}</span>
      </div>
      <!-- 飞行统计 -->
      <div class="stats-section">
        <div class="stats-row">
          <div class="stat-block">
            <div class="stat-label">今日飞行次数</div>
            <div class="stat-value-container">
              <div class="stat-value">{{ deviceDetail?.todayFlightCount || 0 }} <span class="unit">次</span></div>
            </div>
          </div>
          <div class="stat-block">
            <div class="stat-label">今日飞行里程</div>
            <div class="stat-value-container">
              <div class="stat-value">{{ formatNumber(deviceDetail?.todayDistance) }} <span class="unit">km</span></div>
            </div>
          </div>
          <div class="stat-block">
            <div class="stat-label">今日飞行时长</div>
            <div class="stat-value-container">
              <div class="stat-value">{{ formatNumber(deviceDetail?.todayDuration) }} <span class="unit">h</span></div>
            </div>
          </div>
        </div>
        <div class="stats-row">
          <div class="stat-block">
            <div class="stat-label">累计飞行次数</div>
            <div class="stat-value-container">
              <div class="stat-value color-blue">{{ deviceDetail?.totalFlightCount || 0 }} <span class="unit color-blue">次</span></div>
            </div>
          </div>
          <div class="stat-block">
            <div class="stat-label">累计飞行里程</div>
            <div class="stat-value-container color-blue">
              <div class="stat-value color-blue">{{ formatNumber(deviceDetail?.totalDistance) }} <span class="unit color-blue">km</span></div>
            </div>
          </div>
          <div class="stat-block">
            <div class="stat-label">累计飞行时长</div>
            <div class="stat-value-container color-blue">
              <div class="stat-value color-blue">{{ formatNumber(deviceDetail?.totalDuration) }} <span class="unit color-blue">h</span></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </panelComponent>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, toRefs, getCurrentInstance, onUnmounted } from 'vue';
import type { ComponentInternalInstance } from 'vue';
import panelComponent from '@/views/home/<USER>/panelComponent.vue';
import DictTag from '@/components/DictTag/index.vue';
import panelBg from '@/assets/images/deviceInfoPanelBg.png';
import { dashboardListDroneDevice, getDashboardDroneDeviceDetailStatistics } from '@/api/biz/droneDevice/index';
import { DroneDeviceVO, DroneDeviceDetailStatVO } from '@/api/biz/droneDevice/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { device_status } = toRefs<any>(proxy?.useDict('device_status'));

// 响应式数据
const deviceList = ref<DroneDeviceVO[]>([]);
const selectedDevice = ref<DroneDeviceVO | null>(null);
const deviceDetail = ref<DroneDeviceDetailStatVO | null>(null);
const showDeviceList = ref(false);
const loading = ref(false);

// 获取设备列表
const fetchDeviceList = async () => {
  try {
    const response = await dashboardListDroneDevice({
      pageNum: 1,
      pageSize: 100
    });

    // 根据实际响应格式处理数据
    const apiResponse = response as any;
    if (apiResponse && apiResponse.rows && Array.isArray(apiResponse.rows)) {
      deviceList.value = apiResponse.rows as DroneDeviceVO[];
    } else if (apiResponse.data && apiResponse.data.rows) {
      // 备用格式处理
      deviceList.value = apiResponse.data.rows as DroneDeviceVO[];
    } else {
      deviceList.value = [];
    }

    // 默认选择第一个设备
    if (deviceList.value.length > 0) {
      selectedDevice.value = deviceList.value[0];
      await fetchDeviceDetail(selectedDevice.value.id);
    }
  } catch (error) {
    console.error('获取设备列表失败:', error);
    deviceList.value = [];
  }
};

// 获取设备详情统计
const fetchDeviceDetail = async (deviceId: string | number) => {
  if (!deviceId) return;
  loading.value = true;
  try {
    const response = await getDashboardDroneDeviceDetailStatistics(deviceId);
    deviceDetail.value = response.data;
  } catch (error) {
    console.error('获取设备详情失败:', error);
  } finally {
    loading.value = false;
  }
};

// 切换设备列表显示/隐藏
const toggleDeviceList = (event: Event) => {
  event.stopPropagation(); // 阻止事件冒泡
  showDeviceList.value = !showDeviceList.value;
};

// 选择设备
const selectDevice = async (device: DroneDeviceVO) => {
  selectedDevice.value = device;
  showDeviceList.value = false;
  await fetchDeviceDetail(device.id);
};

// 格式化数字，保留一位小数
const formatNumber = (value: number | undefined): string => {
  if (value === undefined || value === null) return '0.0';
  return value.toFixed(1);
};

// 获取设备显示名称（优先显示nickname，没有则显示deviceName）
const getDeviceDisplayName = (device: DroneDeviceVO | null): string => {
  if (!device) return '请选择设备';
  return device.nickname || device.deviceName || '未知设备';
};

// 组件挂载时获取数据
onMounted(() => {
  fetchDeviceList();

  // 添加全局点击事件监听，点击外部时关闭下拉框
  document.addEventListener('click', (event) => {
    const target = event.target as HTMLElement;
    if (!target.closest('.drone-selector') && !target.closest('.device-dropdown')) {
      showDeviceList.value = false;
    }
  });
});

// 组件卸载时移除事件监听
onUnmounted(() => {
  document.removeEventListener('click', () => {
    showDeviceList.value = false;
  });
});
</script>

<style scoped>
.device-info-main {
  margin-top: 23px;
  padding: 32px 24px 0 24px;
  position: relative;
  z-index: 10;
}
.drone-selector {
  font-family: 'Source Han Sans CN';
  color: #fff;
  font-size: 14px;
  font-weight: 400;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  cursor: pointer;
  height: 14px;
  position: relative;
  z-index: 1;
}
.arrow-icon {
  width: 14px;
  height: 7px;
  margin-left: 9px;
}
.status-row {
  display: flex;
  align-items: center;
  margin-bottom: 29px;
  margin-top: 31px;
  padding-left: 8px;
}
.status-text-container {
  margin-left: 15px;
  background: url('@/assets/images/status-text-bg.png') no-repeat center center;
  font-family: 'CustomFont', Arial, sans-serif;
  font-weight: 400;
  color: #e5e5e5;
  background-size: 100% 100%;
  padding: 19px 39px 19px 25px;
  height: 49px;
  display: flex;
  align-items: center;
}
.status-text {
  font-family: 'Source Han Sans CN', Arial, sans-serif;
  font-weight: 400;
  color: #e5e5e5;
  font-size: 14px;
  text-align: center;
}
.battery-text {
  margin-left: 11px;
  background: url('@/assets/images/battery-text-bg.png') no-repeat center center;
  background-size: 100% 100%;
  padding: 19px 39px 19px 25px;
  height: 49px;
  display: flex;
  align-items: center;
}
.icon-block,
.battery-block {
  display: flex;
  align-items: center;
  gap: 8px;
}
.icon {
  width: 49px;
  height: 49px;
}
.battery-percent {
  font-size: 25px;
  font-family: 'CustomFont', Arial, sans-serif;
  color: #19fcde;
  font-weight: 400;
}
.controller-row {
  background: url('@/assets/images/controller-row-bg.png') no-repeat center center;
  background-size: 100% 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  height: 40px;
  margin-bottom: 18px;
  font-size: 16px;
  font-family: 'CustomFont', Arial, sans-serif;
  font-weight: 400;
}
.stats-section {
  margin-top: 10px;
}
.stats-title {
  font-size: 15px;
  color: #00eaff;
  margin: 10px 0 6px 0;
}
.stats-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 22px;
}
.stat-block {
  text-align: center;
}
.stat-value {
  font-size: 20px;
  font-weight: 400;
  color: #19fcde;
  font-family: 'CustomFont', Arial, sans-serif;
}
.unit {
  font-size: 14px;
  color: #19fcde;
}
.color-blue {
  color: #0af0ff;
}
.stat-label {
  font-size: 14px;
  color: #ffffff;
  margin-bottom: 12px;
  font-family: 'Source Han Sans CN';
  font-weight: 400;
}
.stat-value-container {
  background: url('@/assets/images/stat-bg.png') no-repeat center center;
  background-size: 100% 100%;
  min-width: 140px;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 下拉箭头旋转动画 */
.arrow-icon {
  transition: transform 0.3s ease;
}
.arrow-icon.rotated {
  transform: rotate(180deg);
}

/* 设备选择下拉列表 */
.device-dropdown {
  position: absolute;
  top: calc(100% + 5px);
  right: 0;
  width: 200px;
  max-height: 250px;
  background: rgba(20, 30, 40, 0.98);
  border: 1px solid rgba(25, 252, 222, 0.3);
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
  z-index: 9999;
  backdrop-filter: blur(12px);
  overflow-y: auto;
  animation: dropdownFadeIn 0.2s ease-out;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.device-option {
  padding: 12px 16px;
  color: #fff;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.device-option:last-child {
  border-bottom: none;
}

.device-option:hover {
  background: rgba(25, 252, 222, 0.15);
  color: #19fcde;
  transform: translateX(2px);
}

/* 设备选择器样式 */
.drone-selector {
  position: relative;
  z-index: 1;
}
</style>
