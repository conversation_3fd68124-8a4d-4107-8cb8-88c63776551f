<template>
  <panelComponent width="501px" height="373px" :bg="panelBg" headerTop="18px">
    <template #header>
      <div>通知</div>
    </template>
    <div class="tabs">
      <div class="tab" v-for="tab in tabs" :key="tab" @click="activeTab = tab">{{ tab }}</div>
    </div>
    <div class="notification-list">
      <div v-for="(item, idx) in notifications" :key="idx" class="notification-item" :class="item.status">
        <div class="time">{{ item.time }}</div>
        <div class="content">
          <img :src="item.status === 'fail' ? taskFailIcon : taskIcon" style="width: 15px; height: 48px; margin-right: 24px" />
          <div class="conent-text" :class="item.status">
            <div class="title">{{ item.title }}</div>
            <div class="result" :class="item.status">{{ item.result }}</div>
          </div>
        </div>
      </div>
    </div>
  </panelComponent>
</template>

<script setup lang="ts">
import panelComponent from '@/views/home/<USER>/panelComponent.vue';
import panelBg from '@/assets/images/notificationPanelBg.png';
import taskFailIcon from '@/assets/images/taskFailIcon.png';
import taskIcon from '@/assets/images/taskIcon.png';

const notifications = [
  {
    time: '2025/05/17 09:00',
    status: 'fail', // fail/success
    title: '飞行任务执行失败，任务名称：巡检任务...',
    result: '任务失败'
  },
  {
    time: '2025/05/17 09:00',
    status: 'fail',
    title: '飞行任务执行失败，任务名称：巡检任务...',
    result: '任务失败'
  },
  {
    time: '2025/05/17 09:00',
    status: 'success',
    title: '飞行任务执行失败，任务名称：巡检任务...',
    result: '任务完成'
  }
];
const activeTab = ref('任务通知');
const tabs = ['任务通知', '应急消息'];
</script>

<style scoped>
.tabs {
  display: flex;
  gap: 16px;
  margin-top: 41px;
  padding: 10px;
  justify-content: flex-end;
}
.tab {
  width: 85px;
  height: 27px;
  cursor: pointer;
  padding: 4px 16px;
  background: url('@/assets/images/taskButton.png') no-repeat center center;
  background-size: 100%;
  font-size: 12px;
  font-family: 'Source Han Sans CN', Arial, sans-serif;
}
.tab.active {
  color: #fff;
  background: #1e2a3a;
}
.notification-list {
  max-height: 300px;
  overflow-y: auto;
}
.notification-item {
  display: flex;
  margin-bottom: 16px;
  gap: 8px;
  padding: 0px 30px 0px 50px;
  align-items: flex-start;
  flex-direction: column;
}
.notification-item .time {
  font-family: 'CustomFont';
  font-size: 14px;
  color: #FFFFFF;
}
.notification-item .content {
  flex: 1;
  display: flex;
  width: 100%;
  font-family: 'Source Han Sans CN';
}
.notification-item .content .conent-text{
  width: 100%;
  background: rgba(255, 72, 61, 0.1);
  border-radius: 6px 6px 6px 6px;
  padding: 6px 0px 6px 29px;
}
.notification-item.success .content .conent-text {
  background: rgba(25, 252, 222, 0.1);
}
.notification-item .title {
  font-size: 14px;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.notification-item .result {
  font-size: 14px;
  /* font-weight: bold; */
}
.notification-item.fail .result {
  color: #ff4d4f;
}
.notification-item.success .result {
  color: #00e6c7;
}
</style>
