<template>
  <div id="cesium-map-view__container">
    <!-- <img :src="BGMAP" alt="BGMAP" /> -->
  </div>
</template>

<script setup lang="ts">
import { shallowRef, ref, onMounted, onUnmounted, readonly } from 'vue';
import { usePatrolAreaRenderer, fetchPatrolAreaData, PatrolAreaInfo } from '@/hooks/usePatrolAreaRenderer';
import * as DC from '@dvgis/dc-sdk';
import '@dvgis/dc-sdk/dist/dc.min.css';
import { defaultRoute } from '@/utils';

let viewer = {};
const viewerRef = shallowRef(null);

// 获取天地图API密钥
const TIANDITU_KEY = import.meta.env.VITE_APP_TIANDITU_KEY;

// 调试：检查API密钥是否正确获取
if (!TIANDITU_KEY) {
  console.error('天地图API密钥未配置，请检查环境变量 VITE_APP_TIANDITU_KEY');
} else {
  console.log('天地图API密钥已加载:', TIANDITU_KEY.substring(0, 8) + '...');
}

// 选点相关状态
const isPickingMode = ref(false);
const pickingCallback = ref<((coordinates: { lng: number; lat: number; height: number }) => void) | null>(null);
let currentPickMarker: any = null; // 当前选中点的标记

// 暴露给父组件的方法
const startLocationPicking = (callback: (coordinates: { lng: number; lat: number; height: number }) => void, cancelCallback?: () => void) => {
  isPickingMode.value = true;
  pickingCallback.value = callback;

  // 改变鼠标样式，提示用户可以选点
  const mapContainer = document.getElementById('cesium-map-view__container');
  if (mapContainer) {
    mapContainer.style.cursor = 'crosshair';
  }

  // 添加ESC键监听，允许用户取消选点
  const handleEscKey = (event: KeyboardEvent) => {
    if (event.key === 'Escape' && isPickingMode.value) {
      stopLocationPicking();
      if (cancelCallback) {
        cancelCallback();
      }
      document.removeEventListener('keydown', handleEscKey);
    }
  };
  document.addEventListener('keydown', handleEscKey);
};

const stopLocationPicking = () => {
  isPickingMode.value = false;
  pickingCallback.value = null;

  // 恢复鼠标样式
  const mapContainer = document.getElementById('cesium-map-view__container');
  if (mapContainer) {
    mapContainer.style.cursor = 'default';
  }

  // 清除选中点标记
  if (currentPickMarker && viewer.overlayLayer) {
    viewer.overlayLayer.removeOverlay(currentPickMarker);
    currentPickMarker = null;
  }
};

// 处理地图点击事件
const handleMapClick = (event: any) => {
  if (!isPickingMode.value || !pickingCallback.value) return;

  // 确保viewer已经初始化
  if (!viewer || !viewer.scene) {
    console.warn('地图未初始化完成');
    return;
  }

  const { position } = event;

  // 使用正确的屏幕坐标字段 (event.windowPosition)
  const screenPosition = event.windowPosition;
  if (!screenPosition || typeof screenPosition.x !== 'number' || typeof screenPosition.y !== 'number') {
    console.warn('未找到有效的屏幕坐标');
    return;
  }

  // 使用正确的屏幕坐标进行坐标拾取
  const cartesian3 = viewer.scene.pickPosition(new DC.Cartesian2(screenPosition.x, screenPosition.y));

  if (!cartesian3) {
    console.warn('未点击到有效位置');
    return;
  }

  // 转换为经纬度坐标
  const cartographic = viewer.scene.globe.ellipsoid.cartesianToCartographic(cartesian3);
  const lng = DC.Math.toDegrees(cartographic.longitude);
  const lat = DC.Math.toDegrees(cartographic.latitude);
  const height = cartographic.height || 0;

  console.log(`选中坐标 - 经度：${lng.toFixed(6)}, 纬度：${lat.toFixed(6)}, 高度：${height.toFixed(2)}`);

  // 清除之前的标记
  if (currentPickMarker && viewer.overlayLayer) {
    viewer.overlayLayer.removeOverlay(currentPickMarker);
  }

  // 添加新的选中点标记
  const pickPosition = new DC.Position(lng, lat, height);
  currentPickMarker = new DC.Point(pickPosition);
  currentPickMarker.setStyle({
    pixelSize: 12,
    color: DC.Color.YELLOW,
    outlineColor: DC.Color.RED,
    outlineWidth: 2,
    heightReference: DC.HeightReference.CLAMP_TO_GROUND
  });

  // 确保有overlay图层
  if (!viewer.overlayLayer) {
    viewer.overlayLayer = new DC.VectorLayer('overlay').addTo(viewer);
  }
  viewer.overlayLayer.addOverlay(currentPickMarker);

  // 调用回调函数，返回坐标
  pickingCallback.value({
    lng: parseFloat(lng.toFixed(6)),
    lat: parseFloat(lat.toFixed(6)),
    height: parseFloat(height.toFixed(2))
  });

  // 自动关闭选点模式
  stopLocationPicking();
};



// 调试函数：检查地图状态
const debugMapStatus = () => {
  console.log('🔍 地图状态调试信息:');
  console.log('==========================================');

  // 检查viewer对象
  console.log('1. Viewer对象状态:');
  console.log('  - viewer存在:', !!viewer);
  console.log('  - viewer类型:', typeof viewer);

  if (viewer) {
    console.log('  - viewer.scene存在:', !!viewer.scene);

    if (viewer.scene) {
      console.log('  - viewer.scene.camera存在:', !!viewer.scene.camera);
      console.log('  - viewer.scene.mode:', viewer.scene.mode);

      if (viewer.scene.camera) {
        const camera = viewer.scene.camera;
        console.log('  - camera.position:', camera.position);
        console.log('  - camera.positionCartographic存在:', !!camera.positionCartographic);

        if (camera.positionCartographic) {
          const cartographic = camera.positionCartographic;
          console.log('  - 相机经度:', DC.Math.toDegrees(cartographic.longitude).toFixed(6));
          console.log('  - 相机纬度:', DC.Math.toDegrees(cartographic.latitude).toFixed(6));
          console.log('  - 相机高度:', cartographic.height.toFixed(2), 'm');
        }

        console.log('  - computeViewRectangle方法存在:', typeof camera.computeViewRectangle === 'function');
      }
    }
  }

  // 检查DC SDK
  console.log('2. DC SDK状态:');
  console.log('  - DC对象存在:', typeof DC !== 'undefined');
  if (typeof DC !== 'undefined') {
    console.log('  - DC.Math存在:', !!DC.Math);
    console.log('  - DC.Math.toDegrees存在:', typeof DC.Math?.toDegrees === 'function');
  }

  // 检查天地图API
  console.log('3. 天地图API状态:');
  console.log('  - TIANDITU_KEY存在:', !!TIANDITU_KEY);
  console.log('  - TIANDITU_KEY长度:', TIANDITU_KEY?.length || 0);

  console.log('==========================================');
};

// 获取地图边界
const getMapBounds = () => {
  try {
    console.log('🔍 开始获取地图边界...');

    // 详细检查viewer对象的初始化状态
    if (!viewer) {
      console.warn('❌ viewer对象未初始化');
      return '108.2,22.6,108.6,23.0';
    }
    console.log('✅ viewer对象存在');

    if (!viewer.scene) {
      console.warn('❌ viewer.scene未初始化');
      return '108.2,22.6,108.6,23.0';
    }
    console.log('✅ viewer.scene存在');

    if (!viewer.scene.camera) {
      console.warn('❌ viewer.scene.camera未初始化');
      return '108.2,22.6,108.6,23.0';
    }
    console.log('✅ viewer.scene.camera存在');

    const camera = viewer.scene.camera;

    // 检查相机状态
    console.log('📷 相机状态信息:');
    console.log('  - 相机位置:', camera.position);
    console.log('  - 相机方向:', camera.direction);
    console.log('  - 相机上方向:', camera.up);
    console.log('  - 相机右方向:', camera.right);

    // 获取相机的地理位置信息
    if (camera.positionCartographic) {
      const cartographic = camera.positionCartographic;
      console.log('  - 相机地理位置:');
      console.log('    经度:', DC.Math.toDegrees(cartographic.longitude).toFixed(6));
      console.log('    纬度:', DC.Math.toDegrees(cartographic.latitude).toFixed(6));
      console.log('    高度:', cartographic.height.toFixed(2), 'm');
    }

    // 检查computeViewRectangle方法是否存在
    if (typeof camera.computeViewRectangle !== 'function') {
      console.error('❌ camera.computeViewRectangle方法不存在');
      return '108.2,22.6,108.6,23.0';
    }
    console.log('✅ camera.computeViewRectangle方法存在');

    // 尝试计算视野矩形
    console.log('🔄 正在计算视野矩形...');
    const rectangle = camera.computeViewRectangle();

    // 详细检查rectangle对象
    console.log('📐 视野矩形计算结果:', rectangle);

    if (!rectangle) {
      console.warn('❌ computeViewRectangle返回null，可能的原因:');
      console.warn('  - 相机视角过小或过大');
      console.warn('  - 相机位置异常');
      console.warn('  - 地图场景模式不支持');
      console.warn('  - 使用默认边界');
      return '108.2,22.6,108.6,23.0';
    }

    // 检查rectangle对象的属性
    console.log('📐 矩形对象详细信息:');
    console.log('  - west (弧度):', rectangle.west);
    console.log('  - south (弧度):', rectangle.south);
    console.log('  - east (弧度):', rectangle.east);
    console.log('  - north (弧度):', rectangle.north);

    // 检查弧度值是否有效
    if (isNaN(rectangle.west) || isNaN(rectangle.south) || isNaN(rectangle.east) || isNaN(rectangle.north)) {
      console.error('❌ 矩形坐标包含NaN值');
      return '108.2,22.6,108.6,23.0';
    }

    // 转换弧度到度数
    const westDeg = DC.Math.toDegrees(rectangle.west);
    const southDeg = DC.Math.toDegrees(rectangle.south);
    const eastDeg = DC.Math.toDegrees(rectangle.east);
    const northDeg = DC.Math.toDegrees(rectangle.north);

    console.log('🌍 转换为度数:');
    console.log('  - west (度):', westDeg);
    console.log('  - south (度):', southDeg);
    console.log('  - east (度):', eastDeg);
    console.log('  - north (度):', northDeg);

    // 检查度数值是否在有效范围内
    if (westDeg < -180 || westDeg > 180 || eastDeg < -180 || eastDeg > 180 || southDeg < -90 || southDeg > 90 || northDeg < -90 || northDeg > 90) {
      console.error('❌ 坐标超出有效范围');
      console.error('  有效范围: 经度[-180,180], 纬度[-90,90]');
      return '108.2,22.6,108.6,23.0';
    }

    // 保留6位小数精度
    const west = westDeg.toFixed(6);
    const south = southDeg.toFixed(6);
    const east = eastDeg.toFixed(6);
    const north = northDeg.toFixed(6);

    // 验证边界逻辑合理性
    if (parseFloat(west) >= parseFloat(east)) {
      console.error('❌ 西边界大于等于东边界:', west, '>=', east);
      return '108.2,22.6,108.6,23.0';
    }

    if (parseFloat(south) >= parseFloat(north)) {
      console.error('❌ 南边界大于等于北边界:', south, '>=', north);
      return '108.2,22.6,108.6,23.0';
    }

    const bounds = `${west},${south},${east},${north}`;
    console.log('✅ 成功计算地图边界:', bounds);

    // 计算边界范围大小
    const widthDeg = parseFloat(east) - parseFloat(west);
    const heightDeg = parseFloat(north) - parseFloat(south);
    console.log('📏 边界范围大小:');
    console.log('  - 宽度:', widthDeg.toFixed(6), '度');
    console.log('  - 高度:', heightDeg.toFixed(6), '度');

    return bounds;
  } catch (error) {
    console.error('❌ 获取地图边界时发生异常:', error);
    console.error('  错误堆栈:', error.stack);

    // 尝试备用方法获取边界
    console.log('🔄 尝试备用方法获取边界...');
    const fallbackBounds = getFallbackMapBounds();
    if (fallbackBounds !== '108.2,22.6,108.6,23.0') {
      console.log('✅ 备用方法成功获取边界:', fallbackBounds);
      return fallbackBounds;
    }

    return '108.2,22.6,108.6,23.0'; // 默认范围
  }
};

// 备用的地图边界获取方法
const getFallbackMapBounds = () => {
  try {
    if (!viewer || !viewer.scene || !viewer.scene.camera) {
      return '108.2,22.6,108.6,23.0';
    }

    const camera = viewer.scene.camera;

    // 方法1: 尝试使用相机位置和高度估算边界
    if (camera.positionCartographic) {
      const cartographic = camera.positionCartographic;
      const centerLng = DC.Math.toDegrees(cartographic.longitude);
      const centerLat = DC.Math.toDegrees(cartographic.latitude);
      const height = cartographic.height;

      // 根据高度估算视野范围（简单估算）
      let range = 0.01; // 默认范围
      if (height > 1000000) range = 5.0;
      else if (height > 500000) range = 2.0;
      else if (height > 100000) range = 1.0;
      else if (height > 50000) range = 0.5;
      else if (height > 10000) range = 0.1;
      else if (height > 5000) range = 0.05;

      const west = (centerLng - range).toFixed(6);
      const south = (centerLat - range).toFixed(6);
      const east = (centerLng + range).toFixed(6);
      const north = (centerLat + range).toFixed(6);

      console.log('📍 使用相机位置估算边界:', `${west},${south},${east},${north}`);
      return `${west},${south},${east},${north}`;
    }

    // 方法2: 如果相机位置也获取不到，使用固定的南宁范围
    return '108.2,22.6,108.6,23.0';
  } catch (error) {
    console.error('备用边界获取方法也失败:', error);
    return '108.2,22.6,108.6,23.0';
  }
};

// 获取当前地图级别
const getCurrentMapLevel = () => {
  try {
    console.log('🎯 开始获取地图级别...');

    if (!viewer || !viewer.scene || !viewer.scene.camera) {
      console.warn('❌ 地图未初始化，使用默认级别12');
      return 12;
    }

    const camera = viewer.scene.camera;

    // 检查positionCartographic是否存在
    if (!camera.positionCartographic) {
      console.warn('❌ camera.positionCartographic不存在，使用默认级别12');
      return 12;
    }

    const height = camera.positionCartographic.height;
    console.log('📏 相机高度:', height.toFixed(2), 'm');

    // 检查高度是否有效
    if (isNaN(height) || height < 0) {
      console.warn('❌ 相机高度无效:', height, '使用默认级别12');
      return 12;
    }

    // 根据相机高度估算地图级别（1-18）
    let level;
    if (height > 20000000) level = 1;
    else if (height > 10000000) level = 3;
    else if (height > 5000000) level = 5;
    else if (height > 2000000) level = 7;
    else if (height > 1000000) level = 9;
    else if (height > 500000) level = 11;
    else if (height > 200000) level = 13;
    else if (height > 100000) level = 15;
    else if (height > 50000) level = 17;
    else level = 18;

    console.log(`✅ 相机高度: ${height.toFixed(0)}m, 对应级别: ${level}`);

    // 验证级别范围
    if (level < 1 || level > 18) {
      console.warn('❌ 计算的级别超出范围:', level, '使用默认级别12');
      return 12;
    }

    return level;
  } catch (error) {
    console.error('❌ 获取地图级别时发生异常:', error);
    console.error('  错误堆栈:', error.stack);
    return 12; // 默认级别
  }
};

// 新的地图边界获取方法（基于网上示例的改进版本）
const getMapBoundsV2 = () => {
  try {
    console.log('🔍 [V2] 开始获取地图边界...');

    // 检查基本对象
    if (!viewer || !viewer.scene || !viewer.scene.camera) {
      console.warn('❌ [V2] 地图基本对象未初始化');
      return '108.2,22.6,108.6,23.0';
    }

    const camera = viewer.scene.camera;
    let params = {};

    // 方法1: 优先尝试使用 computeViewRectangle (3D模式下通常有效)
    console.log('🔄 [V2] 尝试方法1: computeViewRectangle...');
    const extend = camera.computeViewRectangle();

    if (typeof extend === 'undefined') {
      console.warn('⚠️ [V2] computeViewRectangle返回undefined，使用2D屏幕坐标转换方式');

      // 方法2: 2D下会可能拾取不到坐标，extend返回undefined，所以做以下转换
      if (!viewer.scene.canvas) {
        console.error('❌ [V2] viewer.scene.canvas不存在');
        return '108.2,22.6,108.6,23.0';
      }

      if (!viewer.scene.globe || !viewer.scene.globe.ellipsoid) {
        console.error('❌ [V2] viewer.scene.globe.ellipsoid不存在');
        return '108.2,22.6,108.6,23.0';
      }

      const canvas = viewer.scene.canvas;
      const upperLeft = new DC.Cartesian2(0, 0); // canvas左上角坐标转2d坐标
      const lowerRight = new DC.Cartesian2(canvas.clientWidth, canvas.clientHeight); // canvas右下角坐标转2d坐标
      const ellipsoid = viewer.scene.globe.ellipsoid;

      console.log('📐 [V2] Canvas尺寸:', canvas.clientWidth, 'x', canvas.clientHeight);

      const upperLeft3 = camera.pickEllipsoid(upperLeft, ellipsoid); // 2D转3D世界坐标
      const lowerRight3 = camera.pickEllipsoid(lowerRight, ellipsoid); // 2D转3D世界坐标

      if (!upperLeft3 || !lowerRight3) {
        console.error('❌ [V2] 屏幕坐标转3D世界坐标失败');
        return '108.2,22.6,108.6,23.0';
      }

      const upperLeftCartographic = ellipsoid.cartesianToCartographic(upperLeft3); // 3D世界坐标转弧度
      const lowerRightCartographic = ellipsoid.cartesianToCartographic(lowerRight3); // 3D世界坐标转弧度

      if (!upperLeftCartographic || !lowerRightCartographic) {
        console.error('❌ [V2] 3D世界坐标转弧度失败');
        return '108.2,22.6,108.6,23.0';
      }

      const minx = DC.Math.toDegrees(upperLeftCartographic.longitude); // 弧度转经纬度
      const maxx = DC.Math.toDegrees(lowerRightCartographic.longitude); // 弧度转经纬度
      const miny = DC.Math.toDegrees(lowerRightCartographic.latitude); // 弧度转经纬度
      const maxy = DC.Math.toDegrees(upperLeftCartographic.latitude); // 弧度转经纬度

      console.log('[V2] 2D方式 - 经度：' + minx + '----' + maxx);
      console.log('[V2] 2D方式 - 纬度：' + miny + '----' + maxy);

      params.minx = minx;
      params.maxx = maxx;
      params.miny = miny;
      params.maxy = maxy;
    } else {
      console.log('✅ [V2] computeViewRectangle成功，使用3D获取方式');

      // 3D获取方式
      params.maxx = DC.Math.toDegrees(extend.east);
      params.maxy = DC.Math.toDegrees(extend.north);
      params.minx = DC.Math.toDegrees(extend.west);
      params.miny = DC.Math.toDegrees(extend.south);

      console.log('[V2] 3D方式 - 经度：' + params.minx + '----' + params.maxx);
      console.log('[V2] 3D方式 - 纬度：' + params.miny + '----' + params.maxy);
    }

    // 验证坐标有效性
    if (isNaN(params.minx) || isNaN(params.maxx) || isNaN(params.miny) || isNaN(params.maxy)) {
      console.error('❌ [V2] 获取的坐标包含NaN');
      return '108.2,22.6,108.6,23.0';
    }

    // 验证坐标范围
    if (
      params.minx < -180 ||
      params.minx > 180 ||
      params.maxx < -180 ||
      params.maxx > 180 ||
      params.miny < -90 ||
      params.miny > 90 ||
      params.maxy < -90 ||
      params.maxy > 90
    ) {
      console.error('❌ [V2] 获取的坐标超出有效范围');
      return '108.2,22.6,108.6,23.0';
    }

    // 验证边界逻辑
    if (params.minx >= params.maxx || params.miny >= params.maxy) {
      console.error('❌ [V2] 获取的边界逻辑异常');
      return '108.2,22.6,108.6,23.0';
    }

    // 返回屏幕所在经纬度范围，格式为 "minx,miny,maxx,maxy"
    const bounds = `${params.minx.toFixed(6)},${params.miny.toFixed(6)},${params.maxx.toFixed(6)},${params.maxy.toFixed(6)}`;
    console.log('✅ [V2] 成功获取地图边界:', bounds);

    return bounds;
  } catch (error) {
    console.error('❌ [V2] 获取地图边界时发生异常:', error);
    console.error('  错误堆栈:', error.stack);
    return '108.2,22.6,108.6,23.0'; // 默认范围
  }
};

// 搜索相关状态
const searchResults = ref<any[]>([]);
const searchMarkers = ref<any[]>([]);

// 搜索缓存配置
const CACHE_EXPIRE_TIME = 5 * 60 * 1000; // 5分钟缓存
const searchCache = new Map<string, { data: any[]; timestamp: number }>();

// HTTP API搜索功能（新版本）
const performTiandituHttpSearch = async (keyword: string) => {
  try {
    console.log('🚀 开始HTTP API搜索:', keyword);

    // 调试地图状态
    debugMapStatus();

    // 清除之前的搜索结果
    clearSearchResults();

    // 获取当前地图边界和级别（使用新的V2方法）
    const mapBound = getMapBoundsV2();
    const level = getCurrentMapLevel();

    // 构造缓存键
    const cacheKey = `${keyword}_${mapBound}_${level}`;

    // 检查缓存
    const cachedResult = searchCache.get(cacheKey);
    if (cachedResult && Date.now() - cachedResult.timestamp < CACHE_EXPIRE_TIME) {
      console.log('🎯 使用缓存结果:', keyword);
      const searchData = cachedResult.data;

      // 更新搜索结果状态
      searchResults.value = searchData;

      // 在地图上显示搜索结果标记
      displaySearchMarkers(searchData);

      return searchData;
    }

    console.log('地图边界:', mapBound, '级别:', level);

    // 构造搜索参数
    const postStr = {
      keyWord: keyword,
      level: level.toString(),
      mapBound: mapBound,
      queryType: 1, // 普通搜索（含地铁公交）
      start: 0,
      count: 10,
      show: 2 // 返回详细POI信息
    };

    // 构造请求URL
    const url = `https://api.tianditu.gov.cn/v2/search?postStr=${encodeURIComponent(JSON.stringify(postStr))}&type=query&tk=${TIANDITU_KEY}`;

    console.log('请求URL:', url);

    // 发起HTTP请求
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      },
      // 添加超时控制
      signal: AbortSignal.timeout(10000) // 10秒超时
    });

    if (!response.ok) {
      let errorMessage = `HTTP请求失败: ${response.status} ${response.statusText}`;

      // 根据状态码提供更友好的错误信息
      switch (response.status) {
        case 400:
          errorMessage = '搜索参数错误，请检查输入';
          break;
        case 401:
          errorMessage = 'API密钥无效，请检查配置';
          break;
        case 403:
          errorMessage = 'API访问被拒绝，请检查权限';
          break;
        case 429:
          errorMessage = 'API调用频率过高，请稍后重试';
          break;
        case 500:
          errorMessage = '搜索服务暂时不可用，请稍后重试';
          break;
      }

      throw new Error(errorMessage);
    }

    const data = await response.json();
    console.log('HTTP API响应:', data);

    // 解析搜索结果
    const searchData = parseHttpApiResults(data);
    console.log('解析后的搜索数据:', searchData);

    // 缓存结果
    searchCache.set(cacheKey, {
      data: searchData,
      timestamp: Date.now()
    });

    // 清理过期缓存（简单的LRU策略）
    if (searchCache.size > 50) {
      const oldestKey = searchCache.keys().next().value;
      searchCache.delete(oldestKey);
    }

    // 更新搜索结果状态
    searchResults.value = searchData;

    // 在地图上显示搜索结果标记
    displaySearchMarkers(searchData);

    return searchData;
  } catch (error) {
    console.error('HTTP API搜索失败:', error);
    throw error;
  }
};



// 主搜索方法（简化版本，只使用HTTP API）
const performTiandituSearch = async (keyword: string) => {
  const startTime = performance.now();

  try {
    console.log(`🔍 开始搜索: "${keyword}"`);

    // 使用HTTP API搜索
    const results = await performTiandituHttpSearch(keyword);

    const endTime = performance.now();
    console.log(`✅ HTTP API搜索成功，耗时: ${(endTime - startTime).toFixed(0)}ms，结果数: ${results.length}`);

    return results;
  } catch (error) {
    const endTime = performance.now();
    console.error(`❌ 搜索失败，总耗时: ${(endTime - startTime).toFixed(0)}ms:`, error);
    throw error;
  }
};



// 解析HTTP API搜索结果
const parseHttpApiResults = (apiData) => {
  try {
    console.log('开始解析HTTP API结果:', apiData);

    // 检查API响应状态
    if (!apiData.status || apiData.status.infocode !== 1000) {
      const errorMsg = apiData.status?.cndesc || '搜索服务返回错误';
      console.error('API返回错误:', errorMsg);
      throw new Error(errorMsg);
    }

    const searchData = [];
    const resultType = apiData.resultType;

    console.log('结果类型:', resultType);

    switch (resultType) {
      case 1: // POI点数据
        if (apiData.pois && apiData.pois.length > 0) {
          apiData.pois.forEach((poi, index) => {
            try {
              const lnglatArr = poi.lonlat ? poi.lonlat.split(',') : [];
              if (lnglatArr.length >= 2) {
                const lng = parseFloat(lnglatArr[0]);
                const lat = parseFloat(lnglatArr[1]);

                // 验证坐标有效性
                if (!isNaN(lng) && !isNaN(lat) && lng !== 0 && lat !== 0) {
                  searchData.push({
                    id: poi.hotPointID || `poi_${index}`,
                    name: poi.name || '未知地点',
                    address: poi.address || '',
                    phone: poi.phone || '',
                    longitude: lng,
                    latitude: lat,
                    poiType: poi.typeName || poi.typeCode || '',
                    type: 'poi'
                  });
                }
              }
            } catch (error) {
              console.warn(`解析POI ${index} 失败:`, error);
            }
          });
        }
        break;

      case 2: // 统计数据
        if (apiData.statistics && apiData.statistics.priorityCitys) {
          apiData.statistics.priorityCitys.forEach((city, index) => {
            try {
              const lnglatArr = city.lonlat ? city.lonlat.split(',') : [];
              const lng = lnglatArr.length >= 2 ? parseFloat(lnglatArr[0]) : 0;
              const lat = lnglatArr.length >= 2 ? parseFloat(lnglatArr[1]) : 0;

              searchData.push({
                id: `city_${index}`,
                name: city.name,
                address: `${city.name} (${city.count}个结果)`,
                phone: '',
                longitude: lng,
                latitude: lat,
                adminCode: city.adminCode,
                count: city.count,
                type: 'city'
              });
            } catch (error) {
              console.warn(`解析城市统计 ${index} 失败:`, error);
            }
          });
        }
        break;

      case 3: // 行政区划
        if (apiData.area) {
          try {
            const lnglatArr = apiData.area.lonlat ? apiData.area.lonlat.split(',') : [];
            if (lnglatArr.length >= 2) {
              const lng = parseFloat(lnglatArr[0]);
              const lat = parseFloat(lnglatArr[1]);

              if (!isNaN(lng) && !isNaN(lat)) {
                searchData.push({
                  id: 'area_0',
                  name: apiData.area.name || '行政区',
                  address: apiData.area.name || '',
                  phone: '',
                  longitude: lng,
                  latitude: lat,
                  level: apiData.area.level,
                  type: 'area'
                });
              }
            }
          } catch (error) {
            console.warn('解析行政区划失败:', error);
          }
        }
        break;

      case 4: // 建议词
        if (apiData.suggests && apiData.suggests.length > 0) {
          apiData.suggests.forEach((suggest, index) => {
            try {
              searchData.push({
                id: `suggest_${index}`,
                name: suggest.name,
                address: suggest.address || '',
                phone: '',
                longitude: 0, // 建议词通常没有具体坐标
                latitude: 0,
                gbCode: suggest.gbCode,
                type: 'suggest'
              });
            } catch (error) {
              console.warn(`解析建议词 ${index} 失败:`, error);
            }
          });
        }
        break;

      case 5: // 线路结果
        if (apiData.lineData && apiData.lineData.length > 0) {
          apiData.lineData.forEach((line, index) => {
            try {
              searchData.push({
                id: line.uuid || `line_${index}`,
                name: line.name,
                address: `${line.name} (${line.stationNum}站)`,
                phone: '',
                longitude: 0, // 线路没有单一坐标
                latitude: 0,
                stationNum: line.stationNum,
                type: 'line'
              });
            } catch (error) {
              console.warn(`解析线路 ${index} 失败:`, error);
            }
          });
        }
        break;

      default:
        console.warn('未知的结果类型:', resultType);
        break;
    }

    console.log(`HTTP API解析完成，共 ${searchData.length} 个结果`);
    return searchData;
  } catch (error) {
    console.error('解析HTTP API结果失败:', error);
    throw error;
  }
};



// 在地图上显示搜索结果标记
const displaySearchMarkers = (searchData) => {
  console.log(`🗺️ 开始显示 ${searchData.length} 个搜索结果的地图标记`);

  // 确保有overlay图层
  if (!viewer.overlayLayer) {
    viewer.overlayLayer = new DC.VectorLayer('overlay').addTo(viewer);
  }

  // 收集有效坐标点用于设置视野
  const validPoints = [];

  searchData.forEach((item, index) => {
    // 验证坐标有效性
    const lng = parseFloat(item.longitude);
    const lat = parseFloat(item.latitude);

    // 检查坐标是否有效：不为NaN、不为0、在有效范围内
    if (!isNaN(lng) && !isNaN(lat) && lng !== 0 && lat !== 0 && lng >= -180 && lng <= 180 && lat >= -90 && lat <= 90) {
      try {
        const position = new DC.Position(lng, lat);

        // 创建标记点
        const marker = new DC.Point(position);
        marker.setStyle({
          pixelSize: 10,
          color: DC.Color.CYAN,
          outlineColor: DC.Color.WHITE,
          outlineWidth: 2,
          heightReference: DC.HeightReference.CLAMP_TO_GROUND
        });

        // 创建标签
        const label = new DC.Label(position, `${index + 1}. ${item.name}`);
        label.setStyle({
          fillColor: DC.Color.WHITE,
          font: '14px Microsoft YaHei',
          backgroundColor: DC.Color.fromCssColorString('rgba(0,0,0,0.7)'),
          backgroundPadding: new DC.Cartesian2(8, 4),
          pixelOffset: new DC.Cartesian2(0, -30)
        });

        // 添加到地图
        viewer.overlayLayer.addOverlay(marker);
        viewer.overlayLayer.addOverlay(label);

        // 保存标记引用
        searchMarkers.value.push({ marker, label, data: item });
        validPoints.push(position);

        console.log(`✓ 添加标记 ${index + 1}: ${item.name}`);
      } catch (error) {
        console.warn(`✗ 创建标记失败 ${index + 1}: ${item.name}`, error);
      }
    } else {
      console.log(`⚠ 跳过无效坐标 ${index + 1}: ${item.name}`);
    }
  });

  // 如果有有效坐标点，调整地图视野
  if (validPoints.length > 0) {
    if (validPoints.length === 1) {
      // 单个点，使用Cesium原生flyTo方法避免DC SDK的矩形计算问题
      const point = validPoints[0];
      try {
        // 使用Cesium原生camera.flyTo，避开DC SDK的flyToPosition
        viewer.scene.camera.flyTo({
          destination: DC.Cartesian3.fromDegrees(point.lng, point.lat, 15000), // 15km高度
          duration: 2.0,
          complete: () => {
            console.log('已定位到搜索结果');
          }
        });
      } catch (error) {
        console.warn('Cesium flyTo失败，尝试DC SDK方法:', error);
        // 降级到原来的方法
        viewer.flyToPosition(
          validPoints[0],
          () => {
            console.log('已定位到搜索结果');
          },
          1
        );
      }
    } else {
      // 多个点，计算边界并飞行到包含所有点的视野
      try {
        // 计算所有点的边界
        const lngs = validPoints.map((p) => p.lng);
        const lats = validPoints.map((p) => p.lat);

        const minLng = Math.min(...lngs);
        const maxLng = Math.max(...lngs);
        const minLat = Math.min(...lats);
        const maxLat = Math.max(...lats);

        // 添加适当的边距，避免点贴边显示
        const lngSpan = maxLng - minLng;
        const latSpan = maxLat - minLat;
        const lngPadding = Math.max(lngSpan * 0.1, 0.001); // 至少0.001度边距
        const latPadding = Math.max(latSpan * 0.1, 0.001);

        // 构建边界数组 [minX, minY, maxX, maxY]
        const bounds = [
          minLng - lngPadding, // minX (最小经度)
          minLat - latPadding, // minY (最小纬度)
          maxLng + lngPadding, // maxX (最大经度)
          maxLat + latPadding // maxY (最大纬度)
        ];

        console.log(`飞行到边界: [${bounds.map((b) => b.toFixed(6)).join(', ')}]`);

        // 使用 flyToBounds 飞行到包含所有搜索结果的视野
        viewer.flyToBounds(
          bounds,
          {
            heading: 0, // 正北方向
            pitch: -90, // 俯视角度
            roll: 0 // 无倾斜
          },
          () => {
            console.log(`已显示所有 ${validPoints.length} 个搜索结果`);
          },
          2
        ); // 2秒飞行时间
      } catch (error) {
        console.warn('飞行到边界失败，使用第一个点定位:', error);
        // 如果flyToBounds失败，降级到飞行到第一个点
        const point = validPoints[0];
        try {
          viewer.scene.camera.flyTo({
            destination: DC.Cartesian3.fromDegrees(point.lng, point.lat, 15000),
            duration: 2.0,
            complete: () => {
              console.log('已定位到第一个搜索结果');
            }
          });
        } catch (error) {
          console.warn('降级flyTo也失败:', error);
          viewer.flyToPosition(
            validPoints[0],
            () => {
              console.log('已定位到第一个搜索结果');
            },
            1
          );
        }
      }
    }
  } else {
    console.log('没有有效的搜索结果坐标可以显示');
  }
};

// 清除搜索结果
const clearSearchResults = () => {
  console.log('🗑️ 清除搜索结果标记');
  
  if (viewer.overlayLayer) {
    searchMarkers.value.forEach(({ marker, label }) => {
      try {
        viewer.overlayLayer.removeOverlay(marker);
        viewer.overlayLayer.removeOverlay(label);
      } catch (error) {
        console.warn('清除搜索标记失败:', error);
      }
    });
  }
  
  searchMarkers.value = [];
  searchResults.value = [];
};

// 飞行到指定位置
const flyToPosition = (position: { lng: number; lat: number; height?: number }) => {
  if (!viewer) {
    console.warn('地图未初始化');
    return;
  }

  try {
    // 优先使用Cesium原生camera.flyTo，避免DC SDK的矩形计算问题
    viewer.scene.camera.flyTo({
      destination: DC.Cartesian3.fromDegrees(position.lng, position.lat, position.height || 15000),
      duration: 2.0,
      complete: () => {
        console.log(`已飞行到位置: ${position.lng}, ${position.lat}`);
      }
    });
  } catch (error) {
    console.warn('Cesium flyTo失败，尝试DC SDK方法:', error);
    // 降级到DC SDK的方法
    if (viewer.flyToPosition) {
      const dcPosition = new DC.Position(position.lng, position.lat, position.height || 1000);
      viewer.flyToPosition(
        dcPosition,
        () => {
          console.log(`已飞行到位置: ${position.lng}, ${position.lat}`);
        },
        1
      );
    } else {
      console.error('所有定位方法都不可用');
    }
  }
};

// 清理搜索缓存
const clearSearchCache = () => {
  searchCache.clear();
  console.log('搜索缓存已清理');
};

// 暴露给父组件的方法
defineExpose({
  startLocationPicking,
  stopLocationPicking,
  flyToPosition,
  performTiandituSearch,
  clearSearchResults,
  searchResults: readonly(searchResults),
  searchMarkers: readonly(searchMarkers)
});

const initViewer = () => {
  viewer = new DC.Viewer('cesium-map-view__container', {
    sceneMode: 2,
    contextOptions: {
      webgl: {
        alpha: true
      }
    }
  });
  viewerRef.value = viewer;

  viewer.scene.skyBox.destroy();
  viewer.scene.skyBox = undefined;
  viewer.scene.sun.destroy();
  viewer.scene.sun = undefined;
  viewer.scene.moon.destroy();
  viewer.scene.moon = undefined;
  viewer.scene.skyAtmosphere.destroy();
  viewer.scene.skyAtmosphere = undefined;

  viewer.scene.backgroundColor = new DC.Color(0, 0, 0, 0);

  const effect = new DC.Effect(viewer);
  effect.brightness.enable = true;
  effect.brightness.intensity = 0.8;

  // viewer.setOptions({
  //     "showAtmosphere": false, //是否显示大气层
  //     "showSun": false, //是否显示太阳
  //     "showMoon": false, //是否显示月亮
  //     "skyBox": {
  //         show: false
  //     }
  // })
  // let baseLayer = DC.ImageryLayerFactory.createImageryLayer(DC.ImageryType.AMAP)
  // viewer.addBaseLayer(baseLayer)
  // let cva = DC.ImageryLayerFactory.createImageryLayer(DC.ImageryType.TDT, {
  //   key: '7563ce11662dbf857b471ad2b01c3ca0',
  //   style: 'cva'
  // });
  // let img = DC.ImageryLayerFactory.createImageryLayer(DC.ImageryType.TDT, {
  //   key: '7563ce11662dbf857b471ad2b01c3ca0',
  //   style: 'img'
  // });
  // viewer.addBaseLayer([img, cva]);
  // let baselayer_2 = DC.ImageryLayerFactory.createImageryLayer(DC.ImageryType.TENCENT, {
  //   style: 4
  // });
  // viewer.addBaseLayer(baselayer_2, {
  //   name: '墨渊',
  //   iconUrl: 'examples/assets/icon/dark.png'
  // });

  const baseLayer_img = DC.ImageryLayerFactory.createImageryLayer(DC.ImageryType.TDT, {
    style: 'img',
    key: TIANDITU_KEY
  });
  const baseLayer_cva = DC.ImageryLayerFactory.createImageryLayer(DC.ImageryType.TDT, {
    style: 'cva',
    key: TIANDITU_KEY
  });
  const baselayer_vec = DC.ImageryLayerFactory.createImageryLayer(DC.ImageryType.TENCENT, {
    style: 2
  });
  viewer.addBaseLayer([baseLayer_img, baseLayer_cva], {
    name: '影像',
    iconUrl: 'examples/assets/icon/img.png'
  });
  // 设置地图中心为南宁
  const pos = new DC.Position(108.3661, 22.8178, 170000, 0, -90);
  viewer.flyToPosition(pos, () => {}, 1);

  // 添加地图点击事件监听
  viewer.on(DC.MouseEventType.CLICK, handleMapClick);
};

const patrolAreas = ref<PatrolAreaInfo[]>([]);

const initAirport = () => {
  const layer_build = new DC.TilesetLayer('layer_build').addTo(viewer);
  const build = new DC.Tileset('//resource.dvgis.cn/data/3dtiles/ljz/tileset.json', { skipLevels: true });
  const customShader = new DC.CustomShader({
    fragmentShaderText: `
      void fragmentMain(FragmentInput fsInput, inout czm_modelMaterial material) {
         vec4 position = czm_inverseModelView * vec4(fsInput.attributes.positionEC,1); // 位置
         float glowRange = 100.0; // 光环的移动范围(高度)
         vec4 temp = vec4(0.2,  0.5, 1.0, 1.0); // 颜色
         temp *= vec4(vec3(position.z / 100.0), 1.0);  // 渐变
         // 动态光环
         float time = fract(czm_frameNumber / 360.0);
         time = abs(time - 0.5) * 2.0;
         float diff = step(0.005, abs( clamp(position.z / glowRange, 0.0, 1.0) - time));
         material.diffuse = vec3(temp.rgb + temp.rgb * (1.0 - diff)) ;
       }
      `
  });
  build.setCustomShader(customShader);
  layer_build.addOverlay(build);
  const layerGroup = new DC.LayerGroup('layerGroup').addTo(viewer);
  const layer = new DC.VectorLayer('airportRange');
  const layer_label = new DC.VectorLayer('airportRangeLabel');
  defaultRoute.forEach((item) => {
    const center = DC.Position.fromObject({ lng: item.content.geometry.coordinates[0], lat: item.content.geometry.coordinates[1] });
    const circle = new DC.Circle(center, item.content.geometry.radius);
    circle.setStyle({
      material: new DC.RadarLineMaterialProperty({
        color: new DC.Color(0, 1.0, 1.0, 0.8),
        speed: 8.0
      })
    });
    const label = new DC.Label(center, item.name);
    label.setStyle({
      fillColor: DC.Color.WHITE,
      font: '24px'
    });
    layer.addOverlay(circle);
    layer_label.addOverlay(label);
  });
  layerGroup.addLayer(layer);
  layerGroup.addLayer(layer_label);
  // 保持广西中心视角，不飞行到特定图层
  // viewer.flyTo(layer, 2);
};

onMounted(async () => {
  await DC.ready();
  initViewer();
  initAirport();
  // if (viewerRef.value) {
  //   usePatrolAreaRenderer(viewerRef, patrolAreas);

  //   // Fetch patrol data
  //   fetchPatrolAreaData().then((data) => {
  //     patrolAreas.value = data;
  //   });
  // }
});
onUnmounted(() => {
  // 清理选点状态
  stopLocationPicking();

  // 清理搜索缓存
  searchCache.clear();

  // 销毁地图实例
  if (viewer && typeof viewer.destroy === 'function') {
    viewer.destroy();
  }
});
</script>

<style lang="scss" scoped>
#cesium-map-view__container {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  // background-image: url('@/assets/images/base-map-bg.png');
  // background-size: cover;
  // background-position: center;
  // background-repeat: no-repeat;
}
</style>
