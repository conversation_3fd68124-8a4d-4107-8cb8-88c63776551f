<template>
  <div id="cesium-map-view__container">
    <!-- <img :src="BGMAP" alt="BGMAP" /> -->
  </div>

</template>

<script setup lang="ts">
import { shallowRef, ref, onMounted, onUnmounted } from 'vue';
import { usePatrolAreaRenderer, fetchPatrolAreaData, PatrolAreaInfo } from '@/hooks/usePatrolAreaRenderer';
import * as DC from '@dvgis/dc-sdk';
import '@dvgis/dc-sdk/dist/dc.min.css';
import { defaultRoute } from '@/utils';
let viewer = {};
let viewerRef = shallowRef(null);
let currentBaseLayers = [];

const initViewer = async () => {
  viewer = new DC.Viewer('cesium-map-view__container', {
    sceneMode: 2,
    contextOptions: {
      webgl: {
        alpha: true
      }
    }
  });
  viewerRef.value = viewer;

  viewer.scene.skyBox.destroy();
  viewer.scene.skyBox = undefined;
  viewer.scene.sun.destroy();
  viewer.scene.sun = undefined;
  viewer.scene.moon.destroy();
  viewer.scene.moon = undefined;
  viewer.scene.skyAtmosphere.destroy();
  viewer.scene.skyAtmosphere = undefined;

  viewer.scene.backgroundColor = new DC.Color(0, 0, 0, 0);

  const effect = new DC.Effect(viewer);
  effect.brightness.enable = true;
  effect.brightness.intensity = 0.8;

  // viewer.setOptions({
  //     "showAtmosphere": false, //是否显示大气层
  //     "showSun": false, //是否显示太阳
  //     "showMoon": false, //是否显示月亮
  //     "skyBox": {
  //         show: false
  //     }
  // })
  // let baseLayer = DC.ImageryLayerFactory.createImageryLayer(DC.ImageryType.AMAP)
  // viewer.addBaseLayer(baseLayer)
  // let cva = DC.ImageryLayerFactory.createImageryLayer(DC.ImageryType.TDT, {
  //   key: '7563ce11662dbf857b471ad2b01c3ca0',
  //   style: 'cva'
  // });
  // let img = DC.ImageryLayerFactory.createImageryLayer(DC.ImageryType.TDT, {
  //   key: '7563ce11662dbf857b471ad2b01c3ca0',
  //   style: 'img'
  // });
  // viewer.addBaseLayer([img, cva]);
  // let baselayer_2 = DC.ImageryLayerFactory.createImageryLayer(DC.ImageryType.TENCENT, {
  //   style: 4
  // });
  // viewer.addBaseLayer(baselayer_2, {
  //   name: '墨渊',
  //   iconUrl: 'examples/assets/icon/dark.png'
  // });
  // 初始化默认底图（影像）
  await switchBaseLayer('satellite');
  // 设置地图中心为南宁
  const pos = new DC.Position(108.3661, 22.8178, 600000, 0, -90);
  viewer.flyToPosition(pos, () => {}, 1);
};

const patrolAreas = ref<PatrolAreaInfo[]>([]);

// 底图切换功能
const switchBaseLayer = async (layerType: string) => {
  // 检查viewer是否已初始化
  if (!viewer || !viewer.addBaseLayer) {
    console.warn('地图viewer未初始化，无法切换底图');
    return;
  }

  try {
    // 移除当前的基础底图
    if (currentBaseLayers.length > 0) {
      currentBaseLayers.forEach((layer) => {
        if (viewer.removeBaseLayer) {
          viewer.removeBaseLayer(layer);
        }
      });
      currentBaseLayers = [];
    }

    const tdtKey = 'e66ccbaf369e1f09a8f34d7b1be1c22f';

    switch (layerType) {
      case 'vector': // 地图
        {
          const vecLayer = await DC.ImageryLayerFactory.createImageryLayer(DC.ImageryType.TDT, {
            key: tdtKey,
            style: 'elec'
          });
          viewer.addBaseLayer(vecLayer, {
            name: '电子地图'
          });
          currentBaseLayers = [vecLayer];
        }
        break;
      case 'satellite': // 影像
        {
          const imgLayer = await DC.ImageryLayerFactory.createImageryLayer(DC.ImageryType.TDT, {
            key: tdtKey,
            style: 'img'
          });
          viewer.addBaseLayer(imgLayer, {
            name: '卫星影像'
          });
          currentBaseLayers = [imgLayer];
        }
        break;
      case 'terrain': // 地形
        {
          const terLayer = await DC.ImageryLayerFactory.createImageryLayer(DC.ImageryType.TDT, {
            key: tdtKey,
            style: 'ter'
          });
          viewer.addBaseLayer(terLayer, {
            name: '地形地图'
          });
          currentBaseLayers = [terLayer];
        }
        break;
      case 'surface': // 地表覆盖 (使用影像)
        {
          const imgLayer = await DC.ImageryLayerFactory.createImageryLayer(DC.ImageryType.TDT, {
            key: tdtKey,
            style: 'img'
          });
          viewer.addBaseLayer(imgLayer, {
            name: '地表覆盖'
          });
          currentBaseLayers = [imgLayer];
        }
        break;
      case '3d': // 三维（使用影像作为基础）
        {
          const imgLayer = await DC.ImageryLayerFactory.createImageryLayer(DC.ImageryType.TDT, {
            key: tdtKey,
            style: 'img'
          });
          viewer.addBaseLayer(imgLayer, {
            name: '三维影像'
          });
          currentBaseLayers = [imgLayer];

          // 切换到3D模式
          if (viewer.changeSceneMode) {
            viewer.changeSceneMode(3);
          }
        }
        break;
      default:
        console.warn('未知的图层类型:', layerType);
        return;
    }

    // 如果不是3D模式，确保在2D模式
    if (layerType !== '3d' && viewer.changeSceneMode) {
      viewer.changeSceneMode(2);
    }

    console.log(`已切换到${layerType}底图`);
  } catch (error) {
    console.error('切换底图失败:', error);
  }
};

// 暴露切换方法到全局，供其他组件调用
window.switchMapBaseLayer = switchBaseLayer;

const initAirport = () => {
  let layer_build = new DC.TilesetLayer('layer_build').addTo(viewer);
  let build = new DC.Tileset('//resource.dvgis.cn/data/3dtiles/ljz/tileset.json', { skipLevels: true });
  let customShader = new DC.CustomShader({
    fragmentShaderText: `
      void fragmentMain(FragmentInput fsInput, inout czm_modelMaterial material) {
         vec4 position = czm_inverseModelView * vec4(fsInput.attributes.positionEC,1); // 位置
         float glowRange = 100.0; // 光环的移动范围(高度)
         vec4 temp = vec4(0.2,  0.5, 1.0, 1.0); // 颜色
         temp *= vec4(vec3(position.z / 100.0), 1.0);  // 渐变
         // 动态光环
         float time = fract(czm_frameNumber / 360.0);
         time = abs(time - 0.5) * 2.0;
         float diff = step(0.005, abs( clamp(position.z / glowRange, 0.0, 1.0) - time));
         material.diffuse = vec3(temp.rgb + temp.rgb * (1.0 - diff)) ;
       }
      `
  });
  build.setCustomShader(customShader);
  layer_build.addOverlay(build);
  let layerGroup = new DC.LayerGroup('layerGroup').addTo(viewer);
  let layer = new DC.VectorLayer('airportRange');
  let layer_label = new DC.VectorLayer('airportRangeLabel');
  defaultRoute.forEach((item) => {
    let center = DC.Position.fromObject({ lng: item.content.geometry.coordinates[0], lat: item.content.geometry.coordinates[1] });
    let circle = new DC.Circle(center, item.content.geometry.radius);
    circle.setStyle({
      material: new DC.RadarLineMaterialProperty({
        color: new DC.Color(0, 1.0, 1.0, 0.8),
        speed: 8.0
      })
    });
    let label = new DC.Label(center, item.name);
    label.setStyle({
      fillColor: DC.Color.WHITE,
      font: '24px'
    });
    layer.addOverlay(circle);
    layer_label.addOverlay(label);
  });
  layerGroup.addLayer(layer);
  layerGroup.addLayer(layer_label);
  // 保持广西中心视角，不飞行到特定图层
  // viewer.flyTo(layer, 2);
};

onMounted(async () => {
  await DC.ready();
  await initViewer();
  initAirport();
  // if (viewerRef.value) {
  //   usePatrolAreaRenderer(viewerRef, patrolAreas);

  //   // Fetch patrol data
  //   fetchPatrolAreaData().then((data) => {
  //     patrolAreas.value = data;
  //   });
  // }
});
onUnmounted(() => {
  viewer?.destroy();
});
</script>

<style lang="scss" scoped>
#cesium-map-view__container {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  pointer-events: auto;
  // background-image: url('@/assets/images/base-map-bg.png');
  // background-size: cover;
  // background-position: center;
  // background-repeat: no-repeat;
}
</style>
