<template>
  <div id="cesium-map-view__container">
    <!-- <img :src="BGMAP" alt="BGMAP" /> -->
  </div>

</template>

<script setup lang="ts">
import { shallowRef, ref, onMounted, onUnmounted } from 'vue';
import { usePatrolAreaRenderer, fetchPatrolAreaData, PatrolAreaInfo } from '@/hooks/usePatrolAreaRenderer';
import * as DC from '@dvgis/dc-sdk';
import '@dvgis/dc-sdk/dist/dc.min.css';
import { defaultRoute } from '@/utils';
let viewer = {};
let viewerRef = shallowRef(null);
let currentBaseLayers = [];

const initViewer = async () => {
  viewer = new DC.Viewer('cesium-map-view__container', {
    sceneMode: 2,
    contextOptions: {
      webgl: {
        alpha: true
      }
    }
  });
  viewerRef.value = viewer;

  viewer.scene.skyBox.destroy();
  viewer.scene.skyBox = undefined;
  viewer.scene.sun.destroy();
  viewer.scene.sun = undefined;
  viewer.scene.moon.destroy();
  viewer.scene.moon = undefined;
  viewer.scene.skyAtmosphere.destroy();
  viewer.scene.skyAtmosphere = undefined;

  viewer.scene.backgroundColor = new DC.Color(0, 0, 0, 0);

  const effect = new DC.Effect(viewer);
  effect.brightness.enable = true;
  effect.brightness.intensity = 0.8;

  // viewer.setOptions({
  //     "showAtmosphere": false, //是否显示大气层
  //     "showSun": false, //是否显示太阳
  //     "showMoon": false, //是否显示月亮
  //     "skyBox": {
  //         show: false
  //     }
  // })
  // let baseLayer = DC.ImageryLayerFactory.createImageryLayer(DC.ImageryType.AMAP)
  // viewer.addBaseLayer(baseLayer)
  // let cva = DC.ImageryLayerFactory.createImageryLayer(DC.ImageryType.TDT, {
  //   key: '7563ce11662dbf857b471ad2b01c3ca0',
  //   style: 'cva'
  // });
  // let img = DC.ImageryLayerFactory.createImageryLayer(DC.ImageryType.TDT, {
  //   key: '7563ce11662dbf857b471ad2b01c3ca0',
  //   style: 'img'
  // });
  // viewer.addBaseLayer([img, cva]);
  // let baselayer_2 = DC.ImageryLayerFactory.createImageryLayer(DC.ImageryType.TENCENT, {
  //   style: 4
  // });
  // viewer.addBaseLayer(baselayer_2, {
  //   name: '墨渊',
  //   iconUrl: 'examples/assets/icon/dark.png'
  // });
  // 使用DC-SDK的addBaseLayer初始化默认底图 - 卫星地图
  const key = 'ce71c20df9039e68af51c6977ced1f28';
  
  // 创建卫星底图作为默认底图
  const imgLayer = DC.ImageryLayerFactory.createImageryLayer(DC.ImageryType.TDT, { key, style: 'img' });
  const ciaLayer = DC.ImageryLayerFactory.createImageryLayer(DC.ImageryType.TDT, { key, style: 'cia' });
  
  // 使用addBaseLayer方法添加底图
  viewer.addBaseLayer([imgLayer, ciaLayer], { 
    name: '卫星地图', 
    iconUrl: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3QgeD0iMyIgeT0iMyIgd2lkdGg9IjE4IiBoZWlnaHQ9IjE4IiByeD0iMiIgcnk9IjIiIHN0cm9rZT0iY3VycmVudENvbG9yIiBzdHJva2Utd2lkdGg9IjIiLz4KPGNpcmNsZSBjeD0iOC41IiBjeT0iOC41IiByPSIxLjUiIGZpbGw9ImN1cnJlbnRDb2xvciIvPgo8cGF0aCBkPSJsIDIxIDEwLjUtMTEuNSIgc3Ryb2tlPSJjdXJyZW50Q29sb3IiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPg=='
  });

  console.log('✅ 使用官方推荐方式加载底图完成');
  // 设置地图中心为南宁
  const pos = new DC.Position(108.3661, 22.8178, 600000, 0, -90);
  viewer.flyToPosition(pos, () => {}, 1);
};

const patrolAreas = ref<PatrolAreaInfo[]>([]);

// 预定义底图配置
const basemapConfigs = {
  vector: {
    base: { type: DC.ImageryType.TDT, options: { key: 'ce71c20df9039e68af51c6977ced1f28' } },
    annotation: { type: DC.ImageryType.TDT, options: { key: 'ce71c20df9039e68af51c6977ced1f28', style: 'cva' } }
  },
  satellite: {
    base: { type: DC.ImageryType.TDT, options: { key: 'ce71c20df9039e68af51c6977ced1f28', style: 'img' } },
    annotation: { type: DC.ImageryType.TDT, options: { key: 'ce71c20df9039e68af51c6977ced1f28', style: 'cia' } }
  },
  terrain: {
    base: { type: DC.ImageryType.TDT, options: { key: 'ce71c20df9039e68af51c6977ced1f28', style: 'ter' } },
    annotation: { type: DC.ImageryType.TDT, options: { key: 'ce71c20df9039e68af51c6977ced1f28', style: 'cta' } }
  }
};

// 底图切换功能 - 使用DC-SDK的addBaseLayer方法
const switchBaseLayer = async (layerType: string) => {
  console.log(`🔄 切换到 ${layerType} 底图...`);
  
  if (!viewer || !basemapConfigs[layerType]) {
    console.warn('❌ Viewer未初始化或未知的图层类型:', layerType);
    return;
  }

  try {
    // 1. 移除现有底图 - 使用DC-SDK方法
    if (viewer.removeAllBaseLayers) {
      viewer.removeAllBaseLayers();
    }
    
    // 2. 获取底图配置
    const config = basemapConfigs[layerType];
    
    // 3. 创建并添加新底图
    const baseLayer = DC.ImageryLayerFactory.createImageryLayer(config.base.type, config.base.options);
    const annotationLayer = DC.ImageryLayerFactory.createImageryLayer(config.annotation.type, config.annotation.options);
    
    // 获取底图名称
    const layerNames = {
      vector: '矢量地图',
      satellite: '卫星地图', 
      terrain: '地形地图'
    };
    
    viewer.addBaseLayer([baseLayer, annotationLayer], { 
      name: layerNames[layerType] || layerType,
      iconUrl: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJMMTMuMDkgOC4yNkwyMCA5TDEzLjA5IDE1Ljc0TDEyIDIyTDEwLjkxIDE1Ljc0TDQgOUwxMC45MSA4LjI2TDEyIDJaIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+'
    });
    
    console.log(`✅ 成功切换到: ${layerType} 底图`);
    
  } catch (error) {
    console.error('❌ 切换底图时发生错误:', error);
    if (window.ElMessage) {
      window.ElMessage.error(`底图切换失败: ${error.message || '未知错误'}`);
    }
  }
};

// 获取当前底图信息
const getCurrentBasemapInfo = () => {
  return {
    layerCount: viewer?.baseLayerPicker?.viewModel?.imageryProviderViewModels?.length || 0
  };
};

// 暴露方法到全局，供其他组件调用
window.switchMapBaseLayer = switchBaseLayer;
window.getCurrentBasemapInfo = getCurrentBasemapInfo;

const initAirport = () => {
  let layer_build = new DC.TilesetLayer('layer_build').addTo(viewer);
  let build = new DC.Tileset('//resource.dvgis.cn/data/3dtiles/ljz/tileset.json', { skipLevels: true });
  let customShader = new DC.CustomShader({
    fragmentShaderText: `
      void fragmentMain(FragmentInput fsInput, inout czm_modelMaterial material) {
         vec4 position = czm_inverseModelView * vec4(fsInput.attributes.positionEC,1); // 位置
         float glowRange = 100.0; // 光环的移动范围(高度)
         vec4 temp = vec4(0.2,  0.5, 1.0, 1.0); // 颜色
         temp *= vec4(vec3(position.z / 100.0), 1.0);  // 渐变
         // 动态光环
         float time = fract(czm_frameNumber / 360.0);
         time = abs(time - 0.5) * 2.0;
         float diff = step(0.005, abs( clamp(position.z / glowRange, 0.0, 1.0) - time));
         material.diffuse = vec3(temp.rgb + temp.rgb * (1.0 - diff)) ;
       }
      `
  });
  build.setCustomShader(customShader);
  layer_build.addOverlay(build);
  let layerGroup = new DC.LayerGroup('layerGroup').addTo(viewer);
  let layer = new DC.VectorLayer('airportRange');
  let layer_label = new DC.VectorLayer('airportRangeLabel');
  defaultRoute.forEach((item) => {
    let center = DC.Position.fromObject({ lng: item.content.geometry.coordinates[0], lat: item.content.geometry.coordinates[1] });
    let circle = new DC.Circle(center, item.content.geometry.radius);
    circle.setStyle({
      material: new DC.RadarLineMaterialProperty({
        color: new DC.Color(0, 1.0, 1.0, 0.8),
        speed: 8.0
      })
    });
    let label = new DC.Label(center, item.name);
    label.setStyle({
      fillColor: DC.Color.WHITE,
      font: '24px'
    });
    layer.addOverlay(circle);
    layer_label.addOverlay(label);
  });
  layerGroup.addLayer(layer);
  layerGroup.addLayer(layer_label);
  // 保持广西中心视角，不飞行到特定图层
  // viewer.flyTo(layer, 2);
};

onMounted(async () => {
  await DC.ready();
  await initViewer();
  initAirport();

  // if (viewerRef.value) {
  //   usePatrolAreaRenderer(viewerRef, patrolAreas);

  //   // Fetch patrol data
  //   fetchPatrolAreaData().then((data) => {
  //     patrolAreas.value = data;
  //   });
  // }
});
onUnmounted(() => {
  viewer?.destroy();
});
</script>

<style lang="scss" scoped>
#cesium-map-view__container {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  pointer-events: auto;
  // background-image: url('@/assets/images/base-map-bg.png');
  // background-size: cover;
  // background-position: center;
  // background-repeat: no-repeat;
}
</style>
