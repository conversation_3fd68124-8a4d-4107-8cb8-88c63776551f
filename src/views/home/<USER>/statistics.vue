<template>
  <panelComponent width="500px" height="219px" :bg="panelBg" headerTop="19px">
    <template #header> 飞行统计 </template>
    <!-- 原有内容 -->
    <div class="main">
      <div class="flexCol mt20" style="margin-left: 47px">
        <div class="number-and-unit">
          <span style="color: #0af0ff" class="text">{{stats.totalTaskCount}}</span>
          <span class="unit">次</span>
        </div>
        <div class="label-container" style="margin-left: 13px;">
          <div class="block" style="border: 1px solid #0af0ff"><div class="inner-block" style="background: #0af0ff"></div></div>
          <span class="label-text">任务总次数</span>
        </div>
      </div>
      <div class="flexCol mt20" style="margin-left: 133px">
        <div class="number-and-unit">
          <span style="color: #19fcde" class="text">{{stats.totalDurationHours}}</span>
          <span class="unit">h</span>
        </div>
        <div class="label-container">
          <div class="block" style="border: 1px solid #19fcde"><div class="inner-block" style="background: #19fcde"></div></div>
          <span class="label-text">任务总时长</span>
        </div>
      </div>
    </div>
    <!-- ... -->
  </panelComponent>
</template>

<script setup lang="ts">
import panelComponent from '@/views/home/<USER>/panelComponent.vue';
import panelBg from '@/assets/images/statisticsPanelBg.png';
import { ref, nextTick, computed, onMounted } from 'vue';
import { flightTaskCountStatistics } from '@/api/biz/flightTaskResult';

// 获取飞行统计信息
const stats = ref({
  totalTaskCount: 0,
  totalDurationHours: 0
});
async function fetchData() {
  await nextTick(); // 等待DOM更新
  const response = await flightTaskCountStatistics();
  stats.value = {
    totalTaskCount: response.totalTaskCount || 0,
    totalDurationHours: response.totalDurationHours || 0
  };
}

// 在组件挂载时调用接口获取数据
onMounted(() => {
  fetchData();
});

</script>

<style scoped>
.main {
  background: url('@/assets/images/statisticsBg.png') no-repeat center center;
  background-size: 100% 100%;
  width: 446px;
  height: 118px;
  color: white;
  margin: 71px 27px 16px 27px;
  display: flex;
  /* justify-content: space-around; */
}
.flexCol {
  display: flex;
  flex-direction: column;
}
.number-and-unit {
  display: flex;
  font-family: 'CustomFont';
  align-items: center;
  margin-bottom: 15px;
}
.text {
  font-size: 35px;
  margin-right: 7px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.unit {
  font-size: 16px;
  color: white;
}
.label-container {
  display: flex;
  align-items: center;
}
.block {
  width: 6px;
  height: 16px;
  margin-right: 8px;
  position: relative;
}
.inner-block {
  width: 4px;
  height: 10px;
  position: absolute;
  bottom: 0;
}
.mileage-icon {
  background-color: #0af0ff;
}
.duration-icon {
  background-color: #19fcde;
}
.label-text {
  font-size: 14px;
  color: white;
  font-family: 'Source Han Sans CN', Arial, sans-serif;
}
</style>
