<template>
  <div
    v-if="visible"
    class="floating-map-search"
    @click.stop
  >
    <!-- Main search box -->
    <div class="search-box">
      <!-- Search input container -->
      <div class="search-input-container">
        <input
          ref="searchInputRef"
          v-model="searchState.keyword"
          type="text"
          class="search-input"
          :placeholder="placeholder"
          @keydown="handleKeyDown"
          @input="handleInput"
          @focus="handleInputFocus"
          @blur="handleInputBlur"
        />
        
        <!-- Search button -->
        <button
          class="search-button"
          :class="{ loading: searchState.loading }"
          :disabled="searchState.loading || !searchState.keyword.trim()"
          @click="handleSearch"
          title="搜索"
        >
          <svg v-if="!searchState.loading" class="search-icon" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
            <path d="M124.884 109.812L94.256 79.166c-.357-.357-.757-.629-1.129-.914a50.366 50.366 0 0 0 8.186-27.59C101.327 22.689 78.656 0 50.67 0 22.685 0 0 22.688 0 50.663c0 27.989 22.685 50.663 50.656 50.663 10.186 0 19.643-3.03 27.6-8.201.286.385.557.771.9 1.114l30.628 30.632a10.633 10.633 0 0 0 7.543 3.129c2.728 0 5.457-1.043 7.543-3.115 4.171-4.157 4.171-10.915.014-15.073M50.671 85.338C31.557 85.338 16 69.78 16 50.663c0-19.102 15.557-34.661 34.67-34.661 19.115 0 34.657 15.559 34.657 34.675 0 19.102-15.557 34.661-34.656 34.661" fill="currentColor"/>
          </svg>
          <span v-else class="loading-spinner" />
        </button>
        
        <!-- Close button -->
        <button
          class="close-button"
          @click="handleClose"
          title="关闭搜索"
        >
          <svg class="close-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M18.3 5.71a.996.996 0 0 0-1.41 0L12 10.59 7.11 5.7A.996.996 0 1 0 5.7 7.11L10.59 12 5.7 16.89a.996.996 0 1 0 1.41 1.41L12 13.41l4.89 4.89a.996.996 0 1 0 1.41-1.41L13.41 12l4.89-4.89c.38-.38.38-1.02 0-1.4z" fill="currentColor"/>
          </svg>
        </button>
      </div>
      
      <!-- Search results dropdown -->
      <div
        v-if="searchState.showResults"
        class="search-results"
        role="listbox"
        :aria-label="`搜索结果，共 ${searchState.results.length} 项`"
      >
        <!-- Loading state -->
        <div v-if="searchState.loading" class="results-loading" role="status" aria-live="polite">
          <div class="loading-spinner" aria-hidden="true" />
          <span>搜索中...</span>
        </div>
        
        <!-- Error state -->
        <div v-else-if="searchState.error" class="results-error" role="alert" aria-live="assertive">
          <span class="error-icon" aria-hidden="true">⚠️</span>
          <span>{{ searchState.error }}</span>
          <button 
            v-if="retryCount < SEARCH_CONFIG.retryAttempts"
            class="retry-button"
            @click="handleSearch"
            :disabled="searchState.loading"
          >
            重试
          </button>
        </div>
        
        <!-- Empty state -->
        <div v-else-if="searchState.results.length === 0" class="results-empty" role="status">
          <span class="empty-icon" aria-hidden="true">📍</span>
          <div class="empty-content">
            <div class="empty-title">未找到相关结果</div>
            <div class="empty-suggestion">
              请尝试：
              <ul class="suggestion-list">
                <li>使用更简洁的关键词</li>
                <li>检查拼写是否正确</li>
                <li>尝试使用地标或知名建筑名称</li>
              </ul>
            </div>
          </div>
        </div>
        
        <!-- Results list -->
        <div v-else class="results-list">
          <!-- Results header -->
          <div class="results-header">
            <span class="results-count">找到 {{ searchState.results.length }} 个结果</span>
            <button 
              class="clear-results-button"
              @click="clearResults"
              title="清除搜索结果"
            >
              清除
            </button>
          </div>
          
          <!-- Results items -->
          <div
            v-for="(result, index) in searchState.results"
            :key="result.id"
            class="result-item"
            :class="{ 
              selected: searchState.selectedIndex === index,
              hovered: hoveredIndex === index 
            }"
            role="option"
            :aria-selected="searchState.selectedIndex === index"
            :tabindex="searchState.selectedIndex === index ? 0 : -1"
            @click="handleResultSelect(result, index)"
            @mouseenter="hoveredIndex = index"
            @mouseleave="hoveredIndex = -1"
          >
            <div class="result-content">
              <div class="result-name" :title="result.name">{{ result.name }}</div>
              <div class="result-address" :title="result.address">{{ result.address }}</div>
              <div class="result-meta">
                <span v-if="result.type" class="result-type">{{ getResultTypeLabel(result.type) }}</span>
                <span v-if="result.phone" class="result-phone" :title="result.phone">📞 {{ result.phone }}</span>
                <span v-if="hasValidCoordinates(result)" class="result-coordinates">
                  📍 {{ formatCoordinates(result.longitude, result.latitude) }}
                </span>
              </div>
            </div>
            <div class="result-actions">
              <div class="result-index">{{ index + 1 }}</div>
              <button 
                class="result-action-button"
                @click.stop="handleResultSelect(result, index)"
                :title="`定位到 ${result.name}`"
              >
                定位
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick, onMounted, onUnmounted } from 'vue';

// Props interface
interface FloatingMapSearchProps {
  visible: boolean;
  mapViewRef?: any;
  position?: {
    top?: string;
    left?: string;
  };
  placeholder?: string;
}

// Emits interface
interface FloatingMapSearchEmits {
  (e: 'update:visible', visible: boolean): void;
  (e: 'search-result-selected', result: SearchResult): void;
  (e: 'search-closed'): void;
}

// Search result interface
interface SearchResult {
  id: string;
  name: string;
  address: string;
  longitude: number;
  latitude: number;
  type: 'poi' | 'city' | 'area' | 'suggest' | 'line';
  phone?: string;
  poiType?: string;
  adminCode?: string;
  count?: number;
  gbCode?: string;
  level?: string;
}

// Search state interface
interface SearchState {
  keyword: string;
  results: SearchResult[];
  loading: boolean;
  showResults: boolean;
  selectedIndex: number;
  error: string | null;
}

// Props with defaults
const props = withDefaults(defineProps<FloatingMapSearchProps>(), {
  visible: false,
  mapViewRef: null,
  position: () => ({ top: '20px', left: '20px' }),
  placeholder: '搜索地点、地址或关键词...'
});

// Emits
const emit = defineEmits<FloatingMapSearchEmits>();

// Template refs
const searchInputRef = ref<HTMLInputElement | null>(null);

// Reactive state
const searchState = reactive<SearchState>({
  keyword: '',
  results: [],
  loading: false,
  showResults: false,
  selectedIndex: -1,
  error: null
});

// Local state
const hoveredIndex = ref(-1);
const searchTimeout = ref<number | null>(null);
const debounceTimeout = ref<number | null>(null);
const retryCount = ref(0);

// Configuration
const SEARCH_CONFIG = {
  debounceDelay: 300, // 300ms debounce delay for search input
  searchTimeout: 10000, // 10 second timeout for search requests
  maxResults: 20, // Maximum number of results to display
  minSearchLength: 1, // Minimum characters required to trigger search
  retryAttempts: 2 // Number of retry attempts for failed searches
};

// Watch for visibility changes to auto-focus input
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    nextTick(() => {
      if (searchInputRef.value) {
        searchInputRef.value.focus();
      }
    });
  } else {
    // Reset state when hidden
    resetSearchState();
  }
});

// Input event handlers
const handleInput = () => {
  // Clear previous debounce timeout
  if (debounceTimeout.value) {
    clearTimeout(debounceTimeout.value);
    debounceTimeout.value = null;
  }

  // Clear error state when user starts typing
  searchState.error = null;

  const keyword = searchState.keyword.trim();

  // If input is empty, hide results and clear data
  if (!keyword) {
    searchState.showResults = false;
    searchState.results = [];
    searchState.selectedIndex = -1;
    
    // Clear search markers from map if available
    if (props.mapViewRef && typeof props.mapViewRef.clearSearchResults === 'function') {
      props.mapViewRef.clearSearchResults();
    }
    return;
  }

  // Input validation - check for minimum length and valid characters
  if (keyword.length < 1) {
    return;
  }

  // Basic input sanitization - remove potentially harmful characters
  const sanitizedKeyword = keyword.replace(/[<>\"'&]/g, '');
  if (sanitizedKeyword !== keyword) {
    searchState.keyword = sanitizedKeyword;
    return;
  }

  console.log('输入变化，准备搜索:', keyword);

  // Debounce search with enhanced delay logic
  const delay = keyword.length <= 2 ? SEARCH_CONFIG.debounceDelay + 200 : SEARCH_CONFIG.debounceDelay;
  
  debounceTimeout.value = window.setTimeout(() => {
    // Double-check that the keyword hasn't changed during debounce
    if (searchState.keyword.trim() === keyword) {
      handleSearch();
    }
  }, delay);
};

const handleSearch = async () => {
  const keyword = searchState.keyword.trim();
  if (!keyword) return;

  // Clear previous search timeout
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value);
  }

  try {
    searchState.loading = true;
    searchState.showResults = true;
    searchState.error = null;
    searchState.selectedIndex = -1;

    console.log('开始搜索:', keyword);

    // Set search timeout with proper error handling
    searchTimeout.value = window.setTimeout(() => {
      if (searchState.loading) {
        searchState.loading = false;
        searchState.error = '搜索超时，请稍后重试';
        console.warn('搜索超时:', keyword);
      }
    }, SEARCH_CONFIG.searchTimeout);

    // Call map view search method if available
    if (props.mapViewRef && typeof props.mapViewRef.performTiandituSearch === 'function') {
      let results;
      let searchError = null;
      
      // Retry logic for network failures
      for (let attempt = 0; attempt <= SEARCH_CONFIG.retryAttempts; attempt++) {
        try {
          if (attempt > 0) {
            console.log(`搜索重试 ${attempt}/${SEARCH_CONFIG.retryAttempts}:`, keyword);
            // Add a small delay before retry
            await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
          }
          
          results = await props.mapViewRef.performTiandituSearch(keyword);
          searchError = null;
          break; // Success, exit retry loop
          
        } catch (error) {
          searchError = error;
          console.warn(`搜索尝试 ${attempt + 1} 失败:`, error);
          
          // Don't retry for certain types of errors
          if (error instanceof Error && 
              (error.message.includes('API密钥') || 
               error.message.includes('不可用') ||
               error.message.includes('格式错误'))) {
            break; // Don't retry for configuration errors
          }
        }
      }
      
      // If all retries failed, throw the last error
      if (searchError) {
        throw searchError;
      }
      
      // Clear timeout on success
      if (searchTimeout.value) {
        clearTimeout(searchTimeout.value);
        searchTimeout.value = null;
      }

      // Reset retry count on success
      retryCount.value = 0;

      // Validate and process results
      if (Array.isArray(results)) {
        searchState.results = results.slice(0, SEARCH_CONFIG.maxResults);
        searchState.loading = false;
        
        console.log(`搜索完成，找到 ${results.length} 个结果`);
        
        if (results.length === 0) {
          searchState.error = null; // Let empty state show instead
        }
      } else {
        throw new Error('搜索结果格式错误');
      }
    } else {
      throw new Error('地图搜索功能不可用，请确保地图已正确初始化');
    }
  } catch (error) {
    // Clear timeout on error
    if (searchTimeout.value) {
      clearTimeout(searchTimeout.value);
      searchTimeout.value = null;
    }

    searchState.loading = false;
    
    // Enhanced error handling with specific error messages
    let errorMessage = '搜索失败，请重试';
    
    if (error instanceof Error) {
      if (error.message.includes('超时')) {
        errorMessage = '搜索超时，请检查网络连接后重试';
      } else if (error.message.includes('API密钥')) {
        errorMessage = '地图服务配置错误，请联系管理员';
      } else if (error.message.includes('不可用')) {
        errorMessage = '地图搜索服务暂时不可用';
      } else {
        errorMessage = error.message;
      }
    }
    
    searchState.error = errorMessage;
    console.error('搜索错误:', error);
  }
};

const handleKeyDown = (event: KeyboardEvent) => {
  switch (event.key) {
    case 'Enter':
      event.preventDefault();
      if (searchState.showResults && searchState.selectedIndex >= 0 && searchState.results[searchState.selectedIndex]) {
        // Select the highlighted result
        handleResultSelect(searchState.results[searchState.selectedIndex], searchState.selectedIndex);
      } else if (searchState.keyword.trim() && !searchState.loading) {
        // Perform search if there's a keyword and not currently loading
        handleSearch();
      }
      break;
      
    case 'ArrowDown':
      event.preventDefault();
      if (searchState.showResults && searchState.results.length > 0) {
        // Move selection down, wrap to first item if at end
        if (searchState.selectedIndex < searchState.results.length - 1) {
          searchState.selectedIndex = searchState.selectedIndex + 1;
        } else {
          searchState.selectedIndex = 0; // Wrap to first item
        }
        console.log('键盘导航 - 向下:', searchState.selectedIndex);
      }
      break;
      
    case 'ArrowUp':
      event.preventDefault();
      if (searchState.showResults && searchState.results.length > 0) {
        // Move selection up, wrap to last item if at beginning
        if (searchState.selectedIndex > 0) {
          searchState.selectedIndex = searchState.selectedIndex - 1;
        } else if (searchState.selectedIndex === 0) {
          searchState.selectedIndex = searchState.results.length - 1; // Wrap to last item
        } else {
          searchState.selectedIndex = searchState.results.length - 1; // Start from last item
        }
        console.log('键盘导航 - 向上:', searchState.selectedIndex);
      }
      break;
      
    case 'Escape':
      event.preventDefault();
      if (searchState.showResults) {
        // First ESC closes results dropdown
        searchState.showResults = false;
        searchState.selectedIndex = -1;
      } else {
        // Second ESC closes the entire search interface
        handleClose();
      }
      break;
      
    case 'Tab':
      // Allow tab navigation but close results dropdown
      if (searchState.showResults) {
        searchState.showResults = false;
        searchState.selectedIndex = -1;
      }
      break;
  }
};

const handleInputFocus = () => {
  if (searchState.keyword.trim() && searchState.results.length > 0) {
    searchState.showResults = true;
  }
};

const handleInputBlur = () => {
  // Delay hiding results to allow for result clicks
  setTimeout(() => {
    if (hoveredIndex.value === -1) {
      searchState.showResults = false;
    }
  }, 150);
};

// Handle clicks outside the search component
const handleClickOutside = (event: MouseEvent) => {
  const searchElement = document.querySelector('.floating-map-search');
  if (searchElement && !searchElement.contains(event.target as Node)) {
    // Only close results dropdown, not the entire search interface
    if (searchState.showResults) {
      searchState.showResults = false;
      searchState.selectedIndex = -1;
    }
  }
};

// Result selection handler
const handleResultSelect = (result: SearchResult, index: number) => {
  console.log('选择搜索结果:', result);
  
  searchState.selectedIndex = index;
  searchState.showResults = false;
  
  // Validate result coordinates
  const lng = parseFloat(String(result.longitude));
  const lat = parseFloat(String(result.latitude));
  
  if (isNaN(lng) || isNaN(lat) || lng === 0 || lat === 0) {
    console.warn('选择的结果坐标无效:', result);
    searchState.error = '该位置坐标无效，无法在地图上显示';
    return;
  }

  // Fly to the selected location using mapView's method
  if (props.mapViewRef && typeof props.mapViewRef.flyToPosition === 'function') {
    try {
      props.mapViewRef.flyToPosition({
        lng,
        lat,
        height: 1000 // Default height for better view
      });
      console.log(`飞行到选中位置: ${lng}, ${lat}`);
    } catch (error) {
      console.error('飞行到位置失败:', error);
      searchState.error = '无法定位到该位置';
    }
  }
  
  // Emit result selection event for parent component handling
  emit('search-result-selected', {
    ...result,
    longitude: lng,
    latitude: lat
  });
  
  // Update search input with selected result name
  searchState.keyword = result.name;
  
  // Clear any previous errors
  searchState.error = null;
};

// UI event handlers
const handleClose = () => {
  emit('update:visible', false);
  emit('search-closed');
};

// Utility functions
const getResultTypeLabel = (type: string): string => {
  const typeLabels: Record<string, string> = {
    poi: 'POI',
    city: '城市',
    area: '区域',
    suggest: '建议',
    line: '线路'
  };
  return typeLabels[type] || type;
};

const hasValidCoordinates = (result: SearchResult): boolean => {
  const lng = parseFloat(String(result.longitude));
  const lat = parseFloat(String(result.latitude));
  return !isNaN(lng) && !isNaN(lat) && lng !== 0 && lat !== 0;
};

const formatCoordinates = (longitude: number, latitude: number): string => {
  const lng = parseFloat(String(longitude));
  const lat = parseFloat(String(latitude));
  return `${lng.toFixed(4)}, ${lat.toFixed(4)}`;
};

const clearResults = () => {
  console.log('清除搜索结果');
  searchState.showResults = false;
  searchState.results = [];
  searchState.selectedIndex = -1;
  searchState.error = null;
  
  // Clear search markers from map
  if (props.mapViewRef && typeof props.mapViewRef.clearSearchResults === 'function') {
    props.mapViewRef.clearSearchResults();
  }
};

const resetSearchState = () => {
  console.log('重置搜索状态');
  
  // Clear search data
  searchState.keyword = '';
  searchState.results = [];
  searchState.loading = false;
  searchState.showResults = false;
  searchState.selectedIndex = -1;
  searchState.error = null;
  hoveredIndex.value = -1;
  
  // Clear all timeouts
  if (debounceTimeout.value) {
    clearTimeout(debounceTimeout.value);
    debounceTimeout.value = null;
  }
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value);
    searchTimeout.value = null;
  }
  
  // Clear search results from map if available
  if (props.mapViewRef && typeof props.mapViewRef.clearSearchResults === 'function') {
    try {
      props.mapViewRef.clearSearchResults();
      console.log('已清除地图搜索标记');
    } catch (error) {
      console.warn('清除地图搜索标记失败:', error);
    }
  }
};

// Global event handlers
const handleGlobalKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && props.visible) {
    handleClose();
  }
};

// Lifecycle hooks
onMounted(() => {
  document.addEventListener('keydown', handleGlobalKeyDown);
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener('keydown', handleGlobalKeyDown);
  document.removeEventListener('click', handleClickOutside);
  resetSearchState();
});
</script>

<style lang="scss" scoped>
.floating-map-search {
  position: fixed;
  top: v-bind('props.position?.top || "20px"');
  left: v-bind('props.position?.left || "20px"');
  z-index: 1000; // High z-index to overlay map but not block critical controls
  pointer-events: auto; // Allow interactions with the search box
  
  // Fade-in animation for the entire search interface
  animation: searchInterfaceFadeIn 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

@keyframes searchInterfaceFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.search-box {
  position: relative;
  width: 400px;
  max-width: calc(100vw - 40px);
  background: rgba(255, 255, 255, 0.98); // More opaque background for better visibility
  backdrop-filter: blur(16px); // Stronger blur effect
  -webkit-backdrop-filter: blur(16px); // Safari support
  border-radius: 12px;
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.15), // Stronger main shadow
    0 4px 12px rgba(0, 0, 0, 0.1),   // Medium shadow
    0 1px 4px rgba(0, 0, 0, 0.08),   // Subtle close shadow
    inset 0 1px 0 rgba(255, 255, 255, 0.7), // Brighter inset highlight
    0 0 0 1px rgba(255, 255, 255, 0.3); // Subtle outer border
  border: 1px solid rgba(255, 255, 255, 0.4); // More visible border
  pointer-events: auto;
  
  // Enhanced fade-in animation with scale and blur
  animation: searchBoxFadeIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
  transform-origin: top left;
  
  // Add a subtle glow effect to make it stand out more
  &::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.1), rgba(0, 123, 255, 0.05));
    border-radius: 14px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  &:hover::before {
    opacity: 1;
  }
  
  // Ensure results dropdown has proper z-index
  .search-results {
    z-index: 1002;
  }
}

@keyframes searchBoxFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
    filter: blur(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0px);
  }
}

.search-input-container {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  background: rgba(255, 255, 255, 0.8); // More opaque for better contrast
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 12px 12px 0 0;
  position: relative;
  
  // Enhanced inner glow effect
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
  }
  
  // Add focus-within effect for better interaction feedback
  &:focus-within {
    background: rgba(255, 255, 255, 0.9);
    border-bottom-color: rgba(0, 123, 255, 0.2);
    
    &::after {
      background: linear-gradient(90deg, transparent, rgba(0, 123, 255, 0.3), transparent);
    }
  }
}

.search-input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 16px;
  background: transparent;
  color: #333;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  
  &::placeholder {
    color: #999;
    transition: color 0.3s ease;
  }
  
  &:focus {
    color: #000;
    
    &::placeholder {
      color: #bbb;
    }
  }
  
  // Enhanced focus state with subtle glow
  &:focus {
    text-shadow: 0 0 8px rgba(0, 123, 255, 0.1);
  }
}

.search-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  margin-left: 8px;
  border: none;
  border-radius: 8px;
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
  position: relative;
  overflow: hidden;
  
  // Hover state with enhanced effects
  &:hover:not(:disabled) {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    transform: translateY(-1px) scale(1.05);
    box-shadow: 0 4px 16px rgba(0, 123, 255, 0.4);
    
    .search-icon {
      transform: scale(1.1);
    }
  }
  
  // Active/pressed state
  &:active:not(:disabled) {
    transform: translateY(0) scale(1.02);
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
  }
  
  // Focus state for accessibility
  &:focus-visible {
    outline: 2px solid rgba(0, 123, 255, 0.5);
    outline-offset: 2px;
  }
  
  &:disabled {
    background: linear-gradient(135deg, #ccc 0%, #999 100%);
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  }
  
  &.loading {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    cursor: wait;
    
    // Subtle pulse animation during loading
    animation: loadingPulse 2s ease-in-out infinite;
  }
}

@keyframes loadingPulse {
  0%, 100% {
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
  }
  50% {
    box-shadow: 0 2px 12px rgba(0, 123, 255, 0.5);
  }
}

.search-icon {
  width: 16px;
  height: 16px;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.close-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  margin-left: 8px;
  border: none;
  border-radius: 50%; // 圆形按钮
  background: rgba(0, 0, 0, 0.06);
  color: #666;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
  
  // 添加微妙的内阴影效果
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  
  // Enhanced hover state
  &:hover {
    background: rgba(255, 68, 68, 0.1);
    color: #ff4444;
    transform: translateY(-1px) scale(1.08);
    box-shadow: 
      0 3px 12px rgba(255, 68, 68, 0.25),
      inset 0 1px 2px rgba(255, 68, 68, 0.1);
    
    .close-icon {
      transform: rotate(90deg) scale(1.1);
    }
  }
  
  // Active/pressed state
  &:active {
    transform: translateY(0) scale(1.05);
    background: rgba(255, 68, 68, 0.15);
    box-shadow: 
      0 1px 4px rgba(255, 68, 68, 0.3),
      inset 0 2px 4px rgba(255, 68, 68, 0.2);
  }
  
  // Focus state for accessibility
  &:focus-visible {
    outline: 2px solid rgba(255, 68, 68, 0.5);
    outline-offset: 2px;
  }
}

.close-icon {
  width: 16px;
  height: 16px;
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.search-results {
  max-height: 400px;
  overflow-y: auto;
  border-radius: 0 0 12px 12px;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  
  // Enhanced shadow for dropdown
  box-shadow: 
    0 8px 24px rgba(0, 0, 0, 0.12),
    0 2px 8px rgba(0, 0, 0, 0.08);
  
  // Smooth slide-down animation when results appear
  animation: resultsSlideIn 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  transform-origin: top;
}

@keyframes resultsSlideIn {
  from {
    opacity: 0;
    transform: scaleY(0.8) translateY(-10px);
    filter: blur(2px);
  }
  to {
    opacity: 1;
    transform: scaleY(1) translateY(0);
    filter: blur(0px);
  }
}

.results-loading,
.results-error,
.results-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px;
  color: #666;
  font-size: 14px;
  gap: 10px;
  
  // Fade-in animation for state messages
  animation: stateMessageFadeIn 0.3s ease-out forwards;
}

@keyframes stateMessageFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.results-loading {
  .loading-spinner {
    animation: spin 1s linear infinite, loadingGlow 2s ease-in-out infinite;
  }
}

@keyframes loadingGlow {
  0%, 100% {
    filter: drop-shadow(0 0 2px rgba(0, 123, 255, 0.3));
  }
  50% {
    filter: drop-shadow(0 0 6px rgba(0, 123, 255, 0.6));
  }
}

.results-error {
  color: #ff4444;
  background: rgba(255, 68, 68, 0.05);
  border-radius: 8px;
  margin: 8px;
  
  .error-icon {
    animation: errorPulse 2s ease-in-out infinite;
  }
}

@keyframes errorPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.results-empty {
  background: rgba(0, 0, 0, 0.02);
  border-radius: 8px;
  margin: 8px;
  padding: 24px;
  text-align: center;
  
  .empty-icon {
    opacity: 0.7;
    animation: emptyBounce 3s ease-in-out infinite;
    font-size: 24px;
    margin-bottom: 12px;
    display: block;
  }
  
  .empty-content {
    .empty-title {
      font-size: 16px;
      font-weight: 500;
      color: #666;
      margin-bottom: 12px;
    }
    
    .empty-suggestion {
      font-size: 14px;
      color: #888;
      text-align: left;
      
      .suggestion-list {
        margin: 8px 0 0 0;
        padding-left: 16px;
        
        li {
          margin: 4px 0;
          color: #999;
        }
      }
    }
  }
}

@keyframes emptyBounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-2px);
  }
}

.results-error {
  .retry-button {
    margin-left: 12px;
    padding: 4px 12px;
    border: 1px solid #ff4444;
    border-radius: 4px;
    background: transparent;
    color: #ff4444;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
    
    &:hover:not(:disabled) {
      background: #ff4444;
      color: white;
    }
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
}

.error-icon,
.empty-icon {
  font-size: 20px;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  background: rgba(0, 0, 0, 0.02);
  
  .results-count {
    font-size: 12px;
    color: #666;
    font-weight: 500;
  }
  
  .clear-results-button {
    padding: 4px 8px;
    border: none;
    border-radius: 4px;
    background: rgba(0, 0, 0, 0.05);
    color: #666;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
    
    &:hover {
      background: rgba(255, 68, 68, 0.1);
      color: #ff4444;
    }
  }
}

.results-list {
  padding: 8px 0;
}

.result-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border-left: 3px solid transparent;
  position: relative;
  overflow: hidden;
  
  // Enhanced hover state with subtle animations
  &:hover,
  &.hovered {
    background: rgba(0, 123, 255, 0.08);
    transform: translateX(4px);
    box-shadow: inset 0 0 0 1px rgba(0, 123, 255, 0.1);
    
    .result-index {
      transform: scale(1.1);
      box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
    }
    
    .result-name {
      color: #007bff;
    }
  }
  
  // Enhanced selected state
  &.selected {
    background: rgba(0, 123, 255, 0.12);
    border-left-color: #007bff;
    transform: translateX(6px);
    box-shadow: inset 0 0 0 1px rgba(0, 123, 255, 0.2);
    
    .result-index {
      background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
      transform: scale(1.15);
      box-shadow: 0 3px 12px rgba(0, 123, 255, 0.4);
    }
    
    .result-name {
      color: #007bff;
      font-weight: 600;
    }
  }
  
  // Subtle slide-in animation for each result item
  animation: resultItemSlideIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  opacity: 0;
  transform: translateX(-20px);
  
  // Staggered animation delay for each item
  &:nth-child(1) { animation-delay: 0.05s; }
  &:nth-child(2) { animation-delay: 0.1s; }
  &:nth-child(3) { animation-delay: 0.15s; }
  &:nth-child(4) { animation-delay: 0.2s; }
  &:nth-child(5) { animation-delay: 0.25s; }
  &:nth-child(n+6) { animation-delay: 0.3s; }
}

@keyframes resultItemSlideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.result-content {
  flex: 1;
  min-width: 0;
}

.result-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.3;
}

.result-address {
  font-size: 14px;
  color: #666;
  margin-bottom: 6px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
}

.result-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  
  .result-type {
    font-size: 11px;
    color: #007bff;
    background: rgba(0, 123, 255, 0.1);
    padding: 2px 6px;
    border-radius: 3px;
    display: inline-block;
    font-weight: 500;
  }
  
  .result-phone {
    font-size: 11px;
    color: #28a745;
    background: rgba(40, 167, 69, 0.1);
    padding: 2px 6px;
    border-radius: 3px;
    display: inline-block;
  }
  
  .result-coordinates {
    font-size: 11px;
    color: #6c757d;
    background: rgba(108, 117, 125, 0.1);
    padding: 2px 6px;
    border-radius: 3px;
    display: inline-block;
    font-family: monospace;
  }
}

.result-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  margin-left: 12px;
  flex-shrink: 0;
}

.result-index {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  border-radius: 50%;
  font-size: 12px;
  font-weight: bold;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 2px 6px rgba(0, 123, 255, 0.2);
}

.result-action-button {
  padding: 4px 8px;
  border: 1px solid #007bff;
  border-radius: 4px;
  background: transparent;
  color: #007bff;
  cursor: pointer;
  font-size: 11px;
  font-weight: 500;
  transition: all 0.2s ease;
  white-space: nowrap;
  
  &:hover {
    background: #007bff;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
  }
  
  &:active {
    transform: translateY(0);
  }
}

// Responsive design
@media (max-width: 768px) {
  .search-box {
    width: calc(100vw - 20px);
    left: 10px;
    right: 10px;
  }
  
  .search-input {
    font-size: 16px; // Prevent zoom on iOS
  }
  
  .result-item {
    padding: 16px;
  }
  
  .search-button,
  .close-button {
    width: 44px;
    height: 44px;
  }
}
</style>