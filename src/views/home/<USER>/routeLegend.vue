<template>
  <!-- 右下角航线标注模块，在css中调整位置-->
  <div class="route-legend-outer">
    <div class="route-legend-inner">
      <div class="legend-item"><span class="legend-color" style="background: #0af0ff"></span> 应急航线</div>
      <div class="legend-item"><span class="legend-color" style="background: #06f7a1"></span> 应急航线</div>
      <div class="legend-item"><span class="legend-color" style="background: #a259ff"></span> 应急航线</div>
      <div class="legend-item"><span class="legend-color" style="background: #19fcde"></span> 应急航线</div>
    </div>
  </div>
</template>

<style lang="scss">
.route-legend-outer {
  position: absolute;
  right: 579px;
  bottom: 50px;
  width: 146px;
  height: 155px;
  background: rgba(20, 30, 40, 0.25);
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.18);
  z-index: 100;
  pointer-events: none;
  backdrop-filter: blur(8px);
  border: 1.5px solid rgba(120, 150, 170, 0.18);
  display: none;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}
.route-legend-inner {
  width: 112px;
  height: 121px;
  // background: rgba(10, 18, 32, 0.85);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding: 12px 12px;
  gap: 8px;
  pointer-events: auto;
  box-sizing: border-box;
  overflow: hidden;
}
.legend-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #fff;
  margin-bottom: 4px;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  letter-spacing: 1px;
  white-space: nowrap;
}
.legend-item:last-child {
  margin-bottom: 0;
}
.legend-color {
  display: inline-block;
  width: 21px;
  height: 5px;
  border-radius: 3px;
  margin-right: 8px;
}
</style>
