<template>
  <div class="base-layer-switcher">
    <div class="switcher-header">
      <span class="title">图层切换</span>
    </div>
    <div class="layer-options">
      <div 
        v-for="layer in layerTypes" 
        :key="layer.value"
        class="layer-option"
        :class="{ active: currentLayer === layer.value }"
        @click="switchLayer(layer.value)"
      >
        <div class="layer-icon">
          <i :class="layer.icon"></i>
        </div>
        <span class="layer-name">{{ layer.name }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

interface LayerType {
  name: string;
  value: string;
  icon: string;
  tdtStyle?: string;
}

const emit = defineEmits<{
  layerChange: [layerValue: string]
}>();

const currentLayer = ref('satellite');

const layerTypes: LayerType[] = [
  { name: '矢量地图', value: 'vector', icon: 'el-icon-map-location', tdtStyle: 'vec' },
  { name: '卫星地图', value: 'satellite', icon: 'el-icon-picture', tdtStyle: 'img' },
  { name: '地形地图', value: 'terrain', icon: 'el-icon-s-grid', tdtStyle: 'ter' }
];

const switchLayer = (layerValue: string) => {
  if (currentLayer.value !== layerValue) {
    currentLayer.value = layerValue;
    emit('layerChange', layerValue);
  }
};

// 暴露当前图层值
defineExpose({
  currentLayer
});
</script>

<style lang="scss" scoped>
.base-layer-switcher {
  position: fixed;
  right: 20px;
  bottom: 20px;
  width: 200px;
  background: rgba(20, 30, 40, 0.85);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
  z-index: 9999;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(120, 150, 170, 0.2);
  overflow: hidden;
}

.switcher-header {
  padding: 12px 16px;
  border-bottom: 1px solid rgba(120, 150, 170, 0.15);
  
  .title {
    color: #ffffff;
    font-size: 14px;
    font-weight: 500;
    font-family: 'Microsoft YaHei', Arial, sans-serif;
  }
}

.layer-options {
  padding: 8px;
}

.layer-option {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 4px;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  &:hover {
    background: rgba(255, 255, 255, 0.08);
  }
  
  &.active {
    background: rgba(24, 144, 255, 0.2);
    border: 1px solid rgba(24, 144, 255, 0.4);
    
    .layer-name {
      color: #1890ff;
      font-weight: 500;
    }
    
    .layer-icon i {
      color: #1890ff;
    }
  }
}

.layer-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  
  i {
    font-size: 16px;
    color: #cccccc;
    transition: color 0.2s ease;
  }
}

.layer-name {
  color: #ffffff;
  font-size: 13px;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  transition: color 0.2s ease;
}
</style>