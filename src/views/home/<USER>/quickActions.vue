<template>
  <div class="quick-actions-container">
    <div class="action-button" @click="toggleMenu" :class="{ active: showMenu }">
      <svg class="button-icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M664.081597 1023.943114a78.246037 78.246037 0 0 1-78.985549-76.795456v-284.996471a78.27448 78.27448 0 0 1 78.985549-76.93767h280.843828A78.189152 78.189152 0 0 1 1023.939417 662.151187v284.996471a78.246037 78.246037 0 0 1-79.013992 76.795456z m-585.067605 0a78.246037 78.246037 0 0 1-78.985549-76.795456v-284.996471a78.160709 78.160709 0 0 1 78.985549-76.93767h280.786942a78.302923 78.302923 0 0 1 79.042434 76.93767v284.996471h-0.170656a78.246037 78.246037 0 0 1-78.985549 76.795456z m0-585.096048a78.217594 78.217594 0 0 1-78.985549-76.93767V76.912925a78.189152 78.189152 0 0 1 78.957106-76.795456h280.786942a78.27448 78.27448 0 0 1 79.042435 76.93767v284.996471a78.27448 78.27448 0 0 1-79.013992 76.795456z m589.675333-5.688552a77.193655 77.193655 0 0 1-77.990052-75.885288V75.888985a77.25054 77.25054 0 0 1 77.990052-75.942173h277.26004a77.25054 77.25054 0 0 1 77.961609 75.942173v281.384241a77.421197 77.421197 0 0 1-78.132266 75.885288z"
          fill="currentColor"
        ></path>
      </svg>
    </div>

    <!-- 下拉菜单 -->
    <div v-if="showMenu" class="action-menu">
      <div class="menu-item" @click="handlePatrolAction">
        <svg class="menu-icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
            fill="currentColor"
          />
          <path
            d="M464 336a48 48 0 1 0 96 0 48 48 0 1 0-96 0zm72 112h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V456c0-4.4-3.6-8-8-8z"
            fill="currentColor"
          />
        </svg>
        <span>一键巡查</span>
      </div>
      <div class="menu-item" @click="handleEmergencyAction">
        <svg class="menu-icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M955.7 856l-416-720c-6.2-10.7-16.9-16-27.7-16s-21.6 5.3-27.7 16l-416 720C56 877.4 71.4 904 96 904h832c24.6 0 40-26.6 27.7-48zM480 416c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v184c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V416zm32 352a48.01 48.01 0 0 1 0-96 48.01 48.01 0 0 1 0 96z"
            fill="currentColor"
          />
        </svg>
        <span>应急飞行</span>
      </div>
      <div class="menu-item" @click="handleMapSearchAction">
        <svg class="menu-icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0 0 11.6 0l43.6-43.5a8.2 8.2 0 0 0 0-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116 65.6-158.4C296 211.3 352.2 188 412 188s116 23.3 158.4 65.6C612.7 296 636 352.2 636 412s-23.3 116-65.6 158.4z"
            fill="currentColor"
          />
        </svg>
        <span>地图搜索</span>
      </div>
    </div>

    <!-- 一键巡查对话框 -->
    <el-dialog title="一键巡查" v-model="quickPatrolDialog.visible" width="1200px" append-to-body>
      <div class="quick-patrol-container">
        <!-- 上半部分：三栏布局 -->
        <div class="three-column-layout">
          <!-- 第一栏：任务分类 -->
          <div class="column task-category-column">
            <div class="column-header">任务分类</div>
            <div class="category-list">
              <div
                v-for="category in taskCategories"
                :key="category.value"
                class="category-item"
                :class="{ 'active': quickPatrolForm.inspectionType === category.value }"
                @click="selectTaskCategory(category.value)"
              >
                <i class="category-icon" :class="category.icon"></i>
                <span class="category-label">{{ category.label }}</span>
                <i class="el-icon-arrow-right category-arrow"></i>
              </div>
            </div>
          </div>

          <!-- 第二栏：机巢设备 -->
          <div class="column device-column">
            <div class="column-header">机巢</div>
            <div class="device-list" v-loading="deviceLoading">
              <div
                v-for="device in flightDeviceList"
                :key="device.id"
                class="device-item"
                :class="{
                  'active': selectedDeviceId === device.id,
                  'disabled': !quickPatrolForm.inspectionType
                }"
                @click="selectDevice(device)"
              >
                <div class="device-info">
                  <div class="device-name">{{ device.nickname }}</div>
                  <div class="device-status">
                    <span class="status-dot" :class="getDeviceStatusClass(device)"></span>
                    <span class="status-text">{{ getDeviceStatusText(device) }}</span>
                  </div>
                </div>
                <div class="device-icon">
                  <i class="el-icon-position"></i>
                </div>
              </div>
              <div v-if="flightDeviceList.length === 0 && !deviceLoading" class="empty-state">
                <el-empty description="暂无可用设备" :image-size="60" />
              </div>
            </div>
          </div>

          <!-- 第三栏：任务列表 -->
          <div class="column task-column">
            <div class="column-header">任务</div>
            <div class="task-list" v-loading="routeLoading">
              <div
                v-for="template in getTemplatesForDevice(selectedDeviceId)"
                :key="template.id"
                class="task-item"
                :class="{ 'selected': isTaskSelected(selectedDeviceId, template.id) }"
                @click="toggleTaskSelection(selectedDeviceId, template.id)"
              >
                <div class="task-info">
                  <div class="task-name">{{ template.taskName }}</div>
                  <div class="task-meta">
                    <span class="task-time">{{ formatTaskTime(template) }}</span>
                  </div>
                </div>
                <div class="task-action">
                  <el-checkbox
                    :model-value="isTaskSelected(selectedDeviceId, template.id)"
                    @change="toggleTaskSelection(selectedDeviceId, template.id)"
                  />
                </div>
              </div>
              <div v-if="!selectedDeviceId" class="empty-state">
                <div class="empty-text">请先选择机巢设备</div>
              </div>
              <div v-else-if="getTemplatesForDevice(selectedDeviceId).length === 0 && !routeLoading" class="empty-state">
                <el-empty description="该设备暂无可用任务" :image-size="60" />
              </div>
            </div>
          </div>
        </div>

        <!-- 下半部分：已分配任务 -->
        <div class="assigned-tasks-area">
          <div class="assigned-tasks-header">
            <h4 class="area-title">已分配任务</h4>
            <div class="header-actions">
              <el-button type="danger" size="small" @click="clearSelectedPlans" icon="Delete" :disabled="selectedPlansData.length === 0">
                一键清空
              </el-button>
            </div>
          </div>

          <div class="assigned-tasks-content">
            <div v-if="selectedPlansData.length === 0" class="no-assigned-tasks">
              <el-empty description="暂无已分配任务" :image-size="80" />
            </div>
            <div v-else class="assigned-tasks-grid">
              <div v-for="(task, index) in selectedPlansData" :key="`${task.deviceId}-${task.routeId}`" class="assigned-task-card">
                <div class="task-card-header">
                  <span class="task-number">{{ index + 1 }}号{{ task.inspectionType }}</span>
                  <el-button type="danger" size="small" icon="Close" circle @click="removeSelectedPlan(index)" />
                </div>
                <div class="task-card-content">
                  <div class="task-detail">
                    <span class="detail-label">设备:</span>
                    <span class="detail-value">{{ task.deviceName }}</span>
                  </div>
                  <div class="task-detail">
                    <span class="detail-label">任务:</span>
                    <span class="detail-value">{{ task.routeName }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="quickPatrolDialog.visible = false">取消</el-button>
          <el-button type="primary" :loading="quickPatrolDialog.loading" @click="handleQuickTakeoff" :disabled="selectedPlansData.length === 0">
            一键起飞 ({{ selectedPlansData.length }} 个任务)
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 应急飞行对话框 -->
    <el-dialog title="应急飞行" v-model="emergencyFlightDialog.visible" width="650px" append-to-body>
      <el-form :model="emergencyFlightForm" label-width="150px">
        <el-form-item label="飞行终点" required>
          <el-radio-group v-model="emergencyFlightForm.endpointType">
            <el-radio value="pile">桩号</el-radio>
            <el-radio value="coordinate">坐标</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 桩号输入区域 -->
        <template v-if="emergencyFlightForm.endpointType === 'pile'">
          <el-form-item label="所属项目">
            <el-select v-model="emergencyFlightForm.projectId" placeholder="请选择所属项目" filterable>
              <el-option
                v-for="project in projectList"
                :key="project.id"
                :label="`${project.projectName}(${project.projectDesc || '无描述'})`"
                :value="project.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="桩号">
            <el-input v-model="emergencyFlightForm.pileNo" placeholder="请输入桩号" />
          </el-form-item>
        </template>

        <!-- 坐标输入区域 -->
        <template v-if="emergencyFlightForm.endpointType === 'coordinate'">
          <el-form-item label="经度">
            <el-input v-model="emergencyFlightForm.longitude" placeholder="请输入经度" />
          </el-form-item>
          <el-form-item label="纬度">
            <el-input v-model="emergencyFlightForm.latitude" placeholder="请输入纬度" />
          </el-form-item>
          <el-form-item label="">
            <el-button type="primary" plain icon="Location" @click="handleMapSelection">地图选点</el-button>
          </el-form-item>
        </template>

        <el-form-item label="执行设备" required>
          <el-select v-model="emergencyFlightForm.deviceId" placeholder="请选择执行设备" filterable>
            <el-option v-for="device in flightDeviceList" :key="device.id" :label="device.nickname" :value="device.id" />
          </el-select>
        </el-form-item>

        <!-- 飞行参数展示区域 -->
        <el-divider content-position="left">飞行参数</el-divider>
        <div class="flight-params-display">
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="返航高度">
                <el-input value="120米" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="目标高度">
                <el-input value="120米" readonly />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="安全起飞高度">
                <el-input value="120米" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="最高速度">
                <el-input value="10m/s" readonly />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="指点飞行高度">
                <el-input value="120米" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="指点飞行模式">
                <el-input value="设定高度飞行" readonly />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="遥控器失控动作">
                <el-input value="返航" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="信号丢失时退出航线">
                <el-input value="是" readonly />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="24">
              <el-form-item label="指点飞行失控动作">
                <el-input value="退出指点飞行任务，执行普通失控行为" readonly />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="emergencyFlightDialog.visible = false">取消</el-button>
          <el-button type="primary" :loading="emergencyFlightDialog.loading" @click="handleEmergencyTakeoff">起飞</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 地图搜索对话框 -->
    <el-dialog title="地图搜索" v-model="mapSearchDialog.visible" width="800px" append-to-body>
      <div class="map-search-container">
        <!-- 搜索输入区域 -->
        <div class="search-input-area">
          <el-form :model="mapSearchForm" label-width="80px">
            <el-form-item label="搜索关键词">
              <div class="search-input-group">
                <el-input v-model="mapSearchForm.keyword" placeholder="请输入地点名称、地址或关键词" @keyup.enter="performMapSearch" clearable />
                <el-button type="primary" :loading="mapSearchDialog.loading" @click="performMapSearch" icon="Search"> 搜索 </el-button>
              </div>
            </el-form-item>
          </el-form>
        </div>

        <!-- 搜索结果区域 -->
        <div class="search-results-area" v-if="mapSearchForm.searchResults.length > 0">
          <div class="results-header">
            <h4 class="results-title">搜索结果 ({{ mapSearchForm.searchResults.length }})</h4>
            <el-button type="danger" size="small" @click="clearSearchResults" icon="Delete"> 清空结果 </el-button>
          </div>

          <div class="results-list">
            <div
              v-for="result in mapSearchForm.searchResults"
              :key="result.id"
              class="result-item"
              :class="{ 'selected': mapSearchForm.selectedResult?.id === result.id }"
              @click="selectSearchResult(result)"
            >
              <div class="result-info">
                <div class="result-name">
                  {{ result.name }}
                  <span class="result-type-badge" :class="`type-${result.type}`">{{ getResultTypeText(result.type) }}</span>
                </div>
                <div class="result-address" v-if="result.address">{{ result.address }}</div>
                <div class="result-coordinates" v-if="result.longitude && result.latitude && result.longitude !== 0 && result.latitude !== 0">
                  经度: {{ result.longitude.toFixed(6) }}, 纬度: {{ result.latitude.toFixed(6) }}
                </div>
                <div class="result-extra" v-if="result.phone">
                  电话: {{ result.phone }}
                </div>
              </div>
              <div class="result-actions">
                <el-button 
                  v-if="result.longitude && result.latitude && result.longitude !== 0 && result.latitude !== 0"
                  type="primary" 
                  size="small" 
                  icon="Location" 
                  @click.stop="selectSearchResult(result)"
                > 
                  定位 
                </el-button>
                <el-button 
                  v-else
                  type="success" 
                  size="small" 
                  icon="Search" 
                  @click.stop="searchSuggestResult(result)"
                > 
                  搜索 
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-else-if="!mapSearchDialog.loading && mapSearchForm.keyword" class="empty-results">
          <el-empty description="未找到相关搜索结果" :image-size="80" />
        </div>

        <!-- 搜索提示 -->
        <div v-else-if="!mapSearchForm.keyword" class="search-tips">
          <div class="tips-content">
            <h4>搜索提示</h4>
            <ul>
              <li>支持地点名称搜索，如：南宁市政府、广西大学</li>
              <li>支持地址搜索，如：青秀区嘉宾路1号</li>
              <li>支持关键词搜索，如：医院、学校、银行</li>
              <li>搜索结果将在地图上显示标记点</li>
            </ul>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="mapSearchDialog.visible = false">关闭</el-button>
          <el-button type="primary" :disabled="!mapSearchForm.selectedResult" @click="mapSearchDialog.visible = false"> 确定选择 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, reactive, getCurrentInstance, toRefs } from 'vue';
import type { ComponentInternalInstance } from 'vue';

// 接收地图组件引用
interface Props {
  mapViewRef?: any;
}
const props = defineProps<Props>();

// 定义事件发射
interface Emits {
  (e: 'activate-floating-search'): void;
}
const emit = defineEmits<Emits>();
import { listFlightDevice } from '@/api/biz/flightDevice';
import { listFlightTaskInfoTemplate, executeBatchFromTemplates } from '@/api/biz/flightTaskInfoTemplate';
import { listTenantProjects } from '@/api/biz/project';
import { takeoffToPoint } from '@/api/biz/flightTaskInfo';
import { FlightDeviceVO } from '@/api/biz/flightDevice/types';
import { FlightTaskInfoTemplateVO } from '@/api/biz/flightTaskInfoTemplate/types';
import { ProjectVO } from '@/api/biz/project/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { flight_task_type } = toRefs<any>(proxy?.useDict('flight_task_type'));

const showMenu = ref(false);

// 一键巡查相关数据
const quickPatrolDialog = reactive({
  visible: false,
  loading: false
});

const quickPatrolForm = reactive({
  inspectionType: '',
  devicePlans: {} as Record<string, string | number>, // 设备ID -> 计划ID的映射
  categoryDevicePlans: {} as Record<string, Record<string, string | number>> // 分类 -> (设备ID -> 计划ID)
});

// 基础数据
const flightDeviceList = ref<FlightDeviceVO[]>([]);
const projectList = ref<ProjectVO[]>([]);
const deviceTemplateMap = ref<Record<string, FlightTaskInfoTemplateVO[]>>({});
const selectedDeviceId = ref<string | number>('');
const deviceLoading = ref(false);
const routeLoading = ref(false);

// 应急飞行弹窗数据
const emergencyFlightDialog = reactive({
  visible: false,
  loading: false
});

const emergencyFlightForm = reactive({
  endpointType: 'pile', // 'pile' 或 'coordinate'
  projectId: '', // 所属项目ID
  pileNo: '', // 桩号
  longitude: '', // 经度
  latitude: '', // 纬度
  deviceId: ''
});

// 地图搜索弹窗数据
const mapSearchDialog = reactive({
  visible: false,
  loading: false
});

const mapSearchForm = reactive({
  keyword: '', // 搜索关键词
  searchResults: [], // 搜索结果列表
  selectedResult: null // 选中的搜索结果
});

const selectedPlansData = ref<
  Array<{
    inspectionType: string;
    deviceName: string;
    routeName: string;
    deviceId: string | number;
    routeId: string | number;
  }>
>([]);

// 切换菜单显示/隐藏
const toggleMenu = (event: Event) => {
  event.stopPropagation();
  showMenu.value = !showMenu.value;
};

// 任务分类数据 - 使用字典数据
const taskCategories = computed(() => {
  if (!flight_task_type.value) return [];

  // 为字典项添加图标
  const iconMap: Record<string, string> = {
    'road_inspection': 'el-icon-truck',
    'slope_inspection': 'el-icon-warning',
    'construction_inspection': 'el-icon-setting'
  };

  return flight_task_type.value.map((dict: any) => ({
    value: dict.value,
    label: dict.label,
    icon: iconMap[dict.value] || 'el-icon-setting'
  }));
});

// 一键巡查处理
const handlePatrolAction = () => {
  console.log('执行一键巡查');
  showMenu.value = false;
  handleQuickPatrol();
};

// 应急飞行处理
const handleEmergencyAction = () => {
  console.log('执行应急飞行');
  showMenu.value = false;
  handleEmergencyFlight();
};

// 地图搜索处理 - 修改为使用浮动搜索框
const handleMapSearchAction = () => {
  console.log('激活浮动地图搜索');
  showMenu.value = false;
  
  // 发射事件给父组件，激活浮动搜索框
  emit('activate-floating-search');
};

/** 应急飞行 */
const handleEmergencyFlight = () => {
  getFlightDeviceList();
  getProjectList();
  emergencyFlightForm.endpointType = 'pile';
  emergencyFlightForm.projectId = '';
  emergencyFlightForm.pileNo = '';
  emergencyFlightForm.longitude = '';
  emergencyFlightForm.latitude = '';
  emergencyFlightForm.deviceId = '';
  emergencyFlightDialog.visible = true;
};

/** 地图搜索 */
const handleMapSearch = () => {
  // 重置表单
  mapSearchForm.keyword = '';
  mapSearchForm.searchResults = [];
  mapSearchForm.selectedResult = null;

  // 清除之前的搜索标记
  if (props.mapViewRef) {
    props.mapViewRef.clearSearchResults?.();
  }

  // 显示弹窗
  mapSearchDialog.visible = true;
};

/** 一键巡查 */
const handleQuickPatrol = () => {
  // 重置表单
  quickPatrolForm.inspectionType = '';
  quickPatrolForm.devicePlans = {};
  quickPatrolForm.categoryDevicePlans = {};
  selectedDeviceId.value = '';
  selectedPlansData.value = [];

  // 加载设备列表
  getDeviceList();

  // 显示弹窗
  quickPatrolDialog.visible = true;
};

/** 获取设备列表 */
const getDeviceList = async () => {
  deviceLoading.value = true;
  try {
    const res = await listFlightDevice({ pageNum: 1, pageSize: 1000 });
    flightDeviceList.value = res.rows || [];
  } catch (error) {
    console.error('获取设备列表失败:', error);
    flightDeviceList.value = [];
  } finally {
    deviceLoading.value = false;
  }
};

/** 选择任务分类 */
const selectTaskCategory = (categoryValue: string) => {
  // 保存当前分类的选择到categoryDevicePlans中
  if (quickPatrolForm.inspectionType && Object.keys(quickPatrolForm.devicePlans).length > 0) {
    quickPatrolForm.categoryDevicePlans[quickPatrolForm.inspectionType] = { ...quickPatrolForm.devicePlans };
  }

  // 切换到新分类
  quickPatrolForm.inspectionType = categoryValue;
  selectedDeviceId.value = '';

  // 恢复新分类的选择状态
  quickPatrolForm.devicePlans = quickPatrolForm.categoryDevicePlans[categoryValue] || {};

  // 触发更新已选计划列表
  updateSelectedPlans();
};

/** 选择设备 */
const selectDevice = async (device: FlightDeviceVO) => {
  if (!quickPatrolForm.inspectionType) {
    proxy?.$modal.msgWarning('请先选择任务分类');
    return;
  }

  // 如果已经选择了这个设备，直接返回
  if (selectedDeviceId.value === device.id) {
    return;
  }

  selectedDeviceId.value = device.id;

  // 查询当前分类下该设备的模板
  await getTemplateListForSingleDevice(quickPatrolForm.inspectionType, device.deviceSn, device.id);

  const templates = getTemplatesForDevice(device.id);
  if (templates.length === 0) {
    proxy?.$modal.msgWarning('该设备暂无可用任务模板');
  }
};

/** 查询单个设备的模板 */
const getTemplateListForSingleDevice = async (taskType: string, deviceSn: string, deviceId: string | number) => {
  if (!taskType || !deviceSn) {
    return;
  }

  routeLoading.value = true;
  try {
    const res = await listFlightTaskInfoTemplate({
      taskType,
      deviceSn,
      pageNum: 1,
      pageSize: 1000
    });

    const templates = res.rows || [];

    // 更新设备模板映射
    deviceTemplateMap.value[String(deviceId)] = templates;
  } catch (error) {
    console.error('查询单个设备模板失败:', error);
    deviceTemplateMap.value[String(deviceId)] = [];
  } finally {
    routeLoading.value = false;
  }
};

/** 获取指定设备的模板列表 */
const getTemplatesForDevice = (deviceId: string | number): FlightTaskInfoTemplateVO[] => {
  return deviceTemplateMap.value[String(deviceId)] || [];
};

/** 切换任务选择状态 */
const toggleTaskSelection = async (deviceId: string | number, templateId: string | number) => {
  if (!deviceId || !templateId) return;

  const deviceKey = String(deviceId);
  const templateKey = String(templateId);
  const isSelected = quickPatrolForm.devicePlans[deviceKey] === templateKey;

  if (isSelected) {
    // 取消选择
    delete quickPatrolForm.devicePlans[deviceKey];
  } else {
    // 选择任务（每个设备只能选择一个任务）
    quickPatrolForm.devicePlans[deviceKey] = templateKey;
  }

  // 同步更新到categoryDevicePlans
  if (quickPatrolForm.inspectionType) {
    quickPatrolForm.categoryDevicePlans[quickPatrolForm.inspectionType] = { ...quickPatrolForm.devicePlans };
  }

  updateSelectedPlans();
};

/** 检查任务是否被选中 */
const isTaskSelected = (deviceId: string | number, templateId: string | number): boolean => {
  return quickPatrolForm.devicePlans[String(deviceId)] === String(templateId);
};

/** 获取设备状态样式类 */
const getDeviceStatusClass = (device: FlightDeviceVO): string => {
  // 根据设备状态返回对应的样式类
  return 'online'; // 默认在线状态
};

/** 获取设备状态文本 */
const getDeviceStatusText = (device: FlightDeviceVO): string => {
  // 根据设备状态返回对应的文本
  return '待机'; // 默认待机状态
};

/** 格式化任务时间 */
const formatTaskTime = (template: FlightTaskInfoTemplateVO): string => {
  // 根据模板信息格式化显示时间
  return '120min01s'; // 默认时间格式
};

/** 更新已选计划列表 */
const updateSelectedPlans = () => {
  // 重新生成完整的已选计划列表，基于所有分类的选择
  const allPlans: Array<{
    inspectionType: string;
    deviceName: string;
    routeName: string;
    deviceId: string;
    routeId: string;
  }> = [];

  // 遍历所有分类的设备计划
  Object.entries(quickPatrolForm.categoryDevicePlans).forEach(([categoryType, devicePlans]) => {
    Object.entries(devicePlans).forEach(([deviceId, templateId]) => {
      const device = flightDeviceList.value.find((d) => d.id === deviceId);
      // 从设备对应的模板列表中查找
      const deviceTemplates = deviceTemplateMap.value[deviceId] || [];
      const template = deviceTemplates.find((t) => t.id === templateId);

      if (device && template) {
        allPlans.push({
          inspectionType: getInspectionTypeLabel(categoryType),
          deviceName: device.nickname,
          routeName: template.taskName,
          deviceId: device.id,
          routeId: template.id
        });
      }
    });
  });

  // 添加当前分类的选择（如果还没有保存到categoryDevicePlans中）
  if (quickPatrolForm.inspectionType && Object.keys(quickPatrolForm.devicePlans).length > 0) {
    Object.entries(quickPatrolForm.devicePlans).forEach(([deviceId, templateId]) => {
      const device = flightDeviceList.value.find((d) => d.id === deviceId);
      const deviceTemplates = deviceTemplateMap.value[deviceId] || [];
      const template = deviceTemplates.find((t) => t.id === templateId);

      if (device && template) {
        // 检查是否已经存在相同的计划
        const exists = allPlans.some(
          (plan) =>
            plan.deviceId === deviceId &&
            plan.routeId === templateId &&
            plan.inspectionType === getInspectionTypeLabel(quickPatrolForm.inspectionType)
        );

        if (!exists) {
          allPlans.push({
            inspectionType: getInspectionTypeLabel(quickPatrolForm.inspectionType),
            deviceName: device.nickname,
            routeName: template.taskName,
            deviceId: device.id,
            routeId: template.id
          });
        }
      }
    });
  }

  selectedPlansData.value = allPlans;
};

/** 获取巡检类型显示文本 */
const getInspectionTypeLabel = (type: string) => {
  if (!flight_task_type.value) return type;

  const dictItem = flight_task_type.value.find((item: any) => item.value === type);
  return dictItem ? dictItem.label : type;
};

/** 移除已选计划 */
const removeSelectedPlan = (index: number) => {
  const plan = selectedPlansData.value[index];
  if (plan) {
    // 需要找到该计划对应的分类类型
    let targetCategoryType = '';

    // 遍历所有分类，找到包含该设备和任务的分类
    for (const [categoryType, devicePlans] of Object.entries(quickPatrolForm.categoryDevicePlans)) {
      if (devicePlans[String(plan.deviceId)] === String(plan.routeId)) {
        targetCategoryType = categoryType;
        break;
      }
    }

    // 如果找到了对应的分类，从该分类中移除
    if (targetCategoryType) {
      delete quickPatrolForm.categoryDevicePlans[targetCategoryType][String(plan.deviceId)];
    }

    // 如果是当前分类的计划，也要从当前分类中移除
    if (quickPatrolForm.devicePlans[String(plan.deviceId)] === String(plan.routeId)) {
      delete quickPatrolForm.devicePlans[String(plan.deviceId)];
    }

    // 手动更新已选计划列表
    updateSelectedPlans();
  }
};

/** 清空已选计划 */
const clearSelectedPlans = () => {
  // 清空当前分类的设备计划
  quickPatrolForm.devicePlans = {};

  // 清空所有分类的设备计划映射
  quickPatrolForm.categoryDevicePlans = {};

  // 清空已选计划列表
  selectedPlansData.value = [];

  // 重置当前选中的设备
  selectedDeviceId.value = '';
};

/** 一键起飞 */
const handleQuickTakeoff = async () => {
  if (selectedPlansData.value.length === 0) {
    proxy?.$modal.msgWarning('请至少选择一个计划');
    return;
  }

  // 提取模板ID列表
  const templateIds = selectedPlansData.value.map((plan) => plan.routeId);

  // 显示确认信息
  const planDetails = selectedPlansData.value
    .map((plan, index) => `${index + 1}. ${plan.inspectionType} - ${plan.deviceName} - ${plan.routeName}`)
    .join('\n');

  try {
    await proxy?.$modal.confirm(`即将提交 ${selectedPlansData.value.length} 个任务模板：\n\n${planDetails}`);

    // 调用批量执行接口
    quickPatrolDialog.loading = true;

    const res = await executeBatchFromTemplates(templateIds);

    if (res.code === 200) {
      proxy?.$modal.msgSuccess(`成功创建 ${selectedPlansData.value.length} 个巡查任务并开始执行！`);
      quickPatrolDialog.visible = false;

      // 清空选择状态
      quickPatrolForm.inspectionType = '';
      quickPatrolForm.devicePlans = {};
      quickPatrolForm.categoryDevicePlans = {};
      selectedDeviceId.value = '';
      selectedPlansData.value = [];
    } else {
      proxy?.$modal.msgError(res.msg || '创建任务失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量执行任务失败:', error);
      proxy?.$modal.msgError('创建任务失败，请稍后重试');
    }
  } finally {
    quickPatrolDialog.loading = false;
  }
};

// 点击外部关闭菜单
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement;
  if (!target.closest('.quick-actions-container')) {
    showMenu.value = false;
  }
};

/** 获取机巢设备列表 */
const getFlightDeviceList = async () => {
  try {
    const res = await listFlightDevice({ pageNum: 1, pageSize: 1000 });
    flightDeviceList.value = res.rows || [];
  } catch (error) {
    console.error('获取机巢设备列表失败:', error);
    flightDeviceList.value = [];
  }
};

/** 获取项目列表 */
const getProjectList = async () => {
  try {
    const res = await listTenantProjects();
    projectList.value = res.data || [];
  } catch (error) {
    console.error('获取项目列表失败:', error);
    projectList.value = [];
  }
};

/** 地图选点功能 */
const handleMapSelection = () => {
  if (!props.mapViewRef) {
    proxy?.$modal.msgError('地图组件未加载，请稍后重试');
    return;
  }

  // 临时隐藏弹窗，让用户可以点击地图
  emergencyFlightDialog.visible = false;

  // 调用地图组件的选点方法
  props.mapViewRef.startLocationPicking(
    // 选点成功回调
    (coordinates: { lng: number; lat: number; height: number }) => {
      // 选点成功后的回调，自动填充坐标到表单
      emergencyFlightForm.longitude = coordinates.lng.toString();
      emergencyFlightForm.latitude = coordinates.lat.toString();

      // 重新显示弹窗
      emergencyFlightDialog.visible = true;

      proxy?.$modal.msgSuccess(`已选择坐标：${coordinates.lng.toFixed(6)}, ${coordinates.lat.toFixed(6)}`);
    },
    // 取消选点回调
    () => {
      // 用户按ESC键取消选点，重新显示弹窗
      emergencyFlightDialog.visible = true;
      proxy?.$modal.msgWarning('已取消地图选点');
    }
  );

  // 提示用户在地图上选择坐标
  proxy?.$modal.msgSuccess('弹窗已隐藏，请在地图上点击选择目标坐标（按ESC键取消）');
};

/** 执行地图搜索 */
const performMapSearch = async () => {
  if (!mapSearchForm.keyword.trim()) {
    proxy?.$modal.msgWarning('请输入搜索关键词');
    return;
  }

  if (!props.mapViewRef) {
    proxy?.$modal.msgError('地图组件未加载，请稍后重试');
    return;
  }

  mapSearchDialog.loading = true;
  try {
    console.log('搜索关键词:', mapSearchForm.keyword);
    
    // 调用地图组件的天地图搜索功能
    const searchResults = await props.mapViewRef.performTiandituSearch(mapSearchForm.keyword);
    
    // 更新搜索结果
    mapSearchForm.searchResults = searchResults;
    
    if (searchResults.length > 0) {
      proxy?.$modal.msgSuccess(`找到 ${searchResults.length} 个搜索结果`);
    } else {
      proxy?.$modal.msgWarning('未找到相关搜索结果，请尝试其他关键词');
    }
  } catch (error) {
    console.error('地图搜索失败:', error);
    proxy?.$modal.msgError('搜索失败，请稍后重试');
    mapSearchForm.searchResults = [];
  } finally {
    mapSearchDialog.loading = false;
  }
};

/** 选择搜索结果 */
const selectSearchResult = (result: any) => {
  mapSearchForm.selectedResult = result;
  
  console.log('选中搜索结果:', result);
  
  // 在地图上定位到选中的位置
  if (props.mapViewRef && result.longitude && result.latitude && result.longitude !== 0 && result.latitude !== 0) {
    try {
      // 调用地图组件的定位方法
      const position = { lng: result.longitude, lat: result.latitude, height: 1000 };
      props.mapViewRef.flyToPosition?.(position);
      
      proxy?.$modal.msgSuccess(`已定位到：${result.name}`);
    } catch (error) {
      console.error('地图定位失败:', error);
      proxy?.$modal.msgWarning('定位失败，请稍后重试');
    }
  } else if (result.type === 'suggest' || result.type === 'city') {
    // 对于建议词或城市统计结果，提示用户重新搜索
            proxy?.$modal.msgWarning(`"${result.name}" 是建议词，请点击重新搜索获取具体位置`);
  }
};

/** 清空搜索结果 */
const clearSearchResults = () => {
  mapSearchForm.searchResults = [];
  mapSearchForm.selectedResult = null;
  
  // 清除地图上的搜索标记
  if (props.mapViewRef) {
    props.mapViewRef.clearSearchResults?.();
  }
};

/** 获取结果类型文本 */
const getResultTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    'poi': 'POI点',
    'city': '城市统计',
    'area': '行政区',
    'suggest': '建议词',
    'line': '公交线路'
  };
  return typeMap[type] || '未知类型';
};

/** 搜索建议词结果 */
const searchSuggestResult = async (result: any) => {
  // 将建议词作为新的搜索关键词
  mapSearchForm.keyword = result.name;
  
  // 执行搜索
  await performMapSearch();
};

/** 应急飞行起飞 */
const handleEmergencyTakeoff = async () => {
  // 验证表单
  let isValid = false;

  if (emergencyFlightForm.endpointType === 'pile') {
    // 桩号模式：需要所属项目和桩号
    isValid = !!(emergencyFlightForm.projectId && emergencyFlightForm.pileNo);
    if (!isValid) {
      proxy?.$modal.msgWarning('请选择所属项目和填写桩号');
      return;
    }
  } else if (emergencyFlightForm.endpointType === 'coordinate') {
    // 坐标模式：需要经度和纬度
    isValid = !!(emergencyFlightForm.longitude && emergencyFlightForm.latitude);
    if (!isValid) {
      proxy?.$modal.msgWarning('请填写经度和纬度');
      return;
    }
  }

  if (!emergencyFlightForm.deviceId) {
    proxy?.$modal.msgWarning('请选择执行设备');
    return;
  }

  try {
    emergencyFlightDialog.loading = true;

    // 获取选中设备的序列号
    const selectedDevice = flightDeviceList.value.find((device) => device.id === emergencyFlightForm.deviceId);
    if (!selectedDevice) {
      proxy?.$modal.msgError('未找到选中的设备信息');
      return;
    }

    // 构建请求参数
    const params: {
      targetType: number;
      projectCode?: string;
      pileNo?: string;
      targetLongitude?: number;
      targetLatitude?: number;
    } = {
      targetType: emergencyFlightForm.endpointType === 'pile' ? 0 : 1
    };

    if (emergencyFlightForm.endpointType === 'pile') {
      // 桩号模式
      const selectedProject = projectList.value.find((project) => project.id === emergencyFlightForm.projectId);
      params.projectCode = selectedProject?.projectCode || selectedProject?.projectName;
      params.pileNo = emergencyFlightForm.pileNo;
    } else {
      // 坐标模式
      params.targetLongitude = parseFloat(emergencyFlightForm.longitude);
      params.targetLatitude = parseFloat(emergencyFlightForm.latitude);

      // 验证坐标格式
      if (isNaN(params.targetLongitude) || isNaN(params.targetLatitude)) {
        proxy?.$modal.msgWarning('请输入有效的经纬度坐标');
        return;
      }

      // 验证坐标范围
      if (params.targetLongitude < -180 || params.targetLongitude > 180) {
        proxy?.$modal.msgWarning('经度范围应在-180到180之间');
        return;
      }
      if (params.targetLatitude < -90 || params.targetLatitude > 90) {
        proxy?.$modal.msgWarning('纬度范围应在-90到90之间');
        return;
      }
    }

    // 显示确认对话框
    const confirmMessage =
      emergencyFlightForm.endpointType === 'pile'
        ? `确认执行应急飞行？\n设备：${selectedDevice.nickname}\n目标：${params.projectCode} - ${params.pileNo}`
        : `确认执行应急飞行？\n设备：${selectedDevice.nickname}\n目标坐标：${params.targetLongitude}, ${params.targetLatitude}`;

    await proxy?.$modal.confirm(confirmMessage);

    // 调用应急飞行接口
    const res = await takeoffToPoint(selectedDevice.deviceSn, params);

    if (res.code === 200) {
      proxy?.$modal.msgSuccess('应急飞行指令已发送，设备正在执行');
      emergencyFlightDialog.visible = false;

      // 重置表单
      emergencyFlightForm.endpointType = 'pile';
      emergencyFlightForm.projectId = '';
      emergencyFlightForm.pileNo = '';
      emergencyFlightForm.longitude = '';
      emergencyFlightForm.latitude = '';
      emergencyFlightForm.deviceId = '';
    } else {
      proxy?.$modal.msgError(res.msg || '应急飞行指令发送失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('应急飞行失败:', error);
      proxy?.$modal.msgError('应急飞行指令发送失败，请稍后重试');
    }
  } finally {
    emergencyFlightDialog.loading = false;
  }
};

onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<style scoped>
.quick-actions-container {
  position: relative;
  z-index: 200;
}

.action-button {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, rgba(25, 252, 222, 0.2), rgba(10, 240, 255, 0.2));
  border: 2px solid rgba(25, 252, 222, 0.4);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(8px);
  box-shadow: 0 4px 16px rgba(25, 252, 222, 0.2);
}

.action-button:hover {
  border-color: rgba(25, 252, 222, 0.8);
  box-shadow: 0 6px 24px rgba(25, 252, 222, 0.4);
  transform: translateY(-2px);
}

.action-button.active {
  background: linear-gradient(135deg, rgba(25, 252, 222, 0.4), rgba(10, 240, 255, 0.4));
  border-color: rgba(25, 252, 222, 0.8);
  transform: rotate(45deg);
}

.button-icon {
  width: 20px;
  height: 20px;
  color: #19fcde;
  transition: transform 0.3s ease;
}

.action-button.active .button-icon {
  transform: rotate(-45deg);
}

.action-menu {
  position: absolute;
  top: 50%;
  left: calc(100% + 12px);
  transform: translateY(-50%);
  display: flex;
  flex-direction: row;
  background: rgba(20, 30, 40, 0.95);
  border: 1px solid rgba(25, 252, 222, 0.3);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(16px);
  overflow: hidden;
  animation: menuFadeIn 0.3s ease-out;
}

@keyframes menuFadeIn {
  from {
    opacity: 0;
    transform: translateY(-50%) translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(-50%) translateX(0);
  }
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  color: #fff;
  font-size: 14px;
  font-family: 'Source Han Sans CN', Arial, sans-serif;
  cursor: pointer;
  transition: all 0.2s ease;
  border-right: 1px solid rgba(255, 255, 255, 0.05);
  white-space: nowrap;
}

.menu-item:last-child {
  border-right: none;
}

.menu-item:hover {
  background: rgba(25, 252, 222, 0.15);
  color: #19fcde;
  transform: translateX(4px);
}

.menu-icon {
  width: 16px;
  height: 16px;
  margin-right: 12px;
  color: #fff;
  transition: color 0.2s ease;
}

.menu-item:hover .menu-icon {
  color: #19fcde;
}

/* 添加一个小三角箭头指向按钮 */
.action-menu::before {
  content: '';
  position: absolute;
  top: 50%;
  left: -6px;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
  border-right: 6px solid rgba(25, 252, 222, 0.3);
}

/* 一键巡查弹窗样式 */
.quick-patrol-container {
  padding: 0;
}

/* 三栏布局样式 */
.three-column-layout {
  display: flex;
  height: 400px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
}

.column {
  display: flex;
  flex-direction: column;
  border-right: 1px solid #e4e7ed;
}

.column:last-child {
  border-right: none;
}

.task-category-column {
  width: 200px;
  background-color: #f8f9fa;
}

.device-column {
  width: 300px;
  background-color: #fff;
}

.task-column {
  flex: 1;
  background-color: #fff;
}

.column-header {
  padding: 15px 20px;
  background-color: #409eff;
  color: white;
  font-weight: 600;
  font-size: 16px;
  text-align: center;
  border-bottom: 1px solid #e4e7ed;
}

/* 任务分类列表样式 */
.category-list {
  flex: 1;
  overflow-y: auto;
}

.category-item {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 1px solid #f0f0f0;
}

.category-item:hover {
  background-color: #e8f4ff;
}

.category-item.active {
  background-color: #409eff;
  color: white;
}

.category-icon {
  margin-right: 10px;
  font-size: 18px;
}

.category-label {
  flex: 1;
  font-size: 14px;
}

.category-arrow {
  font-size: 12px;
  opacity: 0.6;
}

/* 设备列表样式 */
.device-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.device-item {
  display: flex;
  align-items: center;
  padding: 15px;
  margin-bottom: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #fff;
}

.device-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.device-item.active {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.device-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #f5f5f5;
}

.device-info {
  flex: 1;
}

.device-name {
  font-weight: 600;
  font-size: 14px;
  color: #333;
  margin-bottom: 5px;
}

.device-status {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #666;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 5px;
}

.status-dot.online {
  background-color: #67c23a;
}

.status-dot.offline {
  background-color: #f56c6c;
}

.device-icon {
  font-size: 20px;
  color: #409eff;
}

/* 任务列表样式 */
.task-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.task-item {
  display: flex;
  align-items: center;
  padding: 15px;
  margin-bottom: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #fff;
}

.task-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.task-item.selected {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.task-info {
  flex: 1;
}

.task-name {
  font-weight: 600;
  font-size: 14px;
  color: #333;
  margin-bottom: 5px;
}

.task-meta {
  font-size: 12px;
  color: #666;
}

.task-time {
  color: #67c23a;
}

.task-action {
  margin-left: 10px;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #999;
}

.empty-text {
  font-size: 14px;
  color: #999;
}

/* 已分配任务区域样式 */
.assigned-tasks-area {
  border-top: 1px solid #e4e7ed;
  padding-top: 20px;
}

.assigned-tasks-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.area-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.assigned-tasks-content {
  min-height: 150px;
}

.no-assigned-tasks {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 150px;
}

.assigned-tasks-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 15px;
}

.assigned-task-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 15px;
  background-color: #fff;
  transition: all 0.3s ease;
}

.assigned-task-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.task-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.task-number {
  font-weight: 600;
  color: #409eff;
  font-size: 14px;
}

.task-card-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.task-detail {
  display: flex;
  align-items: center;
  font-size: 13px;
}

.detail-label {
  color: #666;
  margin-right: 8px;
  min-width: 40px;
}

.detail-value {
  color: #333;
  font-weight: 500;
}

.dialog-footer {
  text-align: right;
}

/* 飞行参数展示区域样式 */
.flight-params-display {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin-top: 10px;
}

.flight-params-display .el-form-item {
  margin-bottom: 12px;
}

.flight-params-display .el-input__inner {
  background-color: #fff;
  border: 1px solid #e4e7ed;
  color: #606266;
  font-weight: 500;
}

.flight-params-display .el-form-item__label {
  color: #606266;
  font-weight: 600;
  width: 140px !important;
  min-width: 140px;
}

/* 地图搜索弹窗样式 */
.map-search-container {
  padding: 0;
}

.search-input-area {
  margin-bottom: 20px;
}

.search-input-group {
  display: flex;
  gap: 10px;
  align-items: center;
}

.search-input-group .el-input {
  flex: 1;
}

.search-results-area {
  border-top: 1px solid #e4e7ed;
  padding-top: 20px;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.results-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin: 0;
}

.results-list {
  max-height: 400px;
  overflow-y: auto;
}

.result-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  margin-bottom: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #fff;
}

.result-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.result-item.selected {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.result-info {
  flex: 1;
}

.result-name {
  font-weight: 600;
  font-size: 16px;
  color: #333;
  margin-bottom: 5px;
}

.result-address {
  font-size: 14px;
  color: #666;
  margin-bottom: 3px;
}

.result-coordinates {
  font-size: 12px;
  color: #999;
  font-family: 'Courier New', monospace;
}

.result-actions {
  margin-left: 15px;
}

.empty-results {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.search-tips {
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin-top: 10px;
}

.tips-content h4 {
  color: #409eff;
  margin-bottom: 15px;
  font-size: 16px;
}

.tips-content ul {
  margin: 0;
  padding-left: 20px;
  color: #666;
}

.tips-content li {
  margin-bottom: 8px;
  line-height: 1.5;
}

/* 搜索结果类型标签样式 */
.result-type-badge {
  display: inline-block;
  padding: 2px 6px;
  margin-left: 8px;
  font-size: 10px;
  border-radius: 3px;
  color: white;
  font-weight: normal;
}

.result-type-badge.type-poi {
  background-color: #409eff;
}

.result-type-badge.type-city {
  background-color: #e6a23c;
}

.result-type-badge.type-area {
  background-color: #67c23a;
}

.result-type-badge.type-suggest {
  background-color: #909399;
}

.result-type-badge.type-line {
  background-color: #f56c6c;
}

.result-extra {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}
</style>
