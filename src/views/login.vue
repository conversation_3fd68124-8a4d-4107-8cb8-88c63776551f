<template>
  <div id="login-container" class="login">
    <el-form ref="loginRef" :model="loginForm" :rules="loginRules" class="login-form">
      <div class="tech-corner top-right"></div>
      <div class="tech-corner bottom-left"></div>
      <div class="title-container">
        <div class="title-box">
          <h3 class="title">{{ title }}</h3>
          <lang-select />
        </div>
      </div>
      <el-form-item v-if="tenantEnabled" prop="tenantId">
        <el-select v-model="loginForm.tenantId" filterable :placeholder="proxy.$t('login.selectPlaceholder')" style="width: 100%">
          <el-option v-for="item in tenantList" :key="item.tenantId" :label="item.companyName" :value="item.tenantId"></el-option>
          <template #prefix><svg-icon icon-class="company" class="el-input__icon input-icon" /></template>
        </el-select>
      </el-form-item>
      <el-form-item prop="username">
        <el-input v-model="loginForm.username" type="text" size="large" auto-complete="off" :placeholder="proxy.$t('login.username')">
          <template #prefix><svg-icon icon-class="user" class="el-input__icon input-icon" /></template>
        </el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input
          v-model="loginForm.password"
          type="password"
          size="large"
          auto-complete="off"
          :placeholder="proxy.$t('login.password')"
          @keyup.enter="handleLogin"
        >
          <template #prefix><svg-icon icon-class="password" class="el-input__icon input-icon" /></template>
        </el-input>
      </el-form-item>
      <el-form-item v-if="captchaEnabled" prop="code">
        <el-input
          v-model="loginForm.code"
          size="large"
          auto-complete="off"
          :placeholder="proxy.$t('login.code')"
          style="width: 63%"
          @keyup.enter="handleLogin"
        >
          <template #prefix><svg-icon icon-class="validCode" class="el-input__icon input-icon" /></template>
        </el-input>
        <div class="login-code">
          <img :src="codeUrl" class="login-code-img" @click="getCode" />
        </div>
      </el-form-item>
      <el-checkbox v-model="loginForm.rememberMe" style="margin: 0 0 25px 0; color: #e6f1ff">{{ proxy.$t('login.rememberPassword') }}</el-checkbox>
      <!-- <el-form-item style="float: right">
        <el-button circle :title="proxy.$t('login.social.wechat')" @click="doSocialLogin('wechat')">
          <svg-icon icon-class="wechat" />
        </el-button>
        <el-button circle :title="proxy.$t('login.social.maxkey')" @click="doSocialLogin('maxkey')">
          <svg-icon icon-class="maxkey" />
        </el-button>
        <el-button circle :title="proxy.$t('login.social.topiam')" @click="doSocialLogin('topiam')">
          <svg-icon icon-class="topiam" />
        </el-button>
        <el-button circle :title="proxy.$t('login.social.gitee')" @click="doSocialLogin('gitee')">
          <svg-icon icon-class="gitee" />
        </el-button>
        <el-button circle :title="proxy.$t('login.social.github')" @click="doSocialLogin('github')">
          <svg-icon icon-class="github" />
        </el-button>
      </el-form-item> -->
      <el-form-item style="width: 100%; text-align: center">
        <el-button
          :loading="loading"
          size="large"
          type="primary"
          class="login-button"
          style="width: 100%; margin-bottom: 10px"
          @click.prevent="handleLogin"
        >
          <span v-if="!loading" style="font-weight: 500; letter-spacing: 1px">{{ proxy.$t('login.login') }}</span>
          <span v-else style="font-weight: 500; letter-spacing: 1px">{{ proxy.$t('login.logging') }}</span>
        </el-button>
        <div v-if="register" style="text-align: right">
          <router-link class="link-type" :to="'/register'" style="color: #4a8cff; text-decoration: none">{{
            proxy.$t('login.switchRegisterPage')
          }}</router-link>
        </div>
      </el-form-item>
    </el-form>
    <!--  底部  -->
    <div class="el-login-footer">
      <span>Copyright © 2025 广西北投软件股份有限公司 All Rights Reserved.</span>
    </div>
  </div>
</template>

<script setup lang="ts">
// 暂时移除粒子动画导入
import { getCodeImg, getTenantList } from '@/api/login';
import { authBinding } from '@/api/system/social/auth';
import { getRedirectUrl } from '@/api/sso';
import { useUserStore } from '@/store/modules/user';
import { LoginData, TenantVO } from '@/api/types';
import { to } from 'await-to-js';
import { HttpStatus } from '@/enums/RespEnum';
import { useI18n } from 'vue-i18n';
import { setToken } from '@/utils/auth';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const title = import.meta.env.VITE_APP_TITLE;
const userStore = useUserStore();
const router = useRouter();
const { t } = useI18n();

const loginForm = ref<LoginData>({
  tenantId: '000000',
  username: '',
  password: '',
  rememberMe: false,
  code: '',
  uuid: ''
} as LoginData);

const loginRules: ElFormRules = {
  tenantId: [{ required: true, trigger: 'blur', message: t('login.rule.tenantId.required') }],
  username: [{ required: true, trigger: 'blur', message: t('login.rule.username.required') }],
  password: [{ required: true, trigger: 'blur', message: t('login.rule.password.required') }],
  code: [{ required: true, trigger: 'change', message: t('login.rule.code.required') }]
};

const codeUrl = ref('');
const loading = ref(false);
// 验证码开关
const captchaEnabled = ref(true);
// 租户开关
const tenantEnabled = ref(true);

// 注册开关
const register = ref(false);
const redirect = ref('/');
const loginRef = ref<ElFormInstance>();
// 租户列表
const tenantList = ref<TenantVO[]>([]);

watch(
  () => router.currentRoute.value,
  (newRoute: any) => {
    redirect.value = newRoute.query && newRoute.query.redirect && decodeURIComponent(newRoute.query.redirect);
  },
  { immediate: true }
);

const handleLogin = () => {
  loginRef.value?.validate(async (valid: boolean, fields: any) => {
    if (valid) {
      loading.value = true;
      // 勾选了需要记住密码设置在 localStorage 中设置记住用户名和密码
      if (loginForm.value.rememberMe) {
        localStorage.setItem('tenantId', String(loginForm.value.tenantId));
        localStorage.setItem('username', String(loginForm.value.username));
        localStorage.setItem('password', String(loginForm.value.password));
        localStorage.setItem('rememberMe', String(loginForm.value.rememberMe));
      } else {
        // 否则移除
        localStorage.removeItem('tenantId');
        localStorage.removeItem('username');
        localStorage.removeItem('password');
        localStorage.removeItem('rememberMe');
      }

      // 检查是否有单点登录参数
      const client = getParam('client', '');
      const redirectParam = getParam('redirect', '');
      const hasSsoParams = client && redirectParam && client !== '' && redirectParam !== '';

      // 执行普通登录
      const [err] = await to(userStore.login(loginForm.value));
      if (!err) {
        if (hasSsoParams) {
          // 如果有单点登录参数，登录成功后刷新页面
          // 刷新后会触发checkSsoStatus方法，进行SSO重定向
          ElMessage.success(proxy.$t('login.loginSuccess'));
          setTimeout(() => {
            window.location.reload();
          }, 800);
        } else {
          // 普通登录成功，按原有逻辑跳转
          const redirectUrl = redirect.value || '/';
          await router.push(redirectUrl);
        }
        loading.value = false;
      } else {
        loading.value = false;
        // 重新获取验证码
        if (captchaEnabled.value) {
          await getCode();
        }
      }
    } else {
      console.log('error submit!', fields);
    }
  });
};

/**
 * 获取验证码
 */
const getCode = async () => {
  const res = await getCodeImg();
  const { data } = res;
  captchaEnabled.value = data.captchaEnabled === undefined ? true : data.captchaEnabled;
  if (captchaEnabled.value) {
    codeUrl.value = 'data:image/gif;base64,' + data.img;
    loginForm.value.uuid = data.uuid;
  }
};

const getLoginData = () => {
  const tenantId = localStorage.getItem('tenantId');
  const username = localStorage.getItem('username');
  const password = localStorage.getItem('password');
  const rememberMe = localStorage.getItem('rememberMe');
  loginForm.value = {
    tenantId: tenantId === null ? String(loginForm.value.tenantId) : tenantId,
    username: username === null ? String(loginForm.value.username) : username,
    password: password === null ? String(loginForm.value.password) : String(password),
    rememberMe: rememberMe === null ? false : Boolean(rememberMe)
  } as LoginData;
};

/**
 * 获取租户列表
 */
const initTenantList = async () => {
  const { data } = await getTenantList(false);
  tenantEnabled.value = data.tenantEnabled === undefined ? true : data.tenantEnabled;
  if (tenantEnabled.value) {
    tenantList.value = data.voList;
    if (tenantList.value != null && tenantList.value.length !== 0) {
      loginForm.value.tenantId = tenantList.value[0].tenantId;
    }
  }
};

/**
 * 第三方登录
 * @param type
 */
const doSocialLogin = (type: string) => {
  authBinding(type, loginForm.value.tenantId).then((res: any) => {
    if (res.code === HttpStatus.SUCCESS) {
      // 获取授权地址跳转
      window.location.href = res.data;
    } else {
      ElMessage.error(res.msg);
    }
  });
};

/**
 * 检查单点登录状态
 */
const checkSsoStatus = async () => {
  loading.value = true;
  try {
    // 获取URL参数
    const client = getParam('client', '');
    const redirectUrl = getParam('redirect', '');
    const mode = getParam('mode', '');
    // 如果没有单点登录相关参数，则不进行单点登录检查
    if (!client || !redirectUrl || client === '' || redirectUrl === '') {
      loading.value = false;
      return;
    }

    const params = {
      client: client,
      redirect: redirectUrl,
      mode: mode
    };

    const res = await getRedirectUrl(params);
    if (res.code === HttpStatus.SUCCESS) {
      // 已登录，并且redirect地址有效，开始跳转
      window.location.href = res.data;
    } else if (res.code === HttpStatus.UNAUTHORIZED) {
      // 未登录，等待用户输入账号密码
      ElMessage.info(proxy.$t('login.ssoNotLoggedIn'));
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error('检查单点登录状态失败:', error);
    ElMessage.error(proxy.$t('login.ssoCheckFailed'));
  } finally {
    loading.value = false;
  }
};

/**
 * 从URL中获取参数
 */
const getParam = (name: string, defaultValue?: string) => {
  const query = window.location.search.substring(1);
  const vars = query.split('&');
  for (let i = 0; i < vars.length; i++) {
    const pair = vars[i].split('=');
    if (pair[0] === name) {
      return pair[1] + (pair[2] ? '=' + pair[2] : '');
    }
  }
  return defaultValue === undefined ? null : defaultValue;
};

onMounted(() => {
  // 检查单点登录状态
  checkSsoStatus();
  getCode();
  initTenantList();
  getLoginData();

  // 暂时移除粒子动画初始化
});
</script>

<style lang="scss" scoped>
/* 标题容器 */
.title-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

/* 科技感装饰角落 */
.tech-corner {
  position: absolute;
  width: 50px;
  height: 50px;

  &.top-right {
    top: 0;
    right: 0;
    border-top: 2px solid #4a8cff;
    border-right: 2px solid #4a8cff;
    border-top-right-radius: 10px;
    animation: borderPulse 3s infinite ease-in-out;
  }

  &.bottom-left {
    bottom: 0;
    left: 0;
    border-bottom: 2px solid #4a8cff;
    border-left: 2px solid #4a8cff;
    border-bottom-left-radius: 10px;
    animation: borderPulse 3s infinite ease-in-out 1.5s;
  }
}
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-image: url('../assets/images/login-background-tech.svg');
  background-size: cover;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(10, 45, 90, 0.3) 0%, rgba(6, 26, 64, 0.6) 100%);
    z-index: 0;
  }
}

.title-box {
  display: flex;

  .title {
    margin: 20px auto 30px auto;
    text-align: center;
    color: #e6f1ff;
    font-weight: 600;
    font-size: 28px;
    letter-spacing: 2px;
    text-shadow:
      0 0 15px rgba(74, 140, 255, 0.8),
      0 0 25px rgba(74, 140, 255, 0.4);
    background: linear-gradient(135deg, #e6f1ff 0%, #4a8cff 50%, #e6f1ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  :deep(.lang-select--style) {
    line-height: 0;
    color: #4a8cff;
  }
}

.login-form {
  border-radius: 10px;
  background: url('../assets/images/login-form-bg.svg') no-repeat center center;
  background-size: 100% 100%;
  width: 400px;
  padding: 25px 25px 5px 25px;
  z-index: 1;
  box-shadow:
    0 0 20px rgba(0, 0, 0, 0.5),
    0 0 30px rgba(74, 140, 255, 0.3);
  backdrop-filter: blur(5px);
  position: relative;
  border: 1px solid rgba(74, 140, 255, 0.3);
  animation: breathe 3s infinite ease-in-out;
}

.login-form .el-input {
  height: 40px;
}

.login-form .el-input input {
  height: 40px;
  background-color: rgba(10, 45, 90, 0.6) !important; /* 使用 !important 确保生效 */
  border: 1px solid rgba(74, 140, 255, 0.3);
  color: #e6f1ff;
}

.login-form .el-input input::placeholder {
  color: rgba(230, 241, 255, 0.6);
}

.login-form .el-input input:focus {
  border-color: #4a8cff;
  box-shadow: 0 0 5px rgba(74, 140, 255, 0.5);
}

.login-form .input-icon {
  height: 39px;
  width: 14px;
  margin-left: 0px;
  color: #4a8cff;
}

.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}

.login-code {
  width: 33%;
  height: 40px;
  float: right;

  img {
    cursor: pointer;
    vertical-align: middle;
  }
}

.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: rgba(230, 241, 255, 0.7);
  font-family: Arial, serif;
  font-size: 12px;
  letter-spacing: 1px;
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
  z-index: 1;
}

.login-code-img {
  height: 40px;
  padding-left: 12px;
}
</style>

<style>
/* 动画效果已移除以解决SASS编译问题 */
</style>
