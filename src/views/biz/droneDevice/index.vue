<template>
  <div class="p-2">
    <el-row :gutter="20">
      <!-- 部门树 -->
      <el-col :lg="4" :xs="24">
        <el-card shadow="hover">
          <el-input v-model="deptName" placeholder="请输入部门名称" prefix-icon="Search" clearable />
          <div class="mt-2 mb-2">
            <el-button
              size="small"
              :type="!queryParams.deptId ? 'primary' : 'info'"
              :plain="!!queryParams.deptId"
              @click="handleShowAllDepts"
              style="width: 100%"
            >
              {{ queryParams.deptId ? '📋 显示全部设备' : '📋 当前：全部设备' }}
            </el-button>
          </div>
          <el-tree
            ref="deptTreeRef"
            node-key="id"
            :data="deptOptions"
            :props="{ label: 'label', children: 'children' } as any"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            highlight-current
            default-expand-all
            @node-click="handleNodeClick"
          />
        </el-card>
      </el-col>

      <el-col :lg="20" :xs="24">
        <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
          <div v-show="showSearch" class="mb-[10px]">
            <el-card shadow="hover">
              <el-form ref="queryFormRef" :model="queryParams" :inline="true">
                <el-form-item label="关联机巢" prop="dockId">
                  <el-select v-model="queryParams.dockId" placeholder="请选择关联机巢" clearable filterable>
                    <el-option
                      v-for="device in flightDeviceOptions"
                      :key="device.id"
                      :label="`${device.nickname || device.deviceName} (${device.deviceSn})`"
                      :value="device.id"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="设备序列号" prop="deviceSn">
                  <el-input v-model="queryParams.deviceSn" placeholder="请输入设备序列号" clearable @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item label="设备名称" prop="deviceName">
                  <el-input v-model="queryParams.deviceName" placeholder="请输入设备名称" clearable @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item v-if="queryParams.deptId">
                  <el-tag type="info" closable @close="handleShowAllDepts"> 部门筛选：{{ getCurrentDeptName() }} </el-tag>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                  <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                </el-form-item>
              </el-form>
            </el-card>
          </div>
        </transition>

        <el-card shadow="never">
          <template #header>
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['biz:droneDevice:add']">新增</el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['biz:droneDevice:edit']">
                  修改
                </el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['biz:droneDevice:remove']">
                  删除
                </el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['biz:droneDevice:export']">导出</el-button>
              </el-col>
              <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
            </el-row>
          </template>

          <el-table v-loading="loading" border :data="droneDeviceList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="主键ID" align="center" prop="id" v-if="true" />
            <el-table-column label="设备名称" align="center" prop="nickname" />
            <el-table-column label="归属部门" align="center" prop="deptName" />
            <el-table-column label="设备序列号" align="center" prop="deviceSn" />
            <el-table-column label="设备型号" align="center" prop="deviceType" />
            <el-table-column label="关联机巢" align="center" prop="dockName" />
            <el-table-column label="备注" align="center" prop="remark" />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
              <template #default="scope">
                <el-tooltip content="修改" placement="top">
                  <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['biz:droneDevice:edit']"></el-button>
                </el-tooltip>
                <el-tooltip content="删除" placement="top">
                  <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['biz:droneDevice:remove']"></el-button>
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
          />
        </el-card>
      </el-col>
    </el-row>

    <!-- 添加或修改无人机设备对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="600px" append-to-body>
      <el-form ref="droneDeviceFormRef" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="设备名称" prop="deviceName">
              <el-input v-model="form.deviceName" placeholder="请输入设备名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="归属部门" prop="deptId">
              <el-tree-select
                v-model="form.deptId"
                :data="enabledDeptOptions"
                :props="{ value: 'id', label: 'label', children: 'children' } as any"
                value-key="id"
                placeholder="请选择归属部门"
                check-strictly
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="设备序列号" prop="deviceSn">
              <el-input v-model="form.deviceSn" placeholder="请输入设备序列号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备型号" prop="deviceType">
              <el-input v-model="form.deviceType" placeholder="请输入设备型号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="关联机巢" prop="dockId">
              <el-select v-model="form.dockId" placeholder="请选择关联机巢" clearable filterable>
                <el-option
                  v-for="device in flightDeviceOptions"
                  :key="device.id"
                  :label="`${device.nickname || device.deviceName} (${device.deviceSn})`"
                  :value="device.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" placeholder="请输入备注" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="DroneDevice" lang="ts">
import { watchEffect } from 'vue';
import { listDroneDevice, getDroneDevice, delDroneDevice, addDroneDevice, updateDroneDevice, deptTree } from '@/api/biz/droneDevice';
import { DroneDeviceVO, DroneDeviceQuery, DroneDeviceForm } from '@/api/biz/droneDevice/types';
import { listFlightDevice } from '@/api/biz/flightDevice';
import { FlightDeviceVO } from '@/api/biz/flightDevice/types';
import { DeptTreeVO } from '@/api/system/dept/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const droneDeviceList = ref<DroneDeviceVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(false);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

// 部门树相关
const deptName = ref('');
const deptOptions = ref<DeptTreeVO[]>([]);
const enabledDeptOptions = ref<DeptTreeVO[]>([]);
const deptTreeRef = ref<ElTreeInstance>();
const selectedDeptId = ref<string | number | undefined>(undefined); // 当前选中的部门ID
const deptMap = ref<Map<string, string>>(new Map()); // 部门ID到部门名称的映射

// 飞行设备相关
const flightDeviceOptions = ref<FlightDeviceVO[]>([]);
const flightDeviceMap = ref<Map<string, FlightDeviceVO>>(new Map()); // 飞行设备ID到设备对象的映射

const queryFormRef = ref<ElFormInstance>();
const droneDeviceFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: DroneDeviceForm = {
  id: undefined,
  remark: undefined,
  deptId: undefined,
  dockId: undefined,
  deviceSn: undefined,
  deviceName: undefined,
  deviceType: undefined
};
const data = reactive<PageData<DroneDeviceForm, DroneDeviceQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    deptId: undefined,
    dockId: undefined,
    deviceSn: undefined,
    deviceName: undefined,
    deviceType: undefined,
    params: {}
  },
  rules: {
    id: [{ required: true, message: '主键ID不能为空', trigger: 'blur' }],
    deviceSn: [{ required: true, message: '设备序列号不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 通过条件过滤节点 */
const filterNode = (value: string, data: any) => {
  if (!value) return true;
  return data.label.indexOf(value) !== -1;
};

/** 根据名称筛选部门树 */
watchEffect(
  () => {
    deptTreeRef.value?.filter(deptName.value);
  },
  {
    flush: 'post' // watchEffect会在DOM挂载或者更新之前就会触发，此属性控制在DOM元素更新后运行
  }
);

/** 查询部门下拉树结构 */
const getDeptTree = async () => {
  try {
    const res = await deptTree();
    deptOptions.value = res.data;
    enabledDeptOptions.value = filterDisabledDept(res.data);
    buildDeptMap(res.data);
  } catch (error) {
    console.error('获取部门树失败:', error);
  }
};

/** 查询飞行设备列表 */
const getFlightDeviceList = async () => {
  try {
    const res = await listFlightDevice({ pageNum: 1, pageSize: 1000 }); // 获取所有飞行设备
    flightDeviceOptions.value = res.rows;
    buildFlightDeviceMap(res.rows);
  } catch (error) {
    console.error('获取飞行设备列表失败:', error);
  }
};

/** 构建飞行设备ID到设备对象的映射 */
const buildFlightDeviceMap = (deviceList: FlightDeviceVO[]) => {
  const map = new Map<string, FlightDeviceVO>();
  deviceList.forEach((device) => {
    map.set(String(device.id), device);
  });
  flightDeviceMap.value = map;
};

/** 过滤禁用的部门 */
const filterDisabledDept = (deptList: DeptTreeVO[]) => {
  return deptList.filter((dept) => {
    if (dept.disabled) {
      return false;
    }
    if (dept.children && dept.children.length) {
      dept.children = filterDisabledDept(dept.children);
    }
    return true;
  });
};

/** 构建部门ID到部门名称的映射 */
const buildDeptMap = (deptList: DeptTreeVO[]) => {
  const map = new Map<string, string>();

  const traverse = (depts: DeptTreeVO[]) => {
    depts.forEach((dept) => {
      map.set(String(dept.id), dept.label);
      if (dept.children && dept.children.length) {
        traverse(dept.children);
      }
    });
  };

  traverse(deptList);
  deptMap.value = map;
};

/** 节点单击事件 */
const handleNodeClick = (data: DeptTreeVO) => {
  // 如果点击的是已选中的部门，则取消选择
  if (selectedDeptId.value === data.id) {
    selectedDeptId.value = undefined;
    queryParams.value.deptId = undefined;
    deptTreeRef.value?.setCurrentKey(undefined);
  } else {
    // 选择新的部门
    selectedDeptId.value = data.id;
    queryParams.value.deptId = data.id;
  }
  handleQuery();
};

/** 显示全部设备（取消部门筛选） */
const handleShowAllDepts = () => {
  selectedDeptId.value = undefined;
  queryParams.value.deptId = undefined;
  deptTreeRef.value?.setCurrentKey(undefined);
  handleQuery();
};

/** 获取当前选中部门的名称 */
const getCurrentDeptName = () => {
  if (!queryParams.value.deptId) return '';
  return deptMap.value.get(String(queryParams.value.deptId)) || '未知部门';
};

/** 处理设备列表数据，添加部门名称和关联机巢名称 */
const processDeviceList = (devices: any[]) => {
  return devices.map((device) => {
    const processed = { ...device };

    // 添加部门名称映射
    if (device.deptId && deptMap.value.has(String(device.deptId))) {
      processed.deptName = deptMap.value.get(String(device.deptId));
    }

    // 添加关联机巢名称映射
    if (device.dockId && flightDeviceMap.value.has(String(device.dockId))) {
      const flightDevice = flightDeviceMap.value.get(String(device.dockId));
      processed.dockName = flightDevice?.nickname || flightDevice?.deviceName || '未知设备';
    }

    return processed;
  });
};

/** 查询无人机设备列表 */
const getList = async () => {
  loading.value = true;
  const res = await listDroneDevice(queryParams.value);
  droneDeviceList.value = processDeviceList(res.rows);
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  droneDeviceFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  // 清除部门筛选
  selectedDeptId.value = undefined;
  queryParams.value.deptId = undefined;
  deptTreeRef.value?.setCurrentKey(undefined);
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: DroneDeviceVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加无人机设备';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: DroneDeviceVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getDroneDevice(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改无人机设备';
};

/** 提交按钮 */
const submitForm = () => {
  droneDeviceFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateDroneDevice(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addDroneDevice(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: DroneDeviceVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除无人机设备编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delDroneDevice(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'biz/droneDevice/export',
    {
      ...queryParams.value
    },
    `droneDevice_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getDeptTree();
  getFlightDeviceList();
  getList();
});
</script>
