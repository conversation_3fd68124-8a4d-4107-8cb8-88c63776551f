<template>
  <div class="p-2">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="隐患巡查记录" name="record">
        <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
          <div v-show="showSearch" class="mb-[10px]">
            <el-card shadow="hover">
              <el-form ref="queryFormRef" :model="queryParams" :inline="true">
                <el-form-item label="任务名称" prop="taskName">
                  <el-input v-model="queryParams.taskName" placeholder="请输入任务名称" clearable @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item label="任务频率" prop="taskFrequency">
                  <el-input v-model="queryParams.taskFrequency" placeholder="请输入任务频率" clearable @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item label="任务计划开始时间" prop="plannedStartTime">
                  <el-date-picker
                    clearable
                    v-model="queryParams.plannedStartTime"
                    type="date"
                    value-format="YYYY-MM-DD"
                    placeholder="请选择任务计划开始时间"
                  />
                </el-form-item>
                <el-form-item label="任务计划结束时间" prop="plannedEndTime">
                  <el-date-picker
                    clearable
                    v-model="queryParams.plannedEndTime"
                    type="date"
                    value-format="YYYY-MM-DD"
                    placeholder="请选择任务计划结束时间"
                  />
                </el-form-item>
                <el-form-item label="执行航线名称" prop="routeName">
                  <el-input v-model="queryParams.routeName" placeholder="请输入执行航线名称" clearable @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item label="执行设备名称" prop="executionDeviceName">
                  <el-input v-model="queryParams.executionDeviceName" placeholder="请输入执行设备名称" clearable @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item label="执行人员" prop="executor">
                  <el-input v-model="queryParams.executor" placeholder="请输入执行人员" clearable @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item label="实际开始时间" prop="actualStartTime">
                  <el-date-picker
                    clearable
                    v-model="queryParams.actualStartTime"
                    type="date"
                    value-format="YYYY-MM-DD"
                    placeholder="请选择实际开始时间"
                  />
                </el-form-item>
                <el-form-item label="实际结束时间" prop="actualEndTime">
                  <el-date-picker
                    clearable
                    v-model="queryParams.actualEndTime"
                    type="date"
                    value-format="YYYY-MM-DD"
                    placeholder="请选择实际结束时间"
                  />
                </el-form-item>
                <el-form-item label="返航高度" prop="returnAltitude">
                  <el-input v-model="queryParams.returnAltitude" placeholder="请输入返航高度" clearable @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item label="矢量操作" prop="vectorOperation">
                  <el-input v-model="queryParams.vectorOperation" placeholder="请输入矢量操作" clearable @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item label="机场名称" prop="dockName">
                  <el-input v-model="queryParams.dockName" placeholder="请输入机场名称" clearable @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item label="是否存在风险：Y是    N否" prop="hasRisk">
                  <el-input v-model="queryParams.hasRisk" placeholder="请输入是否存在风险：Y是    N否" clearable @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                  <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                </el-form-item>
              </el-form>
            </el-card>
          </div>
        </transition>

        <el-card shadow="never">
          <template #header>
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['biz:roadHazardRecord:add']">新增</el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['biz:roadHazardRecord:edit']"
                  >修改</el-button
                >
              </el-col>
              <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['biz:roadHazardRecord:remove']"
                  >删除</el-button
                >
              </el-col>
              <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['biz:roadHazardRecord:export']">导出</el-button>
              </el-col>
              <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
            </el-row>
          </template>

          <el-table v-loading="loading" border :data="roadHazardRecordList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="序号" align="center" width="80">
              <template #default="scope">
                <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column label="任务名称" align="center" prop="taskName" />
            <el-table-column label="航线" align="center" prop="routeName" />
            <el-table-column label="是否存在风险" align="center" prop="hasRisk">
              <template #default="scope">
                <dict-tag :options="road_hazard_has_risk" :value="scope.row.hasRisk" />
              </template>
            </el-table-column>
            <el-table-column label="巡检时间" align="center" width="320">
              <template #default="scope">
                <div>
                  {{ scope.row.actualStartTime ? parseTime(scope.row.actualStartTime, '{y}-{m}-{d} {h}:{i}:{s}') : '-' }}
                  ~
                  {{ scope.row.actualEndTime ? parseTime(scope.row.actualEndTime, '{y}-{m}-{d} {h}:{i}:{s}') : '-' }}
                </div>
                <div v-if="computeDuration(scope.row.actualStartTime, scope.row.actualEndTime)">
                  耗时:
                  {{ computeDuration(scope.row.actualStartTime, scope.row.actualEndTime) }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
              <template #default="scope">
                <el-button link type="success" @click="handleViewImages(scope.row)">巡检图像</el-button>
                <el-button link type="info" @click="handleViewReport(scope.row)">巡检报告</el-button>
                <!-- 暂时隐藏修改和删除按钮 -->
                <!-- <el-tooltip content="修改" placement="top">
                  <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['biz:roadHazardRecord:edit']"></el-button>
                </el-tooltip>
                <el-tooltip content="删除" placement="top">
                  <el-button
                    link
                    type="primary"
                    icon="Delete"
                    @click="handleDelete(scope.row)"
                    v-hasPermi="['biz:roadHazardRecord:remove']"
                  ></el-button>
                </el-tooltip> -->
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
          />
        </el-card>
        <!-- 添加或修改隐患巡查记录对话框 -->
        <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
          <el-form ref="roadHazardRecordFormRef" :model="form" :rules="rules" label-width="80px">
            <el-form-item label="任务名称" prop="taskName">
              <el-input v-model="form.taskName" placeholder="请输入任务名称" />
            </el-form-item>
            <el-form-item label="执行航线名称" prop="routeName">
              <el-input v-model="form.routeName" placeholder="请输入执行航线名称" />
            </el-form-item>
            <el-form-item label="是否存在风险：Y是    N否" prop="hasRisk">
              <el-input v-model="form.hasRisk" placeholder="请输入是否存在风险：Y是    N否" />
            </el-form-item>
          </el-form>
          <template #footer>
            <div class="dialog-footer">
              <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
              <el-button @click="cancel">取 消</el-button>
            </div>
          </template>
        </el-dialog>

        <!-- 巡检图像弹窗 -->
        <el-dialog title="巡检记录" v-model="imageDialogVisible" width="80%" append-to-body destroy-on-close>
          <div v-if="imageDialogVisible">
            <el-card shadow="never">
              <el-table :data="hazardPointList" border v-loading="hazardPointLoading" size="small">
                <el-table-column type="index" label="序号" width="60" align="center" />
                <el-table-column prop="hazardPointName" label="隐患点名称" align="center" />
                <el-table-column label="是否存在风险" align="center" prop="hasRisk">
                  <template #default="scope">
                    <dict-tag :options="road_hazard_has_risk" :value="scope.row.hasRisk" />
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="100" align="center">
                  <template #default="scope">
                    <el-button link type="primary" @click="handleViewHazardPointImages(scope.row)">查看图片</el-button>
                  </template>
                </el-table-column>
              </el-table>

              <pagination
                v-show="hazardPointTotal > 0"
                :total="hazardPointTotal"
                v-model:page="hazardPointQueryParams.pageNum"
                v-model:limit="hazardPointQueryParams.pageSize"
                @pagination="getHazardPointList"
                class="mt-4"
              />
            </el-card>
          </div>
        </el-dialog>
      </el-tab-pane>

      <el-tab-pane label="隐患点" name="point">
        <RoadHazardPoint />
      </el-tab-pane>
    </el-tabs>

    <!-- 图片查看器 -->
    <HazardPointImageViewer
      v-model="imageViewerVisible"
      :hazard-point-id="currentHazardPointId!"
      :inspect-record-id="currentInspectRecordId!"
      :hazard-point-name="currentHazardPointName"
    />

    <!-- BaskReport报表预览弹窗 -->
    <el-dialog
      title="隐患巡检报告"
      v-model="reportDialogVisible"
      width="900px"
      append-to-body
      destroy-on-close
      :close-on-click-modal="false"
      draggable
      align-center
    >
      <BaskReportPreview
        v-if="reportDialogVisible"
        :reportCode="'jx_jt_roadHazard_report_dev'"
        :parameters="reportParameters"
        :showToolbar="true"
        :height="'700px'"
        :fileName="'隐患巡检报告'"
      />
    </el-dialog>
  </div>
</template>

<script setup name="RoadHazardRecord" lang="ts">
import {
  listRoadHazardRecord,
  getRoadHazardRecord,
  delRoadHazardRecord,
  addRoadHazardRecord,
  updateRoadHazardRecord,
  getHazardPointsByRecordId
} from '@/api/biz/roadHazardRecord';
import { RoadHazardRecordVO, RoadHazardRecordQuery, RoadHazardRecordForm, HazardPointSummaryVO } from '@/api/biz/roadHazardRecord/types';
import RoadHazardPoint from '@/views/biz/roadHazardPoint/index.vue';
import HazardPointImageViewer from '@/components/HazardPointImageViewer.vue';
// BaskReport组件导入
import BaskReportPreview from '@/components/BaskReportPreview/index.vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { road_hazard_has_risk } = toRefs<any>(proxy?.useDict('road_hazard_has_risk'));

const activeTab = ref('record');
const roadHazardRecordList = ref<RoadHazardRecordVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(false);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

// 巡检图像弹窗相关状态
const imageDialogVisible = ref(false);
const hazardPointList = ref<HazardPointSummaryVO[]>([]);
const hazardPointLoading = ref(false);
const hazardPointTotal = ref(0);
const currentRecordId = ref<number | string>();
const hazardPointQueryParams = reactive({
  pageNum: 1,
  pageSize: 10
});

// 图片查看器相关状态
const imageViewerVisible = ref(false);
const currentHazardPointId = ref<string | number>();
const currentHazardPointName = ref<string>();
const currentInspectRecordId = ref<string | number>();

// BaskReport相关响应式变量
const reportDialogVisible = ref(false);
const reportParameters = ref({});

const queryFormRef = ref<ElFormInstance>();
const roadHazardRecordFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: RoadHazardRecordForm = {
  id: undefined,
  taskName: undefined,
  routeName: undefined,
  hasRisk: undefined
};
const data = reactive<PageData<RoadHazardRecordForm, RoadHazardRecordQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    taskName: undefined,
    taskType: undefined,
    taskFrequency: undefined,
    plannedStartTime: undefined,
    plannedEndTime: undefined,
    routeName: undefined,
    executionDeviceName: undefined,
    executor: undefined,
    taskStatus: undefined,
    actualStartTime: undefined,
    actualEndTime: undefined,
    returnAltitude: undefined,
    vectorOperation: undefined,
    dockName: undefined,
    hasRisk: undefined,
    params: {}
  },
  rules: {}
});

const { queryParams, form, rules } = toRefs(data);

/**
 * 计算两个时间的耗时，返回 "xxminyy s" 格式，如果时间为空则返回空字符串
 */
const computeDuration = (start?: string, end?: string): string => {
  if (!start || !end) return '';
  const startTime = new Date(start.replace(' ', 'T')).getTime();
  const endTime = new Date(end.replace(' ', 'T')).getTime();
  const duration = endTime - startTime;
  if (duration <= 0) return '';

  const minutes = Math.floor(duration / (1000 * 60));
  const seconds = Math.floor((duration % (1000 * 60)) / 1000);

  return `${minutes}min${seconds}s`;
};

/** 查询隐患巡查记录列表 */
const getList = async () => {
  loading.value = true;
  const res = await listRoadHazardRecord(queryParams.value);
  roadHazardRecordList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  roadHazardRecordFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: RoadHazardRecordVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加隐患巡查记录';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: RoadHazardRecordVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getRoadHazardRecord(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改隐患巡查记录';
};

/** 提交按钮 */
const submitForm = () => {
  roadHazardRecordFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateRoadHazardRecord(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addRoadHazardRecord(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: RoadHazardRecordVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除隐患巡查记录编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delRoadHazardRecord(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'biz/roadHazardRecord/export',
    {
      ...queryParams.value
    },
    `roadHazardRecord_${new Date().getTime()}.xlsx`
  );
};

/** 获取隐患点列表 */
const getHazardPointList = async () => {
  if (!currentRecordId.value) return;

  hazardPointLoading.value = true;
  try {
    const res = await getHazardPointsByRecordId(currentRecordId.value, hazardPointQueryParams);
    hazardPointList.value = res.rows;
    hazardPointTotal.value = res.total;
  } catch (error) {
    console.error('获取隐患点列表失败:', error);
    proxy?.$modal.msgError('获取隐患点列表失败');
  } finally {
    hazardPointLoading.value = false;
  }
};

/** 查看巡检图像 */
const handleViewImages = (row: RoadHazardRecordVO) => {
  currentRecordId.value = row.id;
  hazardPointQueryParams.pageNum = 1;
  imageDialogVisible.value = true;
  getHazardPointList();
};

/** 查看隐患点图片 */
const handleViewHazardPointImages = (hazardPoint: HazardPointSummaryVO) => {
  currentHazardPointId.value = hazardPoint.hazardPointId;
  currentHazardPointName.value = hazardPoint.hazardPointName;
  currentInspectRecordId.value = currentRecordId.value; // 使用当前的巡检记录ID
  imageViewerVisible.value = true;
};

/** 查看巡检报告 */
const handleViewReport = async (row: RoadHazardRecordVO) => {
  try {
    // 设置报表参数
    reportParameters.value = { hazardRecordId: row.id };
    // 显示报表预览弹窗
    reportDialogVisible.value = true;
  } catch (error) {
    console.error('加载报告失败:', error);
    proxy?.$modal.msgError('加载报告失败，请稍后再试。');
  }
};

onMounted(() => {
  getList();
});
</script>
