<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="任务名称" prop="taskName">
              <el-input v-model="queryParams.taskName" placeholder="请输入任务名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="任务编号" prop="taskNo">
              <el-input v-model="queryParams.taskNo" placeholder="请输入任务编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="任务频率" prop="taskFrequency">
              <el-select v-model="queryParams.taskFrequency" placeholder="请选择任务频率" clearable>
                <el-option v-for="dict in flight_task_frequency" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="航线名称" prop="routeName">
              <el-input v-model="queryParams.routeName" placeholder="请输入执行航线名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="设备名称" prop="executionDeviceName">
              <el-input v-model="queryParams.executionDeviceName" placeholder="请输入执行设备名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="任务状态" prop="taskStatus">
              <el-select v-model="queryParams.taskStatus" placeholder="请选择任务状态" clearable>
                <el-option v-for="dict in flight_task_status" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="开始时间" prop="actualStartTime">
              <el-date-picker
                clearable
                v-model="queryParams.actualStartTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择实际开始时间"
              />
            </el-form-item>
            <el-form-item label="结束时间" prop="actualEndTime">
              <el-date-picker clearable v-model="queryParams.actualEndTime" type="date" value-format="YYYY-MM-DD" placeholder="请选择实际结束时间" />
            </el-form-item>
            <el-form-item label="矢量操作" prop="vectorOperation">
              <el-select v-model="queryParams.vectorOperation" placeholder="请选择矢量操作" clearable>
                <el-option v-for="dict in flight_task_vector_operation" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['biz:flightTaskResult:add']"> 新增 </el-button>
          </el-col>
          <!--          <el-col :span="1.5">-->
          <!--            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['biz:flightTaskResult:edit']"-->
          <!--              >修改-->
          <!--            </el-button>-->
          <!--          </el-col>-->
          <!--          <el-col :span="1.5">-->
          <!--            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['biz:flightTaskResult:remove']"-->
          <!--              >删除-->
          <!--            </el-button>-->
          <!--          </el-col>-->
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['biz:flightTaskResult:export']">导出 </el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="flightTaskResultList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="任务名称" align="center" prop="taskName" />
        <el-table-column label="任务类型" align="center" prop="taskType" />
        <el-table-column label="执行航线名称" align="center" prop="routeName" />
        <el-table-column label="机场名称" align="center" prop="dockName" />
        <el-table-column label="无人机名称" align="center" prop="droneName" />
        <el-table-column label="飞行计划名称" align="center" prop="flightPlanName" />
        <el-table-column label="实际执行时间" align="center" width="320">
          <template #default="scope">
            <div>
              {{ scope.row.actualStartTime ? parseTime(scope.row.actualStartTime, '{y}-{m}-{d} {h}:{i}:{s}') : '-' }}
              ~
              {{ scope.row.actualEndTime ? parseTime(scope.row.actualEndTime, '{y}-{m}-{d} {h}:{i}:{s}') : '-' }}
            </div>
            <div v-if="computeDuration(scope.row.actualStartTime, scope.row.actualEndTime)">
              耗时:
              {{ computeDuration(scope.row.actualStartTime, scope.row.actualEndTime) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" @click="handleViewImages(scope.row)">查看图片</el-button>
            <el-button link type="primary" @click="handleViewVideo(scope.row)">查看录像</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改飞行任务结果记录对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="680px" append-to-body>
      <el-form ref="flightTaskResultFormRef" :model="form" :rules="rules" label-width="128px">
        <el-form-item label="任务计划" prop="taskId">
          <el-select v-model="form.taskId" placeholder="请选择任务计划" filterable @change="taskChange">
            <el-option v-for="task in flightTaskInfoList" :key="task.id" :label="task.taskName" :value="task.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="任务名称" prop="taskName">
          <el-input v-model="form.taskName" placeholder="请输入任务名称" />
        </el-form-item>
        <el-form-item label="任务编号" prop="taskNo">
          <el-input v-model="form.taskNo" placeholder="请输入任务编号" />
        </el-form-item>
        <!--        <el-form-item label="任务类型" prop="taskType">-->
        <!--          <el-select v-model="form.taskType" placeholder="请选择任务类型">-->
        <!--            <el-option-->
        <!--                v-for="dict in flight_task_type"-->
        <!--                :key="dict.value"-->
        <!--                :label="dict.label"-->
        <!--                :value="dict.value"-->
        <!--            ></el-option>-->
        <!--          </el-select>-->
        <!--        </el-form-item>-->
        <el-form-item label="任务频率" prop="taskFrequency">
          <el-select v-model="form.taskFrequency" placeholder="请选择任务频率">
            <el-option v-for="dict in flight_task_frequency" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="执行航线名称" prop="routeName">
          <el-input v-model="form.routeName" placeholder="请输入执行航线名称" />
        </el-form-item>
        <el-form-item label="执行设备名称" prop="executionDeviceName">
          <el-input v-model="form.executionDeviceName" placeholder="请输入执行设备名称" />
        </el-form-item>
        <el-form-item label="任务状态" prop="taskStatus">
          <el-select v-model="form.taskStatus" placeholder="请选择任务状态" clearable>
            <el-option v-for="dict in flight_task_status" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="实际开始时间" prop="actualStartTime">
          <el-date-picker
            clearable
            v-model="form.actualStartTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择实际开始时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="实际结束时间" prop="actualEndTime">
          <el-date-picker clearable v-model="form.actualEndTime" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择实际结束时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="返航高度" prop="returnAltitude">
          <el-input v-model="form.returnAltitude" placeholder="请输入返航高度" />
        </el-form-item>
        <el-form-item label="矢量操作" prop="vectorOperation">
          <el-select v-model="form.vectorOperation" placeholder="请选择矢量操作">
            <el-option v-for="dict in flight_task_vector_operation" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="notes">
          <el-input v-model="form.notes" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 查看图片弹窗 -->
    <el-dialog title="任务图片列表" v-model="imageDialogVisible" width="70%" append-to-body destroy-on-close>
      <div v-if="imageDialogVisible">
        <el-card shadow="never" class="filter-card">
          <el-form ref="imageQueryFormRef" :model="imageQueryParams" :inline="true" size="small" class="compact-form">
            <el-form-item label="文件名" prop="fileName">
              <el-input v-model="imageQueryParams.fileName" placeholder="请输入文件名" style="width: 140px" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleImageQuery" size="small">搜索</el-button>
              <el-button icon="Refresh" @click="resetImageQuery" size="small">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <el-card v-if="imageLoading" shadow="never" v-loading="imageLoading" class="image-card">
          <p>加载中...</p>
        </el-card>
        <el-card v-else-if="imageList && imageList.length > 0" shadow="never" class="image-card">
          <el-table :data="imageList" border style="width: 100%" size="small" class="compact-table">
            <el-table-column type="index" label="序号" width="50" align="center" />
            <el-table-column prop="fileName" label="文件名" min-width="150" align="center" />
            <el-table-column prop="latitude" label="纬度" min-width="80" align="center" />
            <el-table-column prop="longitude" label="经度" min-width="80" align="center" />
            <el-table-column prop="imageTime" label="拍摄时间" min-width="120" align="center" />
            <el-table-column prop="fileSize" label="文件大小" min-width="80" align="center">
              <template #default="scope">
                {{ formatFileSize(scope.row.fileSize) }}
              </template>
            </el-table-column>
            <el-table-column label="图片" width="60" align="center">
              <template #default="scope">
                <ImagePreview :width="40" :height="40" :src="scope.row.imageUrl" />
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="imageTotal > 0"
            :total="imageTotal"
            v-model:page="imageQueryParams.pageNum"
            v-model:limit="imageQueryParams.pageSize"
            @pagination="getImageList"
            class="compact-pagination"
          />
        </el-card>
        <el-card v-else shadow="never" class="image-card">
          <p>暂无图片记录。</p>
        </el-card>
      </div>
    </el-dialog>
    <!-- 图片预览弹窗 -->
    <PreviewModal v-model="previewVisible" :image-url="previewImageUrl" @close="handlePreviewClose" />
    <!-- 查看录像弹窗 -->
    <el-dialog title="任务录像列表" v-model="videoDialogVisible" width="70%" append-to-body destroy-on-close>
      <div v-if="videoDialogVisible">
        <el-card v-if="videoLoading" shadow="never" v-loading="videoLoading" class="video-card">
          <p>加载中...</p>
        </el-card>
        <el-card v-else-if="videoList && videoList.length > 0" shadow="never" class="video-card">
          <el-table :data="videoList" border style="width: 100%" size="small" class="compact-table">
            <el-table-column type="index" label="序号" width="50" align="center" />
            <el-table-column prop="fileName" label="文件名" min-width="200" align="center" />
            <el-table-column prop="fileSize" label="文件大小" min-width="100" align="center">
              <template #default="scope">
                {{ formatFileSize(scope.row.fileSize) }}
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" min-width="150" align="center" />
            <el-table-column prop="drone" label="无人机" min-width="120" align="center" />
            <el-table-column prop="payload" label="载荷" min-width="120" align="center" />
            <el-table-column label="操作" width="150" align="center">
              <template #default="scope">
                <el-button link type="primary" @click="playVideo(scope.row.url)" size="small">播放</el-button>
                <el-button link type="success" @click="downloadVideo(scope.row.url, scope.row.fileName)" size="small">下载</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
        <el-card v-else shadow="never" class="video-card">
          <p>暂无录像记录。</p>
        </el-card>
      </div>
    </el-dialog>
  </div>
</template>

<script setup name="FlightTaskResult" lang="ts">
import {
  listFlightTaskResult,
  getFlightTaskResult,
  delFlightTaskResult,
  addFlightTaskResult,
  updateFlightTaskResult,
  getFlightImages,
  getFlightVideos
} from '@/api/biz/flightTaskResult';
import { FlightTaskResultVO, FlightTaskResultQuery, FlightTaskResultForm } from '@/api/biz/flightTaskResult/types';
import { FlightDeviceVO } from '@/api/biz/flightDevice/types';
import { FlightRouteInfoVO } from '@/api/biz/flightRouteInfo/types';
import { listFlightTaskInfo, getFlightTaskInfo } from '@/api/biz/flightTaskInfo';
import { FlightTaskInfoVO } from '@/api/biz/flightTaskInfo/types';
import { listFlightDevice, getFlightDevice } from '@/api/biz/flightDevice';
import { listFlightRouteInfo, getFlightRouteInfo } from '@/api/biz/flightRouteInfo';
import PreviewModal from '@/components/PreviewModal/index.vue';
import ImagePreview from '@/components/ImagePreview/index.vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { flight_task_type, flight_task_frequency, flight_task_status, flight_task_vector_operation, ai_image_tag } = toRefs<any>(
  proxy?.useDict('flight_task_type', 'flight_task_frequency', 'flight_task_status', 'flight_task_vector_operation', 'ai_image_tag')
);

const flightTaskResultList = ref<FlightTaskResultVO[]>([]);
const flightDeviceList = ref<FlightDeviceVO[]>([]);
const flightRouteInfoList = ref<FlightRouteInfoVO[]>([]);
const flightTaskInfoList = ref<FlightTaskInfoVO[]>([]);

const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(false);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const flightTaskResultFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: FlightTaskResultForm = {
  id: undefined,
  taskId: undefined,
  taskName: undefined,
  taskNo: undefined,
  taskType: undefined,
  taskFrequency: undefined,
  plannedStartTime: undefined,
  plannedEndTime: undefined,
  dailyScheduledStartTime: undefined,
  dailyScheduledEndTime: undefined,
  batteryCapacityReached: undefined,
  storageCapacityReached: undefined,
  routeId: undefined,
  routeName: undefined,
  executionDeviceId: undefined,
  executionDeviceName: undefined,
  executor: undefined,
  estimatedPayload: undefined,
  notes: undefined,
  taskStatus: undefined,
  actualStartTime: undefined,
  actualEndTime: undefined,
  returnAltitude: undefined,
  vectorOperation: undefined
};
const data = reactive<PageData<FlightTaskResultForm, FlightTaskResultQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    taskId: undefined,
    taskName: undefined,
    taskNo: undefined,
    taskType: undefined,
    taskFrequency: undefined,
    plannedStartTime: undefined,
    plannedEndTime: undefined,
    dailyScheduledStartTime: undefined,
    dailyScheduledEndTime: undefined,
    batteryCapacityReached: undefined,
    storageCapacityReached: undefined,
    routeId: undefined,
    routeName: undefined,
    executionDeviceId: undefined,
    executionDeviceName: undefined,
    executor: undefined,
    estimatedPayload: undefined,
    notes: undefined,
    taskStatus: undefined,
    actualStartTime: undefined,
    actualEndTime: undefined,
    returnAltitude: undefined,
    vectorOperation: undefined,
    params: {}
  },
  rules: {
    id: [{ required: true, message: '任务执行结果ID，主键不能为空', trigger: 'blur' }],
    taskName: [{ required: true, message: '任务名称不能为空', trigger: 'blur' }],
    taskNo: [{ required: true, message: '任务编号不能为空', trigger: 'blur' }],
    taskType: [{ required: true, message: '任务类型不能为空', trigger: 'change' }],
    taskFrequency: [{ required: true, message: '任务频率不能为空', trigger: 'change' }],
    taskStatus: [{ required: true, message: '任务状态不能为空', trigger: 'change' }],
    plannedStartTime: [{ required: true, message: '任务计划开始时间不能为空', trigger: 'blur' }],
    plannedEndTime: [{ required: true, message: '任务计划结束时间不能为空', trigger: 'blur' }],
    actualStartTime: [{ required: true, message: '实际开始时间不能为空', trigger: 'blur' }],
    actualEndTime: [{ required: true, message: '实际结束时间不能为空', trigger: 'blur' }],
    routeId: [{ required: true, message: '执行航线id不能为空', trigger: 'blur' }],
    executionDeviceId: [{ required: true, message: '执行设备id不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询飞行任务结果记录列表 */
const getList = async () => {
  loading.value = true;
  const res = await listFlightTaskResult(queryParams.value);
  flightTaskResultList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

const getDeviceList = async () => {
  const res = await listFlightDevice();
  flightDeviceList.value = res.rows;
};

const getRouteList = async () => {
  const res = await listFlightRouteInfo();
  flightRouteInfoList.value = res.rows;
};

const getTaskInfoList = async () => {
  const res = await listFlightTaskInfo();
  flightTaskInfoList.value = res.rows;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  flightTaskResultFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

const taskChange = async (value: string) => {
  // for (let i = 0; i < flightTaskInfoList.value.length; i++) {
  //   if (value == flightTaskInfoList.value[i].id) {
  //     const task = JSON.parse(JSON.stringify(flightTaskInfoList.value[i]));
  //     task.id = form.value.id;
  //     Object.assign(form.value, task);
  //     const res = await getFlightDevice(task.executionDeviceId);
  //     form.value.executionDeviceName = res.data.deviceName;
  //     const res1 = await getFlightRouteInfo(task.routeId);
  //     form.value.routeName = res1.data.routeName;
  //     return;
  //   }
  // }
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: FlightTaskResultVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  getTaskInfoList();
  reset();
  dialog.visible = true;
  dialog.title = '添加飞行任务结果记录';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: FlightTaskResultVO) => {
  getTaskInfoList();
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getFlightTaskResult(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改飞行任务结果记录';
};

/** 提交按钮 */
const submitForm = () => {
  flightTaskResultFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateFlightTaskResult(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addFlightTaskResult(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: FlightTaskResultVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除飞行任务结果记录编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delFlightTaskResult(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'biz/flightTaskResult/export',
    {
      ...queryParams.value
    },
    `飞行任务记录.xlsx`
  );
};

const handleViewImages = (row: FlightTaskResultVO) => {
  if (!row.flightId) {
    proxy?.$modal.msgWarning('飞行记录ID不存在');
    return;
  }
  currentFlightId.value = row.flightId;
  imageQueryParams.flightId = row.flightId;
  imageDialogVisible.value = true;
  getImageList();
};

const handleViewVideo = (row: FlightTaskResultVO) => {
  if (!row.flightId) {
    proxy?.$modal.msgWarning('飞行记录ID不存在');
    return;
  }
  currentFlightId.value = row.flightId;
  videoDialogVisible.value = true;
  getVideoList();
};

/**
 * 计算两个时间的耗时，返回 "xxminyy s" 格式，如果时间为空则返回空字符串
 */
const computeDuration = (start?: string, end?: string): string => {
  if (!start || !end) return '';
  const startTime = new Date(start.replace(' ', 'T')).getTime();
  const endTime = new Date(end.replace(' ', 'T')).getTime();
  if (isNaN(startTime) || isNaN(endTime) || endTime <= startTime) return '';
  const diffSeconds = Math.floor((endTime - startTime) / 1000);
  const minutes = Math.floor(diffSeconds / 60);
  const seconds = diffSeconds % 60;
  return `${minutes}min${seconds}s`;
};

// 图片查看相关
const imageDialogVisible = ref(false);
const imageLoading = ref(false);
const imageList = ref<any[]>([]);
const imageTotal = ref(0);
const currentFlightId = ref<string | null>(null);
const imageQueryFormRef = ref<ElFormInstance>();

// 视频查看相关
const videoDialogVisible = ref(false);
const videoLoading = ref(false);
const videoList = ref<any[]>([]);
const videoTotal = ref(0);

const imageQueryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  flightId: undefined,
  fileName: ''
});

const getImageList = async () => {
  if (!currentFlightId.value) {
    imageList.value = [];
    imageLoading.value = false;
    return;
  }

  imageLoading.value = true;
  try {
    const res = await getFlightImages(currentFlightId.value);
    if (res.data && Array.isArray(res.data)) {
      imageList.value = res.data.map((item: any) => ({
        id: item.file_id,
        stakeNo: '-',
        latitude: item.shoot_latitude || '-',
        longitude: item.shoot_longitude || '-',
        imageTime: item.create_time ? new Date(item.create_time).toLocaleString() : '-',
        labelType: '',
        imageUrl: item.url,
        fileName: item.file_name,
        fileSize: item.file_size,
        drone: item.drone,
        payload: item.payload
      }));
      imageTotal.value = res.data.length;
    } else {
      imageList.value = [];
      imageTotal.value = 0;
    }
  } catch (error) {
    proxy?.$modal.msgError('获取图片列表失败');
    imageList.value = [];
    imageTotal.value = 0;
  } finally {
    imageLoading.value = false;
  }
};

const handleImageQuery = () => {
  imageQueryParams.pageNum = 1;
  getImageList();
};

const resetImageQuery = () => {
  imageQueryFormRef.value?.resetFields();
  imageQueryParams.pageNum = 1;
  imageQueryParams.fileName = '';
  handleImageQuery();
};

const formatFileSize = (bytes: number): string => {
  if (!bytes) return '-';
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return (bytes / Math.pow(1024, i)).toFixed(2) + ' ' + sizes[i];
};

const previewVisible = ref(false);
const previewImageUrl = ref('');

const openPreview = (row: any) => {
  previewImageUrl.value = row.imageUrl;
  previewVisible.value = true;
};

const handlePreviewClose = () => {
  previewVisible.value = false;
  previewImageUrl.value = '';
};

const getVideoList = async () => {
  if (!currentFlightId.value) {
    videoList.value = [];
    videoLoading.value = false;
    return;
  }

  videoLoading.value = true;
  try {
    const res = await getFlightVideos(currentFlightId.value);
    if (res.data && Array.isArray(res.data)) {
      videoList.value = res.data.map((item: any) => ({
        id: item.file_id,
        fileName: item.file_name,
        fileSize: item.file_size,
        createTime: item.create_time ? new Date(item.create_time).toLocaleString() : '-',
        drone: item.drone,
        payload: item.payload,
        url: item.url,
        duration: '-' // 视频时长信息不在返回数据中
      }));
      videoTotal.value = res.data.length;
    } else {
      videoList.value = [];
      videoTotal.value = 0;
    }
  } catch (error) {
    proxy?.$modal.msgError('获取录像列表失败');
    videoList.value = [];
    videoTotal.value = 0;
  } finally {
    videoLoading.value = false;
  }
};

const playVideo = (videoUrl: string) => {
  window.open(videoUrl, '_blank');
};

const downloadVideo = (videoUrl: string, fileName: string) => {
  const link = document.createElement('a');
  link.href = videoUrl;
  link.download = fileName;
  link.click();
};

onMounted(() => {
  getList();
});
</script>

<style scoped>
/* 紧凑布局样式 */
.filter-card {
  margin-bottom: 8px;
}

.compact-form {
  margin-bottom: 0;
}

.compact-form .el-form-item {
  margin-bottom: 6px;
  margin-right: 12px;
}

.image-card {
  margin-top: 6px;
}

.compact-table {
  font-size: 12px;
}

.compact-table .el-table__header th {
  padding: 6px 0;
  font-size: 12px;
  font-weight: 600;
}

.compact-table .el-table__body td {
  padding: 4px 0;
}

.compact-pagination {
  margin-top: 8px;
}

.compact-pagination .el-pagination {
  justify-content: center;
  font-size: 12px;
}

/* 图片缩略图样式优化 */
.el-image {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.el-image:hover {
  transform: scale(1.05);
}

/* 表格行高度优化 */
.el-table--small .el-table__body td {
  padding: 3px 0;
}

/* 搜索表单紧凑化 */
.el-form--inline .el-form-item {
  margin-right: 8px;
  margin-bottom: 6px;
}

/* 进一步减小卡片间距 */
.el-card {
  margin-bottom: 8px;
}

/* 优化表格内容显示 */
.compact-table .cell {
  padding: 4px 8px;
}

/* 减小分页器高度 */
.compact-pagination .el-pagination .el-pager li {
  min-width: 28px;
  height: 28px;
  line-height: 28px;
}

/* 弹窗高度控制 */
:deep(.el-dialog) {
  max-height: 85vh;
  display: flex;
  flex-direction: column;
}

:deep(.el-dialog__body) {
  flex: 1;
  overflow-y: auto;
  max-height: calc(85vh - 120px);
}

/* 表格容器高度优化 */
.image-card .el-card__body {
  max-height: calc(85vh - 200px);
  overflow-y: auto;
}

/* 确保表格在容器内正确显示 */
.compact-table {
  max-height: calc(85vh - 250px);
}
/* 视频卡片样式 */
.video-card {
  margin-top: 6px;
}

.video-card .el-card__body {
  max-height: calc(85vh - 200px);
  overflow-y: auto;
}
</style>
