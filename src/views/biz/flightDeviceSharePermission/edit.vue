<template>
  <el-dialog :title="dialog.title" v-model="dialog.visible" width="700px" append-to-body @close="handleClose">
    <el-form ref="flightDeviceSharePermissionFormRef" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="资源拥有者" prop="ownerTenantId">
        <el-select 
          v-model="form.ownerTenantId" 
          placeholder="请选择资源拥有者" 
          :clearable="!props.presetOwnerTenantId"
          filterable
          :disabled="!!props.presetOwnerTenantId"
        >
          <el-option v-for="tenant in tenantList" :key="tenant.tenantId" :label="tenant.companyName" :value="tenant.tenantId" />
        </el-select>
      </el-form-item>
      <el-form-item label="资源被授权方" prop="targetTenantId">
        <el-select v-model="form.targetTenantId" placeholder="请选择资源被授权方" clearable filterable>
          <el-option v-for="tenant in tenantList" :key="tenant.tenantId" :label="tenant.companyName" :value="tenant.tenantId" />
        </el-select>
      </el-form-item>
      <el-form-item label="设备" prop="deviceId">
        <el-select v-model="form.deviceId" placeholder="请选择设备" :disabled="!form.ownerTenantId" filterable :loading="deviceLoading">
          <el-option v-for="device in deviceList" :key="device.id" :label="device.deviceName" :value="device.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="授权状态" prop="status">
        <el-select v-model="form.status" placeholder="请选择授权状态">
          <el-option v-for="dict in biz_flight_device_share_permission_status" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="备注信息" prop="remark">
        <el-input v-model="form.remark" type="textarea" placeholder="请输入备注信息" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="handleClose">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="FlightDeviceSharePermissionEdit" lang="ts">
import { listFlightDeviceSharePermission, getFlightDeviceSharePermission, addFlightDeviceSharePermission, updateFlightDeviceSharePermission } from '@/api/biz/flightDeviceSharePermission';
import { listTenant } from '@/api/system/tenant';
import { FlightDeviceSharePermissionForm } from '@/api/biz/flightDeviceSharePermission/types';
import { listFlightDevice } from '@/api/biz/flightDevice';

interface Props {
  visible?: boolean;
  editId?: string | number;
  presetDeviceId?: string | number;
  presetOwnerTenantId?: string | number;
  presetTargetTenantId?: string | number;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  editId: undefined,
  presetDeviceId: undefined,
  presetOwnerTenantId: undefined,
  presetTargetTenantId: undefined
});

const emit = defineEmits<{
  close: [];
  success: [];
}>();

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { biz_flight_device_share_permission_status } = toRefs<any>(proxy?.useDict('biz_flight_device_share_permission_status'));

const tenantList = ref<any[]>([]);
const deviceList = ref<any[]>([]);
const deviceLoading = ref(false);
const buttonLoading = ref(false);

const flightDeviceSharePermissionFormRef = ref<ElFormInstance>();

const dialog = reactive({
  visible: false,
  title: ''
});

const initFormData: FlightDeviceSharePermissionForm = {
  id: undefined,
  ownerTenantId: undefined,
  targetTenantId: undefined,
  deviceId: undefined,
  permissionType: undefined,
  status: '1', // 默认有效
  startTime: undefined,
  endTime: undefined,
  remark: undefined
};

const form = ref<FlightDeviceSharePermissionForm>({ ...initFormData });

const rules = {
  ownerTenantId: [{ required: true, message: '资源拥有者租户不能为空', trigger: 'blur' }],
  targetTenantId: [{ required: true, message: '资源被授权方租户不能为空', trigger: 'blur' }],
  deviceId: [{ required: true, message: '设备不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '授权状态不能为空', trigger: 'change' }]
};

const getTenantList = async () => {
  const res = await listTenant({
    tenantId: '',
    contactUserName: '',
    contactPhone: '',
    companyName: '',
    pageNum: 1,
    pageSize: 1000
  });
  tenantList.value = res.data || res.rows || [];
};

const getDeviceList = async (tenantId: string | number) => {
  if (!tenantId) {
    deviceList.value = [];
    return;
  }
  deviceLoading.value = true;
  try {
    const res = await listFlightDevice({ tenantId, pageNum: 1, pageSize: 1000 });
    deviceList.value = res.data || res.rows || [];
  } catch (error) {
    console.error('加载设备列表失败:', error);
    deviceList.value = [];
  } finally {
    deviceLoading.value = false;
  }
};

const reset = () => {
  form.value = { ...initFormData };
  flightDeviceSharePermissionFormRef.value?.resetFields();
};

const handleClose = () => {
  reset();
  dialog.visible = false;
  emit('close');
};

const submitForm = () => {
  flightDeviceSharePermissionFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      try {
        if (form.value.id) {
          await updateFlightDeviceSharePermission(form.value);
        } else {
          await addFlightDeviceSharePermission(form.value);
        }
        proxy?.$modal.msgSuccess('操作成功');
        handleClose();
        emit('success');
      } finally {
        buttonLoading.value = false;
      }
    }
  });
};

watch(() => form.value.ownerTenantId, (val) => {
  if (val) {
    getDeviceList(val);
    // 只有在非编辑模式下才清空设备ID
    if (!props.editId) {
      form.value.deviceId = undefined;
    }
  } else {
    deviceList.value = [];
    form.value.deviceId = undefined;
  }
});

watch(() => props.visible, async (val) => {
  if (val) {
    await getTenantList();
    
    if (props.editId) {
      // 编辑模式
      const res = await getFlightDeviceSharePermission(props.editId);
      const editData = res.data;
      Object.assign(form.value, editData);
      dialog.title = '修改飞行设备授权共享';
      
      if (form.value.ownerTenantId) {
        // 保存当前的设备ID
        const currentDeviceId = form.value.deviceId;
        await getDeviceList(form.value.ownerTenantId);
        // 设备列表加载完成后，恢复设备ID
        form.value.deviceId = currentDeviceId;
      }
    } else {
      // 新增模式
      reset();
      dialog.title = '添加飞行设备授权共享';
      
      // 预设参数
      if (props.presetOwnerTenantId) {
        form.value.ownerTenantId = props.presetOwnerTenantId;
        await getDeviceList(props.presetOwnerTenantId);
      }
      if (props.presetDeviceId) {
        form.value.deviceId = props.presetDeviceId;
      }
      if (props.presetTargetTenantId) {
        form.value.targetTenantId = props.presetTargetTenantId;
      }
    }
    
    dialog.visible = true;
  } else {
    dialog.visible = false;
  }
});
</script> 