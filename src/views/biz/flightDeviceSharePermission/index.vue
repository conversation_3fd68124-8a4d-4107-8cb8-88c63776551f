<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="资源拥有者租户" prop="ownerTenantId">
              <el-select v-model="queryParams.ownerTenantId" placeholder="请选择资源拥有者租户" clearable filterable>
                <el-option v-for="tenant in tenantList" :key="tenant.tenantId" :label="tenant.companyName" :value="tenant.tenantId" />
              </el-select>
            </el-form-item>
            <el-form-item label="资源被授权方租户" prop="targetTenantId">
              <el-select v-model="queryParams.targetTenantId" placeholder="请选择资源被授权方租户" clearable filterable>
                <el-option v-for="tenant in tenantList" :key="tenant.tenantId" :label="tenant.companyName" :value="tenant.tenantId" />
              </el-select>
            </el-form-item>
            <el-form-item label="设备ID" prop="deviceId">
              <el-input v-model="queryParams.deviceId" placeholder="请输入设备ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['biz:flightDeviceSharePermission:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['biz:flightDeviceSharePermission:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['biz:flightDeviceSharePermission:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['biz:flightDeviceSharePermission:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="flightDeviceSharePermissionList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="主键ID" align="center" prop="id" v-if="true" />
        <el-table-column label="资源拥有者租户" align="center" prop="ownerTenantId">
          <template #default="scope">
            {{ tenantList.find((t) => t.tenantId == scope.row.ownerTenantId)?.companyName || scope.row.ownerTenantId }}
          </template>
        </el-table-column>
        <el-table-column label="资源被授权方租户" align="center" prop="targetTenantId">
          <template #default="scope">
            {{ tenantList.find((t) => t.tenantId == scope.row.targetTenantId)?.companyName || scope.row.targetTenantId }}
          </template>
        </el-table-column>
        <el-table-column label="设备" align="center" prop="deviceId">
          <template #default="scope">
            {{ deviceList.find((device) => device.id == scope.row.deviceId)?.deviceName || scope.row.deviceId }}
          </template>
        </el-table-column>
        <el-table-column label="授权状态" align="center" prop="status">
          <template #default="scope">
            <dict-tag :options="biz_flight_device_share_permission_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="备注信息" align="center" prop="remark" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['biz:flightDeviceSharePermission:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['biz:flightDeviceSharePermission:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改飞行设备授权共享对话框 -->
    <FlightDeviceSharePermissionEdit
      :visible="editDialogVisible"
      :edit-id="editId"
      :preset-owner-tenant-id="userStore.tenantId"
      @success="handleEditSuccess"
      @close="handleEditClose"
    />
  </div>
</template>

<script setup name="FlightDeviceSharePermission" lang="ts">
import { listFlightDeviceSharePermission, getFlightDeviceSharePermission, delFlightDeviceSharePermission, addFlightDeviceSharePermission, updateFlightDeviceSharePermission } from '@/api/biz/flightDeviceSharePermission';
import { listTenant } from '@/api/system/tenant';
import {
  FlightDeviceSharePermissionVO,
  FlightDeviceSharePermissionQuery,
  FlightDeviceSharePermissionForm
} from '@/api/biz/flightDeviceSharePermission/types';
import { listFlightDevice } from '@/api/biz/flightDevice';
import FlightDeviceSharePermissionEdit from './edit.vue';
import { useUserStore } from '@/store/modules/user';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const userStore = useUserStore();
const { biz_flight_device_share_permission_status } = toRefs<any>(proxy?.useDict('biz_flight_device_share_permission_status'));

const flightDeviceSharePermissionList = ref<FlightDeviceSharePermissionVO[]>([]);
const loading = ref(true);
const showSearch = ref(false);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

// 添加缺失的变量
const tenantList = ref<any[]>([]);
const deviceList = ref<any[]>([]);

const queryFormRef = ref<ElFormInstance>();

const editDialogVisible = ref(false);
const editId = ref<string | number | undefined>(undefined);

const data = reactive<PageData<FlightDeviceSharePermissionForm, FlightDeviceSharePermissionQuery>>({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    ownerTenantId: undefined,
    targetTenantId: undefined,
    deviceId: undefined,
    permissionType: undefined,
    status: undefined,
    startTime: undefined,
    endTime: undefined,
    params: {}
    }
});

const { queryParams } = toRefs(data);

// 获取租户列表
const getTenantList = async () => {
  const res = await listTenant({
    tenantId: '',
    contactUserName: '',
    contactPhone: '',
    companyName: '',
    pageNum: 1,
    pageSize: 1000
  });
  tenantList.value = res.data || res.rows || [];
};

// 获取设备列表（用于显示设备名称）
const getDeviceList = async () => {
  const res = await listFlightDevice({ pageNum: 1, pageSize: 1000 });
  deviceList.value = res.data || res.rows || [];
};

/** 查询飞行设备授权共享列表 */
const getList = async () => {
  loading.value = true;
  const res = await listFlightDeviceSharePermission(queryParams.value);
  flightDeviceSharePermissionList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: FlightDeviceSharePermissionVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  editId.value = undefined;
  editDialogVisible.value = true;
}

/** 修改按钮操作 */
const handleUpdate = async (row?: FlightDeviceSharePermissionVO) => {
  editId.value = row?.id || ids.value[0];
  editDialogVisible.value = true;
}

const handleEditSuccess = () => {
  editDialogVisible.value = false;
  getList();
}

const handleEditClose = () => {
  editDialogVisible.value = false;
}

/** 删除按钮操作 */
const handleDelete = async (row?: FlightDeviceSharePermissionVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除飞行设备授权共享编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delFlightDeviceSharePermission(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('biz/flightDeviceSharePermission/export', {
    ...queryParams.value
  }, `flightDeviceSharePermission_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getTenantList();
  getDeviceList();
  getList();
});
</script>
