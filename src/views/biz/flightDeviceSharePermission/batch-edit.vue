<template>
  <el-dialog
    title="批量授权设备"
    v-model="dialogVisible"
    width="500px"
    :close-on-click-modal="false"
    append-to-body
    @close="handleClose"
  >
    <el-form ref="formRef" :model="form" label-width="120px">
      <el-form-item label="授权租户">
        <div class="border rounded p-4 bg-gray-50 tenant-list">
          <el-checkbox-group v-model="form.targetTenantIds">
            <div v-for="tenant in tenantList" :key="tenant.tenantId" class="mb-2 flex items-center">
              <el-checkbox 
                :label="tenant.tenantId"
                :disabled="tenant.tenantId === userStore.tenantId"
              >
                {{ tenant.companyName }}
                <el-tag 
                  v-if="getExistingPermission(tenant.tenantId)" 
                  size="small" 
                  class="ml-2"
                  :type="getExistingPermission(tenant.tenantId)?.status === '1' ? 'success' : 'warning'"
                >
                  当前{{ getExistingPermission(tenant.tenantId)?.status === '1' ? '已授权' : '已禁用' }}
                </el-tag>
                <el-tag 
                  v-if="tenant.tenantId === userStore.tenantId" 
                  size="small" 
                  class="ml-2"
                  type="info"
                >
                  当前租户
                </el-tag>
              </el-checkbox>
            </div>
          </el-checkbox-group>
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm" :loading="loading">确 定</el-button>
        <el-button @click="handleClose">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { listTenant } from '@/api/system/tenant';
import { listDeviceSharePermissions } from '@/api/biz/flightDeviceSharePermission';
import type { FlightDeviceSharePermissionVO } from '@/api/biz/flightDeviceSharePermission/types';
import { useUserStore } from '@/store/modules/user';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  deviceId: {
    type: [String, Number],
    required: true
  }
});

const emit = defineEmits(['success', 'close']);

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const userStore = useUserStore();

const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => {
    if (!val) emit('close');
  }
});

const loading = ref(false);
const tenantList = ref<any[]>([]);
const existingPermissions = ref<FlightDeviceSharePermissionVO[]>([]);

const formRef = ref<ElFormInstance>();
const form = reactive({
  targetTenantIds: [] as string[]
});

/** 获取租户的现有授权信息 */
const getExistingPermission = (tenantId: string) => {
  return existingPermissions.value.find((p) => p.targetTenantId === tenantId);
};

/** 获取租户列表 */
const getTenantList = async () => {
  const res = await listTenant({
    tenantId: '',
    contactUserName: '',
    contactPhone: '',
    companyName: '',
    pageNum: 1,
    pageSize: 1000
  });
  tenantList.value = res.data || res.rows || [];
};

/** 获取设备已有的授权信息 */
const getExistingPermissions = async () => {
  try {
    const res = await listDeviceSharePermissions(props.deviceId);
    existingPermissions.value = res.data || res.rows || [];
    // 只选中有效的授权，不包含当前租户
    form.targetTenantIds = existingPermissions.value
      .filter((p) => p.status === '1' && p.targetTenantId !== userStore.tenantId)
      .map((p) => p.targetTenantId);
  } catch (error) {
    console.error('获取已有授权信息失败:', error);
  }
};

/** 提交按钮 */
const submitForm = async () => {
  loading.value = true;
  try {
    emit('success', {
      targetTenantIds: form.targetTenantIds,
      permissionType: '1', // 默认只读权限
      status: '1' // 默认有效状态
    });
  } finally {
    loading.value = false;
  }
};

/** 关闭弹窗 */
const handleClose = () => {
  formRef.value?.resetFields();
  existingPermissions.value = [];
  dialogVisible.value = false;
};

watch(
  () => props.visible,
  (val) => {
    if (val) {
      getExistingPermissions();
    }
  }
);

onMounted(() => {
  getTenantList();
});
</script>

<style scoped>
.tenant-list {
  max-height: 300px;
  overflow-y: auto;
}
</style> 