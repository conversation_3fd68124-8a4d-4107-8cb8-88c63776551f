<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="任务名称" prop="taskName">
              <el-input v-model="queryParams.taskName" placeholder="请输入任务名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="任务编号" prop="taskNo">
              <el-input v-model="queryParams.taskNo" placeholder="请输入任务编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="任务频率" prop="taskFrequency">
              <el-select v-model="queryParams.taskFrequency" placeholder="请选择任务频率" clearable>
                <el-option v-for="dict in flight_task_frequency" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="航线名称" prop="routeName">
              <el-input v-model="queryParams.routeName" placeholder="请输入执行航线名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="设备名称" prop="executionDeviceName">
              <el-input v-model="queryParams.executionDeviceName" placeholder="请输入执行设备名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="任务状态" prop="taskStatus">
              <el-select v-model="queryParams.taskStatus" placeholder="请选择任务状态" clearable>
                <el-option v-for="dict in flight_task_status" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="开始时间" prop="actualStartTime">
              <el-date-picker
                clearable
                v-model="queryParams.actualStartTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择实际开始时间"
              />
            </el-form-item>
            <el-form-item label="结束时间" prop="actualEndTime">
              <el-date-picker clearable v-model="queryParams.actualEndTime" type="date" value-format="YYYY-MM-DD" placeholder="请选择实际结束时间" />
            </el-form-item>
            <el-form-item label="矢量操作" prop="vectorOperation">
              <el-select v-model="queryParams.vectorOperation" placeholder="请选择矢量操作" clearable>
                <el-option v-for="dict in flight_task_vector_operation" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['biz:flightTaskResult:add']"> 新增 </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['biz:flightTaskResult:export']">导出 </el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="flightTaskResultList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="任务名称" align="center" prop="taskName" />
        <el-table-column label="任务类型" align="center" prop="taskType" />
        <el-table-column label="执行航线名称" align="center" prop="routeName" />
        <el-table-column label="机场名称" align="center" prop="dockName" />
        <el-table-column label="无人机名称" align="center" prop="droneName" />
        <el-table-column label="飞行计划名称" align="center" prop="flightPlanName" />
        <el-table-column label="实际执行时间" align="center" width="320">
          <template #default="scope">
            <div>
              {{ scope.row.actualStartTime ? parseTime(scope.row.actualStartTime, '{y}-{m}-{d} {h}:{i}:{s}') : '-' }}
              ~
              {{ scope.row.actualEndTime ? parseTime(scope.row.actualEndTime, '{y}-{m}-{d} {h}:{i}:{s}') : '-' }}
            </div>
            <div v-if="computeDuration(scope.row.actualStartTime, scope.row.actualEndTime)">
              耗时:
              {{ computeDuration(scope.row.actualStartTime, scope.row.actualEndTime) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="日常巡查结果" align="center">
          <template #default="scope">
            <el-button link type="success" @click="handleViewRoadResult(scope.row)">查看</el-button>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" @click="handleViewReport(scope.row)">巡检报告</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改日常巡检对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="680px" append-to-body>
      <el-form ref="flightTaskResultFormRef" :model="form" :rules="rules" label-width="128px">
        <el-form-item label="任务计划" prop="taskId">
          <el-select v-model="form.taskId" placeholder="请选择任务计划" filterable @change="taskChange">
            <el-option v-for="task in flightTaskInfoList" :key="task.id" :label="task.taskName" :value="task.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="任务名称" prop="taskName">
          <el-input v-model="form.taskName" placeholder="请输入任务名称" />
        </el-form-item>
        <el-form-item label="任务编号" prop="taskNo">
          <el-input v-model="form.taskNo" placeholder="请输入任务编号" />
        </el-form-item>
        <el-form-item label="任务频率" prop="taskFrequency">
          <el-select v-model="form.taskFrequency" placeholder="请选择任务频率">
            <el-option v-for="dict in flight_task_frequency" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="执行航线名称" prop="routeName">
          <el-input v-model="form.routeName" placeholder="请输入执行航线名称" />
        </el-form-item>
        <el-form-item label="执行设备名称" prop="executionDeviceName">
          <el-input v-model="form.executionDeviceName" placeholder="请输入执行设备名称" />
        </el-form-item>
        <el-form-item label="任务状态" prop="taskStatus">
          <el-select v-model="form.taskStatus" placeholder="请选择任务状态" clearable>
            <el-option v-for="dict in flight_task_status" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="实际开始时间" prop="actualStartTime">
          <el-date-picker
            clearable
            v-model="form.actualStartTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择实际开始时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="实际结束时间" prop="actualEndTime">
          <el-date-picker clearable v-model="form.actualEndTime" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择实际结束时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="返航高度" prop="returnAltitude">
          <el-input v-model="form.returnAltitude" placeholder="请输入返航高度" />
        </el-form-item>
        <el-form-item label="矢量操作" prop="vectorOperation">
          <el-select v-model="form.vectorOperation" placeholder="请选择矢量操作">
            <el-option v-for="dict in flight_task_vector_operation" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="notes">
          <el-input v-model="form.notes" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 查看图片弹窗 -->
    <el-dialog title="任务图片列表" v-model="imageDialogVisible" width="70%" append-to-body destroy-on-close>
      <div v-if="imageDialogVisible">
        <el-card shadow="never" class="filter-card">
          <el-form ref="imageQueryFormRef" :model="imageQueryParams" :inline="true" size="small" class="compact-form">
            <el-form-item label="桩号" prop="stakeNo">
              <el-input v-model="imageQueryParams.stakeNo" placeholder="请输入桩号" style="width: 140px" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleImageQuery" size="small">搜索</el-button>
              <el-button icon="Refresh" @click="resetImageQuery" size="small">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <el-card v-if="imageLoading" shadow="never" v-loading="imageLoading" class="image-card">
          <p>加载中...</p>
        </el-card>
        <el-card v-else-if="imageList && imageList.length > 0" shadow="never" class="image-card">
          <el-table :data="imageList" border style="width: 100%" size="small" class="compact-table">
            <el-table-column type="index" label="序号" width="50" align="center" />
            <el-table-column prop="stakeNo" label="桩号" min-width="80" align="center" />
            <el-table-column prop="latitude" label="纬度" min-width="80" align="center" />
            <el-table-column prop="longitude" label="经度" min-width="80" align="center" />
            <el-table-column prop="imageTime" label="图片时间" min-width="120" align="center" />
            <el-table-column label="AI识别结果" min-width="120" align="center">
              <template #default="scope">
                <dict-tag :options="ai_image_tag" :value="scope.row.labelType" />
              </template>
            </el-table-column>
            <el-table-column label="图片" width="60" align="center">
              <template #default="scope">
                <el-image
                  :src="scope.row.imageUrl"
                  fit="cover"
                  style="width: 40px; height: 40px; border-radius: 3px"
                  :initial-index="0"
                  @click="openPreview(scope.row)"
                />
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="imageTotal > 0"
            :total="imageTotal"
            v-model:page="imageQueryParams.pageNum"
            v-model:limit="imageQueryParams.pageSize"
            @pagination="getImageList"
            class="compact-pagination"
          />
        </el-card>
        <el-card v-else shadow="never" class="image-card">
          <p>暂无图片记录。</p>
        </el-card>
      </div>
    </el-dialog>
    <!-- 图片预览弹窗 -->
    <PreviewModal v-model="previewVisible" :image-url="previewImageUrl" :labels="currentLabels" :showResultsArea="showResultsArea" @closed="handlePreviewClose" @confirmStatusChange="handleConfirmStatusChange"/>
    <!-- 日常巡查结果弹窗 -->
    <el-dialog title="日常巡查结果" v-model="roadResultDialogVisible" width="80%" append-to-body destroy-on-close>
      <div v-if="roadResultDialogVisible">
        <el-tabs v-model="roadResultTab" class="filter-card">
          <el-tab-pane label="路面病害" name="disease">
            <el-card shadow="never" class="image-card">
              <el-form ref="diseaseQueryFormRef" :model="diseaseQueryParams" :inline="true" size="small" class="compact-form">
                <el-form-item label="病害类型" prop="diseaseType">
                  <el-input v-model="diseaseQueryParams.diseaseType" placeholder="请输入病害类型" style="width: 140px" />
                </el-form-item>
                <el-form-item label="确认状态" prop="diseaseConfirmed">
                  <el-select v-model="diseaseQueryParams.diseaseConfirmed" placeholder="请选择">
                    <el-option label="全部" value="" />
                    <el-option label="已确认" value="Y" />
                    <el-option label="未确认" value="N" />
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="Search" @click="handleDiseaseQuery" size="small">搜索</el-button>
                  <el-button icon="Refresh" @click="resetDiseaseQuery" size="small">重置</el-button>
                </el-form-item>
              </el-form>
              <el-table :data="diseaseList" border v-loading="diseaseLoading" size="small" class="compact-table" style="margin-top: 4px">
                <el-table-column type="index" label="序号" width="50" align="center" />
                <el-table-column label="病害类型" min-width="100" align="center">
                  <template #default="scope">
                    <dict-tag :options="ai_image_tag" :value="scope.row.diseaseType" />
                  </template>
                </el-table-column>
                <el-table-column prop="latitude" label="纬度" min-width="80" align="center" />
                <el-table-column prop="longitude" label="经度" min-width="80" align="center" />
                <el-table-column prop="pileNo" label="桩号" min-width="80" align="center" />
                <el-table-column prop="imageUrl" label="图片" min-width="80" align="center">
                  <template #default="scope">
                    <el-image
                      :src="scope.row.imageUrl"
                      fit="cover"
                      style="width: 40px; height: 40px; border-radius: 3px"
                      :initial-index="0"
                      @click="openPreview(scope.row,false)"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="是否确认识别结果" width="100" align="center" v-hasPermi="['biz:roadInspectionDiseaseInfo:edit']">
                  <template #default="scope">
                    <span v-if="scope.row.diseaseConfirmed=='1'" style="color: rgba(9,8,8,0.9);">是</span>
                    <span v-else style="color: red;">否</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                  <template #default="scope">
                    <el-button link type="primary" @click="openPreview(scope.row,true)">识别结果确认</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <div class="table-footer">
                <div class="pagination-section">
                  <pagination
                    v-show="diseaseTotal > 0"
                    :total="diseaseTotal"
                    v-model:page="diseaseQueryParams.pageNum"
                    v-model:limit="diseaseQueryParams.pageSize"
                    @pagination="getDiseaseList"
                    class="compact-pagination"
                  />
                </div>
                <div v-if="diseaseStatistics.length > 0" class="statistics-section">
                  <div class="statistics-row">
                    <div class="statistics-item-inline statistics-total">
                      <div class="statistics-label-inline">存在问题/病害</div>
                      <div class="statistics-value-inline">{{ diseaseStatistics.reduce((sum, item) => sum + item.count, 0) }}</div>
                    </div>
                    <div v-for="item in diseaseStatistics" :key="item.diseaseType" class="statistics-item-inline">
                      <div class="statistics-label-inline">
                        <dict-tag :options="ai_image_tag" :value="item.diseaseType" />
                      </div>
                      <div class="statistics-value-inline">{{ item.count }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </el-tab-pane>
          <el-tab-pane label="施工监管" name="supervision">
            <el-card shadow="never" class="image-card">
              <el-form ref="supervisionQueryFormRef" :model="supervisionQueryParams" :inline="true" size="small" class="compact-form">
                <el-form-item label="事件类型" prop="evenType">
                  <el-input v-model="supervisionQueryParams.evenType" placeholder="请输入事件类型" style="width: 140px" />
                </el-form-item>
                <el-form-item label="确认状态" prop="recognitionResultConfirmed">
                  <el-select v-model="supervisionQueryParams.recognitionResultConfirmed" placeholder="请选择">
                    <el-option label="全部" value="" />
                    <el-option label="已确认" value="Y" />
                    <el-option label="未确认" value="N" />
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="Search" @click="handleSupervisionQuery" size="small">搜索</el-button>
                  <el-button icon="Refresh" @click="resetSupervisionQuery" size="small">重置</el-button>
                </el-form-item>
              </el-form>
              <el-table :data="supervisionList" border v-loading="supervisionLoading" size="small" class="compact-table" style="margin-top: 4px">
                <el-table-column type="index" label="序号" width="50" align="center" />
                <el-table-column prop="siteLatitude" label="工地纬度" min-width="80" align="center" />
                <el-table-column prop="siteLongitude" label="工地经度" min-width="80" align="center" />
                <el-table-column label="事件类型" min-width="100" align="center">
                  <template #default="scope">
                    <dict-tag :options="ai_image_tag" :value="scope.row.evenType" />
                  </template>
                </el-table-column>
                <el-table-column prop="pileNo" label="桩号" min-width="80" align="center" />
                <el-table-column prop="imageUrl" label="图片" min-width="80" align="center">
                  <template #default="scope">
                    <el-image
                      :src="scope.row.imageUrl"
                      fit="cover"
                      style="width: 40px; height: 40px; border-radius: 3px"
                      :initial-index="0"
                      @click="openPreview(scope.row,false)"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="是否确认识别结果" width="100" align="center" v-hasPermi="['biz:roadInspectionDiseaseInfo:edit']">
                  <template #default="scope">
                    <span v-if="scope.row.diseaseConfirmed=='1'" style="color: rgba(9,8,8,0.9);">是</span>
                    <span v-else style="color: red;">否</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                  <template #default="scope">
                    <el-button link type="primary" @click="openPreview(scope.row,true)">识别结果确认</el-button>
                  </template>
                </el-table-column>

              </el-table>
              <div class="table-footer">
                <div class="pagination-section">
                  <pagination
                    v-show="supervisionTotal > 0"
                    :total="supervisionTotal"
                    v-model:page="supervisionQueryParams.pageNum"
                    v-model:limit="supervisionQueryParams.pageSize"
                    @pagination="getSupervisionList"
                    class="compact-pagination"
                  />
                </div>
                <div v-if="supervisionStatistics.length > 0" class="statistics-section">
                  <div class="statistics-row">
                    <div class="statistics-item-inline statistics-total">
                      <div class="statistics-label-inline">施工巡查问题</div>
                      <div class="statistics-value-inline">{{ supervisionStatistics.reduce((sum, item) => sum + item.count, 0) }}</div>
                    </div>
                    <div v-for="item in supervisionStatistics" :key="item.evenType" class="statistics-item-inline">
                      <div class="statistics-label-inline">
                        <dict-tag :options="ai_image_tag" :value="item.evenType" />
                      </div>
                      <div class="statistics-value-inline">{{ item.count }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </el-tab-pane>
          <el-tab-pane label="侵占路权" name="occupation">
            <el-card shadow="never" class="image-card">
              <el-form ref="occupationQueryFormRef" :model="occupationQueryParams" :inline="true" size="small" class="compact-form">
                <el-form-item label="侵占类型" prop="occupationType">
                  <el-input v-model="occupationQueryParams.occupationType" placeholder="请输入侵占类型" style="width: 140px" />
                </el-form-item>
                <el-form-item label="确认状态" prop="occupationConfirmed">
                  <el-select v-model="occupationQueryParams.occupationConfirmed" placeholder="请选择">
                    <el-option label="全部" value="" />
                    <el-option label="已确认" value="1" />
                    <el-option label="未确认" value="0" />
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="Search" @click="handleOccupationQuery" size="small">搜索</el-button>
                  <el-button icon="Refresh" @click="resetOccupationQuery" size="small">重置</el-button>
                </el-form-item>
              </el-form>
              <el-table :data="occupationList" border v-loading="occupationLoading" size="small" class="compact-table" style="margin-top: 8px">
                <el-table-column type="index" label="序号" width="50" align="center" />
                <el-table-column label="侵占类型" min-width="100" align="center">
                  <template #default="scope">
                    <dict-tag :options="ai_image_tag" :value="scope.row.occupationType" />
                  </template>
                </el-table-column>
                <el-table-column prop="latitude" label="纬度" min-width="80" align="center" />
                <el-table-column prop="longitude" label="经度" min-width="80" align="center" />
                <el-table-column prop="pileNo" label="桩号" min-width="80" align="center" />
                <el-table-column prop="eventTime" label="发生时间" min-width="120" align="center" />
                <el-table-column prop="roadNum" label="路线编号" min-width="80" align="center" />
                <el-table-column prop="remark" label="备注" min-width="100" align="center" />
                <el-table-column label="识别结果确认" width="100" align="center" fixed="right" v-hasPermi="['biz:roadRightOccupationInfo:edit']">
                  <template #default="scope">
                    <el-switch
                      v-model="scope.row.occupationConfirmed"
                      :active-value="'1'"
                      :inactive-value="'0'"
                      inline-prompt
                      active-text="是"
                      inactive-text="否"
                      @change="(val) => handleOccupationConfirmedChange(scope.row, val)"
                    />
                  </template>
                </el-table-column>
                <el-table-column prop="imageUrl" label="图片" min-width="80" align="center">
                  <template #default="scope">
                    <el-image
                      :src="scope.row.imageUrl"
                      fit="cover"
                      style="width: 40px; height: 40px; border-radius: 3px"
                      :initial-index="0"
                      @click="openPreview(scope.row)"
                    />
                  </template>
                </el-table-column>
              </el-table>
              <div class="table-footer">
                <div class="pagination-section">
                  <pagination
                    v-show="occupationTotal > 0"
                    :total="occupationTotal"
                    v-model:page="occupationQueryParams.pageNum"
                    v-model:limit="occupationQueryParams.pageSize"
                    @pagination="getOccupationList"
                    class="compact-pagination"
                  />
                </div>
                <div v-if="occupationStatistics.length > 0" class="statistics-section">
                  <div class="statistics-row">
                    <div class="statistics-item-inline statistics-total">
                      <div class="statistics-label-inline">侵占路权问题</div>
                      <div class="statistics-value-inline">{{ occupationStatistics.reduce((sum, item) => sum + item.count, 0) }}</div>
                    </div>
                    <div v-for="item in occupationStatistics" :key="item.occupationType" class="statistics-item-inline">
                      <div class="statistics-label-inline">
                        <dict-tag :options="ai_image_tag" :value="item.occupationType" />
                      </div>
                      <div class="statistics-value-inline">{{ item.count }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </el-tab-pane>
          <el-tab-pane label="抛洒物" name="throw">
            <el-card shadow="never" class="image-card">
              <el-form ref="throwQueryFormRef" :model="throwQueryParams" :inline="true" size="small" class="compact-form">
                <el-form-item label="抛洒物类型" prop="throwType" label-width="90px">
                  <el-input v-model="throwQueryParams.throwType" placeholder="请输入抛洒物类型" style="width: 140px" />
                </el-form-item>
                <el-form-item label="确认状态" prop="throwConfirmed">
                  <el-select v-model="throwQueryParams.throwConfirmed" placeholder="请选择">
                    <el-option label="全部" value="" />
                    <el-option label="已确认" value="1" />
                    <el-option label="未确认" value="0" />
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="Search" @click="handleThrowQuery" size="small">搜索</el-button>
                  <el-button icon="Refresh" @click="resetThrowQuery" size="small">重置</el-button>
                </el-form-item>
              </el-form>
              <el-table :data="throwList" border v-loading="throwLoading" size="small" class="compact-table" style="margin-top: 8px">
                <el-table-column type="index" label="序号" width="50" align="center" />
                <el-table-column label="抛洒物类型" min-width="140" align="center" label-class-name="nowrap-header">
                  <template #default="scope">
                    <dict-tag :options="ai_image_tag" :value="scope.row.throwType" />
                  </template>
                </el-table-column>
                <el-table-column prop="latitude" label="纬度" min-width="80" align="center" />
                <el-table-column prop="longitude" label="经度" min-width="80" align="center" />
                <el-table-column prop="pileNo" label="桩号" min-width="80" align="center" />
                <el-table-column prop="eventTime" label="发生时间" min-width="120" align="center" />
                <el-table-column prop="roadNum" label="路线编号" min-width="80" align="center" />
                <el-table-column prop="remark" label="备注" min-width="100" align="center" />
                <el-table-column label="识别结果确认" width="100" align="center" fixed="right" v-hasPermi="['biz:roadThrowObjectInfo:edit']">
                  <template #default="scope">
                    <el-switch
                      v-model="scope.row.throwConfirmed"
                      :active-value="'1'"
                      :inactive-value="'0'"
                      inline-prompt
                      active-text="是"
                      inactive-text="否"
                      @change="(val) => handleThrowConfirmedChange(scope.row, val)"
                    />
                  </template>
                </el-table-column>
                <el-table-column prop="imageUrl" label="图片" min-width="80" align="center">
                  <template #default="scope">
                    <el-image
                      :src="scope.row.imageUrl"
                      fit="cover"
                      style="width: 40px; height: 40px; border-radius: 3px"
                      :initial-index="0"
                      @click="openPreview(scope.row)"
                    />
                  </template>
                </el-table-column>
              </el-table>
              <div class="table-footer">
                <div class="pagination-section">
                  <pagination
                    v-show="throwTotal > 0"
                    :total="throwTotal"
                    v-model:page="throwQueryParams.pageNum"
                    v-model:limit="throwQueryParams.pageSize"
                    @pagination="getThrowList"
                    class="compact-pagination"
                  />
                </div>
                <div v-if="throwStatistics.length > 0" class="statistics-section">
                  <div class="statistics-row">
                    <div class="statistics-item-inline statistics-total">
                      <div class="statistics-label-inline">抛洒物问题</div>
                      <div class="statistics-value-inline">{{ throwStatistics.reduce((sum, item) => sum + item.count, 0) }}</div>
                    </div>
                    <div v-for="item in throwStatistics" :key="item.throwType" class="statistics-item-inline">
                      <div class="statistics-label-inline">
                        <dict-tag :options="ai_image_tag" :value="item.throwType" />
                      </div>
                      <div class="statistics-value-inline">{{ item.count }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </el-tab-pane>
          <el-tab-pane label="道路灾害" name="slope">
            <el-card shadow="never" class="image-card">
              <el-form ref="slopeQueryFormRef" :model="slopeQueryParams" :inline="true" size="small" class="compact-form">
                <el-form-item label="项目名称" prop="projectName">
                  <el-input v-model="slopeQueryParams.projectName" placeholder="请输入项目名称" style="width: 140px" />
                </el-form-item>
                <el-form-item label="是否确认" prop="isConfirmed">
                  <el-select v-model="slopeQueryParams.isConfirmed" placeholder="请选择">
                    <el-option label="全部" value="" />
                    <el-option label="已确认" value="1" />
                    <el-option label="未确认" value="0" />
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="Search" @click="handleSlopeQuery" size="small">搜索</el-button>
                  <el-button icon="Refresh" @click="resetSlopeQuery" size="small">重置</el-button>
                </el-form-item>
              </el-form>
              <el-table :data="slopeList" border v-loading="slopeLoading" size="small" class="compact-table" style="margin-top: 8px">
                <el-table-column type="index" label="序号" width="50" align="center" />
                <el-table-column prop="eventTime" label="事件时间" min-width="120" align="center" />
                <el-table-column label="事件类型" min-width="100" align="center">
                  <template #default="scope">
                    <dict-tag :options="ai_image_tag" :value="scope.row.evenType" />
                  </template>
                </el-table-column>
                <el-table-column prop="projectName" label="项目名称" min-width="100" align="center" />
                <el-table-column prop="pileNo" label="桩号" min-width="80" align="center" />
                <el-table-column prop="latitude" label="纬度" min-width="80" align="center" />
                <el-table-column prop="longitude" label="经度" min-width="80" align="center" />
                <el-table-column label="识别结果确认" width="100" align="center" fixed="right" v-hasPermi="['biz:roadEmergencyEvenInfo:edit']">
                  <template #default="scope">
                    <el-switch
                      v-model="scope.row.isConfirmed"
                      :active-value="'1'"
                      :inactive-value="'0'"
                      inline-prompt
                      active-text="是"
                      inactive-text="否"
                      @change="(val) => handleSlopeConfirmedChange(scope.row, val)"
                    />
                  </template>
                </el-table-column>
                <el-table-column prop="imageUrl" label="图片" min-width="80" align="center">
                  <template #default="scope">
                    <el-image :src="scope.row.imageUrl" fit="cover" style="width: 40px; height: 40px; border-radius: 3px" :initial-index="0" @click="openPreview(scope.row)" />
                  </template>
                </el-table-column>
              </el-table>
              <div class="table-footer">
                <div class="pagination-section">
                  <pagination
                    v-show="slopeTotal > 0"
                    :total="slopeTotal"
                    v-model:page="slopeQueryParams.pageNum"
                    v-model:limit="slopeQueryParams.pageSize"
                    @pagination="getSlopeList"
                    class="compact-pagination"
                  />
                </div>
                <div v-if="slopeStatistics.length > 0" class="statistics-section">
                  <div class="statistics-row">
                    <div class="statistics-item-inline statistics-total">
                      <div class="statistics-label-inline">道路灾害事件</div>
                      <div class="statistics-value-inline">{{ slopeStatistics.reduce((sum, item) => sum + item.count, 0) }}</div>
                    </div>
                    <div v-for="item in slopeStatistics" :key="item.evenType" class="statistics-item-inline">
                      <div class="statistics-label-inline">
                        <dict-tag :options="ai_image_tag" :value="item.evenType" />
                      </div>
                      <div class="statistics-value-inline">{{ item.count }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </el-tab-pane>
          <el-tab-pane label="交通事件" name="event">
            <el-card shadow="never" class="image-card">
              <el-form ref="eventQueryFormRef" :model="eventQueryParams" :inline="true" size="small" class="compact-form">
                <el-form-item label="项目名称" prop="projectName">
                  <el-input v-model="eventQueryParams.projectName" placeholder="请输入项目名称" style="width: 140px" />
                </el-form-item>
                <el-form-item label="是否确认" prop="isConfirmed">
                  <el-select v-model="eventQueryParams.isConfirmed" placeholder="请选择">
                    <el-option label="全部" value="" />
                    <el-option label="已确认" value="1" />
                    <el-option label="未确认" value="0" />
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="Search" @click="handleEventQuery" size="small">搜索</el-button>
                  <el-button icon="Refresh" @click="resetEventQuery" size="small">重置</el-button>
                </el-form-item>
              </el-form>
              <el-table :data="eventList" border v-loading="eventLoading" size="small" class="compact-table" style="margin-top: 8px;">
                <el-table-column type="index" label="序号" width="50" align="center" />
                <el-table-column prop="eventTime" label="事故时间" min-width="120" align="center" />
                <el-table-column label="事件类型" min-width="100" align="center">
                  <template #default="scope">
                    <dict-tag :options="ai_image_tag" :value="scope.row.evenType" />
                  </template>
                </el-table-column>
                <el-table-column prop="projectName" label="项目名称" min-width="100" align="center" />
                <el-table-column prop="pileNo" label="桩号" min-width="80" align="center" />
                <el-table-column prop="latitude" label="纬度" min-width="80" align="center" />
                <el-table-column prop="longitude" label="经度" min-width="80" align="center" />
                <el-table-column label="识别结果确认" width="100" align="center" fixed="right" v-hasPermi="['biz:roadAccidentInfo:edit']">
                  <template #default="scope">
                    <el-switch
                      v-model="scope.row.isConfirmed"
                      :active-value="'1'"
                      :inactive-value="'0'"
                      inline-prompt
                      active-text="是"
                      inactive-text="否"
                      @change="(val) => handleEventConfirmedChange(scope.row, val)"
                    />
                  </template>
                </el-table-column>
                <el-table-column prop="imageUrl" label="图片" min-width="80" align="center">
                  <template #default="scope">
                    <el-image :src="scope.row.imageUrl" fit="cover" style="width: 40px; height: 40px; border-radius: 3px" :initial-index="0" @click="openPreview(scope.row)" />
                  </template>
                </el-table-column>
              </el-table>
              <div class="table-footer">
                <div class="pagination-section">
                  <pagination
                    v-show="eventTotal > 0"
                    :total="eventTotal"
                    v-model:page="eventQueryParams.pageNum"
                    v-model:limit="eventQueryParams.pageSize"
                    @pagination="getEventList"
                    class="compact-pagination"
                  />
                </div>
                <div v-if="eventStatistics.length > 0" class="statistics-section">
                  <div class="statistics-row">
                    <div class="statistics-item-inline statistics-total">
                      <div class="statistics-label-inline">交通事件</div>
                      <div class="statistics-value-inline">{{ eventStatistics.reduce((sum, item) => sum + item.count, 0) }}</div>
                    </div>
                    <div v-for="item in eventStatistics" :key="item.evenType" class="statistics-item-inline">
                      <div class="statistics-label-inline">
                        <dict-tag :options="ai_image_tag" :value="item.evenType" />
                      </div>
                      <div class="statistics-value-inline">{{ item.count }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>
    <!-- BaskReport报表预览弹窗 -->
    <el-dialog
      title="巡检报告"
      v-model="reportDialogVisible"
      width="900px"
      append-to-body
      destroy-on-close
      :close-on-click-modal="false"
      draggable
      align-center
    >
      <BaskReportPreview
        v-if="reportDialogVisible"
        :reportCode="'jx_jt_dailyInspection_report_prod'"
        :parameters="reportParameters"
        :showToolbar="true"
        :height="'700px'"
        :fileName="'日常巡检报告'"
      />
    </el-dialog>
  </div>
</template>

<script setup name="DailyInspection" lang="ts">
import { Loading } from '@element-plus/icons-vue';
import {
  listFlightTaskResult,
  getFlightTaskResult,
  delFlightTaskResult,
  addFlightTaskResult,
  updateFlightTaskResult
} from '@/api/biz/flightTaskResult';
import { FlightTaskResultVO, FlightTaskResultQuery, FlightTaskResultForm } from '@/api/biz/flightTaskResult/types';
import { FlightDeviceVO } from '@/api/biz/flightDevice/types';
import { FlightRouteInfoVO } from '@/api/biz/flightRouteInfo/types';
import { listFlightTaskInfo, getFlightTaskInfo } from '@/api/biz/flightTaskInfo';
import { FlightTaskInfoVO } from '@/api/biz/flightTaskInfo/types';
import { listFlightDevice, getFlightDevice } from '@/api/biz/flightDevice';
import { listFlightRouteInfo, getFlightRouteInfo } from '@/api/biz/flightRouteInfo';
import PreviewModal from '@/components/PreviewModal/index.vue';
import { useImageLabels } from '@/hooks/useImageLabels';
// BaskReport组件导入
import BaskReportPreview from '@/components/BaskReportPreview/index.vue';
import { listRoadInspectionDiseaseInfo, getDiseaseStatistics, updateRoadInspectionDiseaseInfo } from '@/api/biz/roadInspectionDiseaseInfo';
import {
  RoadInspectionDiseaseInfoVO,
  RoadInspectionDiseaseInfoQuery,
  BizRoadInspectionDiseaseStatisticsVo,
  RoadInspectionDiseaseInfoForm
} from '@/api/biz/roadInspectionDiseaseInfo/types';
import { listRoadInspectionSupervisionRecord, getSupervisionStatistics, updateRoadInspectionSupervisionRecord } from '@/api/biz/roadInspectionSupervisionRecord';
import {
  RoadInspectionSupervisionRecordVO,
  RoadInspectionSupervisionRecordQuery,
  BizRoadInspectionSupervisionStatisticsVo,
  RoadInspectionSupervisionRecordForm
} from '@/api/biz/roadInspectionSupervisionRecord/types';
import { listRoadAccidentInfo, getAccidentStatistics, updateRoadAccidentInfo } from '@/api/biz/roadAccidentInfo';
import { RoadAccidentInfoVO, RoadAccidentInfoQuery, BizRoadAccidentStatisticsVo, RoadAccidentInfoForm } from '@/api/biz/roadAccidentInfo/types';
import { listRoadRightOccupationInfo, getOccupationStatistics, updateRoadRightOccupationInfo } from '@/api/biz/roadRightOccupationInfo';
import { RoadRightOccupationInfoVO, RoadRightOccupationInfoQuery, BizRoadRightOccupationStatisticsVo, RoadRightOccupationInfoForm } from '@/api/biz/roadRightOccupationInfo/types';
import { listRoadThrowObjectInfo, getThrowObjectStatistics, updateRoadThrowObjectInfo } from '@/api/biz/roadThrowObjectInfo';
import { RoadThrowObjectInfoVO, RoadThrowObjectInfoQuery, BizRoadThrowObjectStatisticsVo, RoadThrowObjectInfoForm } from '@/api/biz/roadThrowObjectInfo/types';
import { listRoadEmergencyEvenInfo, getEmergencyEvenStatistics, updateRoadEmergencyEvenInfo } from '@/api/biz/roadEmergencyEvenInfo';
import { RoadEmergencyEvenInfoVO, RoadEmergencyEvenInfoQuery, BizRoadEmergencyEvenStatisticsVo, RoadEmergencyEvenInfoForm } from '@/api/biz/roadEmergencyEvenInfo/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { flight_task_type, flight_task_frequency, flight_task_status, flight_task_vector_operation, ai_image_tag } = toRefs<any>(
  proxy?.useDict('flight_task_type', 'flight_task_frequency', 'flight_task_status', 'flight_task_vector_operation', 'ai_image_tag')
);

// 使用图片标签 Hook
const { currentLabels, labelLoading, loadLabels } = useImageLabels();

// 初始化标志，防止在数据加载期间触发change事件
const isInitializing = ref(true);

const flightTaskResultList = ref<FlightTaskResultVO[]>([]);
const flightDeviceList = ref<FlightDeviceVO[]>([]);
const flightRouteInfoList = ref<FlightRouteInfoVO[]>([]);
const flightTaskInfoList = ref<FlightTaskInfoVO[]>([]);

const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(false);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const flightTaskResultFormRef = ref<ElFormInstance>();
const previewModalRef = ref(null);
const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: FlightTaskResultForm = {
  id: undefined,
  taskId: undefined,
  taskName: undefined,
  taskNo: undefined,
  taskType: '任务飞行',
  taskFrequency: undefined,
  plannedStartTime: undefined,
  plannedEndTime: undefined,
  dailyScheduledStartTime: undefined,
  dailyScheduledEndTime: undefined,
  batteryCapacityReached: undefined,
  storageCapacityReached: undefined,
  routeId: undefined,
  routeName: undefined,
  executionDeviceId: undefined,
  executionDeviceName: undefined,
  executor: undefined,
  estimatedPayload: undefined,
  notes: undefined,
  taskStatus: undefined,
  actualStartTime: undefined,
  actualEndTime: undefined,
  returnAltitude: undefined,
  vectorOperation: undefined
};
const data = reactive<PageData<FlightTaskResultForm, FlightTaskResultQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    taskId: undefined,
    taskName: undefined,
    taskNo: undefined,
    taskType: '任务飞行',
    taskFrequency: undefined,
    plannedStartTime: undefined,
    plannedEndTime: undefined,
    dailyScheduledStartTime: undefined,
    dailyScheduledEndTime: undefined,
    batteryCapacityReached: undefined,
    storageCapacityReached: undefined,
    routeId: undefined,
    routeName: undefined,
    executionDeviceId: undefined,
    executionDeviceName: undefined,
    executor: undefined,
    estimatedPayload: undefined,
    notes: undefined,
    taskStatus: undefined,
    actualStartTime: undefined,
    actualEndTime: undefined,
    returnAltitude: undefined,
    vectorOperation: undefined,
    params: {}
  },
  rules: {
    id: [{ required: true, message: '任务执行结果ID，主键不能为空', trigger: 'blur' }],
    taskName: [{ required: true, message: '任务名称不能为空', trigger: 'blur' }],
    taskNo: [{ required: true, message: '任务编号不能为空', trigger: 'blur' }],
    taskType: [{ required: true, message: '任务类型不能为空', trigger: 'change' }],
    taskFrequency: [{ required: true, message: '任务频率不能为空', trigger: 'change' }],
    taskStatus: [{ required: true, message: '任务状态不能为空', trigger: 'change' }],
    plannedStartTime: [{ required: true, message: '任务计划开始时间不能为空', trigger: 'blur' }],
    plannedEndTime: [{ required: true, message: '任务计划结束时间不能为空', trigger: 'blur' }],
    actualStartTime: [{ required: true, message: '实际开始时间不能为空', trigger: 'blur' }],
    actualEndTime: [{ required: true, message: '实际结束时间不能为空', trigger: 'blur' }],
    routeId: [{ required: true, message: '执行航线id不能为空', trigger: 'blur' }],
    executionDeviceId: [{ required: true, message: '执行设备id不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询日常巡检记录列表 */
const getList = async () => {
  loading.value = true;
  // 强制带上taskType
  queryParams.value.taskType = '任务飞行';
  const res = await listFlightTaskResult(queryParams.value);
  flightTaskResultList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

const getDeviceList = async () => {
  const res = await listFlightDevice();
  flightDeviceList.value = res.rows;
};

const getRouteList = async () => {
  const res = await listFlightRouteInfo();
  flightRouteInfoList.value = res.rows;
};

const getTaskInfoList = async () => {
  const res = await listFlightTaskInfo();
  flightTaskInfoList.value = res.rows;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  flightTaskResultFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  queryParams.value.taskType = '任务飞行';
  getList();
};

const taskChange = async (value: string) => {
  // 可根据需要补充逻辑
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: FlightTaskResultVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  getTaskInfoList();
  reset();
  form.value.taskType = '任务飞行';
  dialog.visible = true;
  dialog.title = '添加日常巡检记录';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: FlightTaskResultVO) => {
  getTaskInfoList();
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getFlightTaskResult(_id);
  Object.assign(form.value, res.data);
  form.value.taskType = '任务飞行';
  dialog.visible = true;
  dialog.title = '修改日常巡检记录';
};

/** 提交按钮 */
const submitForm = () => {
  form.value.taskType = '任务飞行';
  flightTaskResultFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateFlightTaskResult(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addFlightTaskResult(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: FlightTaskResultVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除日常巡检记录编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delFlightTaskResult(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'biz/flightTaskResult/export',
    {
      ...queryParams.value,
      taskType: '任务飞行'
    },
    `dailyInspection_${new Date().getTime()}.xlsx`
  );
};

const handleViewImages = (row: FlightTaskResultVO) => {
  currentTaskId.value = row.id;
  imageQueryParams.taskId = row.id;
  imageDialogVisible.value = true;
  getImageList();
};

const handleViewVideo = (row: FlightTaskResultVO) => {
  proxy?.$modal.msgWarning('功能开发中...');
};

/**
 * 计算两个时间的耗时，返回 "xxminyy s" 格式，如果时间为空则返回空字符串
 */
const computeDuration = (start?: string, end?: string): string => {
  if (!start || !end) return '';
  const startTime = new Date(start.replace(' ', 'T')).getTime();
  const endTime = new Date(end.replace(' ', 'T')).getTime();
  if (isNaN(startTime) || isNaN(endTime) || endTime <= startTime) return '';
  const diffSeconds = Math.floor((endTime - startTime) / 1000);
  const minutes = Math.floor(diffSeconds / 60);
  const seconds = diffSeconds % 60;
  return `${minutes}min${seconds}s`;
};

// 图片查看相关
const imageDialogVisible = ref(false);
const imageLoading = ref(false);
const imageList = ref<any[]>([]);
const imageTotal = ref(0);
const currentTaskId = ref<string | number | null>(null);
const imageQueryFormRef = ref<ElFormInstance>();

const imageQueryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  taskId: undefined,
  stakeNo: '',
  minConfidence: undefined,
  maxConfidence: undefined
});

const getImageList = async () => {
  if (!currentTaskId.value) {
    imageList.value = [];
    imageLoading.value = false;
    return;
  }

  imageLoading.value = true;
  try {
    // TODO: 替换为实际的图片列表接口
    // const res = await listTaskImages(imageQueryParams);
    // 模拟数据
    const mockData = [
      {
        id: 1,
        stakeNo: 'K12+800',
        latitude: '39.9042',
        longitude: '116.4074',
        imageTime: '2024-05-01 10:15:23',
        labelType: 'car,reflective_jacket,helmet',
        imageUrl: 'https://www.dieyi.cn/upload/thumb_src/800_500/1736854311Nc1d5U2g.png'
      },
      {
        id: 2,
        stakeNo: 'K13+200',
        latitude: '39.9142',
        longitude: '116.4174',
        imageTime: '2024-05-01 10:18:45',
        labelType: 'truck,pedestrian',
        imageUrl: 'https://n.sinaimg.cn/spider20250714/352/w708h444/20250714/f5a6-d8856fc974eb3a7698a5581ddef7b57c.jpg'
      },
      {
        id: 3,
        stakeNo: 'K13+600',
        latitude: '39.9242',
        longitude: '116.4274',
        imageTime: '2024-05-01 10:22:10',
        labelType: 'other_clothes,head',
        imageUrl: 'https://www.dieyi.cn/upload/202501/1736907555.jpg'
      }
    ];
    imageList.value = mockData;
    imageTotal.value = mockData.length;
  } catch (error) {
    proxy?.$modal.msgError('获取图片列表失败');
    imageList.value = [];
    imageTotal.value = 0;
  } finally {
    imageLoading.value = false;
  }
};

const handleImageQuery = () => {
  imageQueryParams.pageNum = 1;
  getImageList();
};

const resetImageQuery = () => {
  imageQueryFormRef.value?.resetFields();
  imageQueryParams.pageNum = 1;
  imageQueryParams.stakeNo = '';
  imageQueryParams.minConfidence = undefined;
  imageQueryParams.maxConfidence = undefined;
  handleImageQuery();
};

const previewVisible = ref(false);
const previewImageUrl = ref('');
const showResultsArea = ref(false);

// 添加响应式变量来跟踪当前操作的行数据
const currentPreviewRow = ref<any>(null);
const currentPreviewType = ref<string>(''); // disease 或 supervision


const openPreview = async (row: any, status: boolean) => {
  currentPreviewRow.value = row;
  previewImageUrl.value = row.imageUrl;
  previewVisible.value = true;
  showResultsArea.value = status; // 传递 showResultsArea
  // 根据不同tab设置当前预览类型
  if (roadResultTab.value === 'disease') {
    currentPreviewType.value = 'disease';
  } else if (roadResultTab.value === 'supervision') {
    currentPreviewType.value = 'supervision';
  }
  if (row.imageUrl) {
    // 根据不同数据类型确定标签类型参数
    let labelType = '';
    if (row.labelType) {
      // 图片列表中的数据
      labelType = row.labelType;
    } else if (row.diseaseType) {
      // 路面病害数据
      labelType = row.diseaseType;
    } else if (row.evenType) {
      // 施工监管、道路灾害、交通事件数据
      labelType = row.evenType;
    } else if (row.occupationType) {
      // 侵占路权数据
      labelType = row.occupationType;
    } else if (row.throwType) {
      // 抛洒物数据
      labelType = row.throwType;
    }

    await loadLabels(row, labelType);
  }
};
// 添加处理确认状态变更的方法
const handleConfirmStatusChange = (isConfirmed: boolean) => {
  // 只处理路面病害和施工监管tab的数据
  if (currentPreviewType.value !== 'disease' && currentPreviewType.value !== 'supervision') {
    return;
  }
  if (!currentPreviewRow.value) return;
  const confirmedValue = isConfirmed ? '1' : '0';
  if (currentPreviewType.value === 'disease') {
    // 更新路面病害数据列表中的确认状态
    const index = diseaseList.value.findIndex(item => item.id === currentPreviewRow.value.id);
    if (index !== -1) {
      diseaseList.value[index].diseaseConfirmed = confirmedValue;
    }
  } else if (currentPreviewType.value === 'supervision') {
    // 更新施工监管数据列表中的确认状态
    const index = supervisionList.value.findIndex(item => item.id === currentPreviewRow.value.id);
    if (index !== -1) {
      supervisionList.value[index].diseaseConfirmed = confirmedValue;
    }
  }
};

const handlePreviewClose = () => {
  previewVisible.value = false;
  previewImageUrl.value = '';
};

const closeDialog = () => {
  debugger;
};

const roadResultDialogVisible = ref(false);
const roadResultTab = ref('disease');

// 路面病害tab相关
const diseaseList = ref<RoadInspectionDiseaseInfoVO[]>([]);
const diseaseTotal = ref(0);
const diseaseLoading = ref(false);
const diseaseQueryParams = reactive<RoadInspectionDiseaseInfoQuery>({
  pageNum: 1,
  pageSize: 10,
  taskResultId: undefined
});
const diseaseQueryFormRef = ref<ElFormInstance>();

// 病害统计数据
const diseaseStatistics = ref<BizRoadInspectionDiseaseStatisticsVo[]>([]);
const diseaseStatisticsLoading = ref(false);

const getDiseaseList = async () => {
  if (!currentTaskId.value) return;
  diseaseLoading.value = true;
  isInitializing.value = true; // 开始加载时设置为初始化状态
  diseaseQueryParams.taskResultId = currentTaskId.value;
  try {
    const res = await listRoadInspectionDiseaseInfo(diseaseQueryParams);
    diseaseList.value = res.rows || [];
    diseaseTotal.value = res.total || 0;
  } catch (e) {
    diseaseList.value = [];
    diseaseTotal.value = 0;
  } finally {
    diseaseLoading.value = false;
    // 数据加载完成后，延迟设置初始化标志为false，确保DOM渲染完成
    nextTick(() => {
      isInitializing.value = false;
    });
  }
};

const fetchDiseaseStatistics = async () => {
  if (!currentTaskId.value) return;
  diseaseStatisticsLoading.value = true;
  try {
    const res = await getDiseaseStatistics(currentTaskId.value);
    diseaseStatistics.value = res.data || [];
  } catch (e) {
    console.error('获取病害统计信息失败:', e);
    diseaseStatistics.value = [];
  } finally {
    diseaseStatisticsLoading.value = false;
  }
};

const handleDiseaseQuery = () => {
  diseaseQueryParams.pageNum = 1;
  getDiseaseList();
};
const resetDiseaseQuery = () => {
  diseaseQueryFormRef.value?.resetFields();
  diseaseQueryParams.pageNum = 1;
  getDiseaseList();
};

watch(
  () => roadResultDialogVisible.value && roadResultTab.value === 'disease' && currentTaskId.value,
  (val) => {
    if (val) {
      getDiseaseList();
      fetchDiseaseStatistics();
    }
  }
);

const handleViewRoadResult = (row: FlightTaskResultVO) => {
  currentTaskId.value = row.id;
  roadResultDialogVisible.value = true;
};

// 施工监管tab相关
const supervisionList = ref<RoadInspectionSupervisionRecordVO[]>([]);
const supervisionTotal = ref(0);
const supervisionLoading = ref(false);
const supervisionQueryParams = reactive<RoadInspectionSupervisionRecordQuery>({
  pageNum: 1,
  pageSize: 10,
  taskResultId: undefined
});
const supervisionQueryFormRef = ref<ElFormInstance>();

// 施工监管统计数据
const supervisionStatistics = ref<BizRoadInspectionSupervisionStatisticsVo[]>([]);
const supervisionStatisticsLoading = ref(false);

const getSupervisionList = async () => {
  if (!currentTaskId.value) return;
  supervisionLoading.value = true;
  isInitializing.value = true; // 开始加载时设置为初始化状态
  supervisionQueryParams.taskResultId = currentTaskId.value;
  try {
    const res = await listRoadInspectionSupervisionRecord(supervisionQueryParams);
    supervisionList.value = res.rows || [];
    supervisionTotal.value = res.total || 0;
  } catch (e) {
    supervisionList.value = [];
    supervisionTotal.value = 0;
  } finally {
    supervisionLoading.value = false;
    // 数据加载完成后，延迟设置初始化标志为false，确保DOM渲染完成
    nextTick(() => {
      isInitializing.value = false;
    });
  }
};

const fetchSupervisionStatistics = async () => {
  if (!currentTaskId.value) return;
  supervisionStatisticsLoading.value = true;
  try {
    const res = await getSupervisionStatistics(currentTaskId.value);
    supervisionStatistics.value = res.data || [];
  } catch (e) {
    console.error('获取施工监管统计信息失败:', e);
    supervisionStatistics.value = [];
  } finally {
    supervisionStatisticsLoading.value = false;
  }
};

const handleSupervisionQuery = () => {
  supervisionQueryParams.pageNum = 1;
  getSupervisionList();
};
const resetSupervisionQuery = () => {
  supervisionQueryFormRef.value?.resetFields();
  supervisionQueryParams.pageNum = 1;
  getSupervisionList();
};

watch(
  () => roadResultDialogVisible.value && roadResultTab.value === 'supervision' && currentTaskId.value,
  (val) => {
    if (val) {
      getSupervisionList();
      fetchSupervisionStatistics();
    }
  }
);

// 道路灾害tab相关
const slopeList = ref<RoadEmergencyEvenInfoVO[]>([]);
const slopeTotal = ref(0);
const slopeLoading = ref(false);
const slopeQueryParams = reactive<RoadEmergencyEvenInfoQuery>({
  pageNum: 1,
  pageSize: 10,
  taskResultId: undefined
});
const slopeQueryFormRef = ref<ElFormInstance>();

// 道路灾害统计数据
const slopeStatistics = ref<BizRoadEmergencyEvenStatisticsVo[]>([]);
const slopeStatisticsLoading = ref(false);

const getSlopeList = async () => {
  if (!currentTaskId.value) return;
  slopeLoading.value = true;
  isInitializing.value = true; // 开始加载时设置为初始化状态
  slopeQueryParams.taskResultId = currentTaskId.value;
  try {
    const res = await listRoadEmergencyEvenInfo(slopeQueryParams);
    slopeList.value = res.rows || [];
    slopeTotal.value = res.total || 0;
  } catch (e) {
    slopeList.value = [];
    slopeTotal.value = 0;
  } finally {
    slopeLoading.value = false;
    // 数据加载完成后，延迟设置初始化标志为false，确保DOM渲染完成
    nextTick(() => {
      isInitializing.value = false;
    });
  }
};

const fetchSlopeStatistics = async () => {
  if (!currentTaskId.value) return;
  slopeStatisticsLoading.value = true;
  try {
    const res = await getEmergencyEvenStatistics(currentTaskId.value);
    slopeStatistics.value = res.data || [];
  } catch (e) {
    console.error('获取道路灾害统计信息失败:', e);
    slopeStatistics.value = [];
  } finally {
    slopeStatisticsLoading.value = false;
  }
};

const handleSlopeQuery = () => {
  slopeQueryParams.pageNum = 1;
  getSlopeList();
};
const resetSlopeQuery = () => {
  slopeQueryFormRef.value?.resetFields();
  slopeQueryParams.pageNum = 1;
  getSlopeList();
};

watch(
  () => roadResultDialogVisible.value && roadResultTab.value === 'slope' && currentTaskId.value,
  (val) => {
    if (val) {
      getSlopeList();
      fetchSlopeStatistics();
    }
  }
);

// 交通事件tab相关
const eventList = ref<RoadAccidentInfoVO[]>([]);
const eventTotal = ref(0);
const eventLoading = ref(false);
const eventQueryParams = reactive<RoadAccidentInfoQuery>({
  pageNum: 1,
  pageSize: 10,
  taskResultId: undefined
});
const eventQueryFormRef = ref<ElFormInstance>();

// 交通事故统计数据
const eventStatistics = ref<BizRoadAccidentStatisticsVo[]>([]);
const eventStatisticsLoading = ref(false);

const getEventList = async () => {
  if (!currentTaskId.value) return;
  eventLoading.value = true;
  isInitializing.value = true; // 开始加载时设置为初始化状态
  eventQueryParams.taskResultId = currentTaskId.value;
  try {
    const res = await listRoadAccidentInfo(eventQueryParams);
    eventList.value = res.rows || [];
    eventTotal.value = res.total || 0;
  } catch (e) {
    eventList.value = [];
    eventTotal.value = 0;
  } finally {
    eventLoading.value = false;
    // 数据加载完成后，延迟设置初始化标志为false，确保DOM渲染完成
    nextTick(() => {
      isInitializing.value = false;
    });
  }
};

const fetchEventStatistics = async () => {
  if (!currentTaskId.value) return;
  eventStatisticsLoading.value = true;
  try {
    const res = await getAccidentStatistics(currentTaskId.value);
    eventStatistics.value = res.data || [];
  } catch (e) {
    console.error('获取交通事故统计信息失败:', e);
    eventStatistics.value = [];
  } finally {
    eventStatisticsLoading.value = false;
  }
};

const handleEventQuery = () => {
  eventQueryParams.pageNum = 1;
  getEventList();
};
const resetEventQuery = () => {
  eventQueryFormRef.value?.resetFields();
  eventQueryParams.pageNum = 1;
  getEventList();
};

watch(
  () => roadResultDialogVisible.value && roadResultTab.value === 'event' && currentTaskId.value,
  (val) => {
    if (val) {
      getEventList();
      fetchEventStatistics();
    }
  }
);

// 侵占路权tab相关
const occupationList = ref<RoadRightOccupationInfoVO[]>([]);
const occupationTotal = ref(0);
const occupationLoading = ref(false);
const occupationQueryParams = reactive<RoadRightOccupationInfoQuery>({
  pageNum: 1,
  pageSize: 10,
  taskResultId: undefined
});
const occupationQueryFormRef = ref<ElFormInstance>();

// 侵占路权统计数据
const occupationStatistics = ref<BizRoadRightOccupationStatisticsVo[]>([]);
const occupationStatisticsLoading = ref(false);

const getOccupationList = async () => {
  if (!currentTaskId.value) return;
  occupationLoading.value = true;
  isInitializing.value = true; // 开始加载时设置为初始化状态
  occupationQueryParams.taskResultId = currentTaskId.value;
  try {
    const res = await listRoadRightOccupationInfo(occupationQueryParams);
    occupationList.value = res.rows || [];
    occupationTotal.value = res.total || 0;
  } catch (e) {
    occupationList.value = [];
    occupationTotal.value = 0;
  } finally {
    occupationLoading.value = false;
    // 数据加载完成后，延迟设置初始化标志为false，确保DOM渲染完成
    nextTick(() => {
      isInitializing.value = false;
    });
  }
};

const fetchOccupationStatistics = async () => {
  if (!currentTaskId.value) return;
  occupationStatisticsLoading.value = true;
  try {
    const res = await getOccupationStatistics(currentTaskId.value);
    occupationStatistics.value = res.data || [];
  } catch (e) {
    console.error('获取侵占路权统计信息失败:', e);
    occupationStatistics.value = [];
  } finally {
    occupationStatisticsLoading.value = false;
  }
};

const handleOccupationQuery = () => {
  occupationQueryParams.pageNum = 1;
  getOccupationList();
};
const resetOccupationQuery = () => {
  occupationQueryFormRef.value?.resetFields();
  occupationQueryParams.pageNum = 1;
  getOccupationList();
};

watch(
  () => roadResultDialogVisible.value && roadResultTab.value === 'occupation' && currentTaskId.value,
  (val) => {
    if (val) {
      getOccupationList();
      fetchOccupationStatistics();
    }
  }
);

// 抛洒物tab相关
const throwList = ref<RoadThrowObjectInfoVO[]>([]);
const throwTotal = ref(0);
const throwLoading = ref(false);
const throwQueryParams = reactive<RoadThrowObjectInfoQuery>({
  pageNum: 1,
  pageSize: 10,
  taskResultId: undefined
});
const throwQueryFormRef = ref<ElFormInstance>();

// 抛洒物统计数据
const throwStatistics = ref<BizRoadThrowObjectStatisticsVo[]>([]);
const throwStatisticsLoading = ref(false);

const getThrowList = async () => {
  if (!currentTaskId.value) return;
  throwLoading.value = true;
  isInitializing.value = true; // 开始加载时设置为初始化状态
  throwQueryParams.taskResultId = currentTaskId.value;
  try {
    const res = await listRoadThrowObjectInfo(throwQueryParams);
    throwList.value = res.rows || [];
    throwTotal.value = res.total || 0;
  } catch (e) {
    throwList.value = [];
    throwTotal.value = 0;
  } finally {
    throwLoading.value = false;
    // 数据加载完成后，延迟设置初始化标志为false，确保DOM渲染完成
    nextTick(() => {
      isInitializing.value = false;
    });
  }
};

const fetchThrowStatistics = async () => {
  if (!currentTaskId.value) return;
  throwStatisticsLoading.value = true;
  try {
    const res = await getThrowObjectStatistics(currentTaskId.value);
    throwStatistics.value = res.data || [];
  } catch (e) {
    console.error('获取抛洒物统计信息失败:', e);
    throwStatistics.value = [];
  } finally {
    throwStatisticsLoading.value = false;
  }
};

const handleThrowQuery = () => {
  throwQueryParams.pageNum = 1;
  getThrowList();
};
const resetThrowQuery = () => {
  throwQueryFormRef.value?.resetFields();
  throwQueryParams.pageNum = 1;
  getThrowList();
};

watch(
  () => roadResultDialogVisible.value && roadResultTab.value === 'throw' && currentTaskId.value,
  (val) => {
    if (val) {
      getThrowList();
      fetchThrowStatistics();
    }
  }
);

// BaskReport相关响应式变量
const reportDialogVisible = ref(false);
const reportParameters = ref({});

const handleViewReport = async (row: FlightTaskResultVO) => {
  try {
    // 设置报表参数
    reportParameters.value = { taskResultId: row.id };
    // 显示报表预览弹窗
    reportDialogVisible.value = true;
  } catch (error) {
    console.error('加载报告失败:', error);
    proxy?.$modal.msgError('加载报告失败，请稍后再试。');
  }
};

onMounted(() => {
  getList();
});

/** 路面病害识别结果状态变更 */
const handleDiseaseConfirmedChange = async (row: RoadInspectionDiseaseInfoVO, val: string) => {
  // 如果正在初始化，则跳过执行
  if (isInitializing.value) {
    return;
  }

  try {
    const updateData: RoadInspectionDiseaseInfoForm = {
      id: row.id,
      diseaseConfirmed: val
    };
    await updateRoadInspectionDiseaseInfo(updateData);
    proxy?.$modal.msgSuccess('路面病害确认状态更新成功');
  } catch (error) {
    proxy?.$modal.msgError('更新路面病害确认状态失败');
    // 恢复原状态
    row.diseaseConfirmed = row.diseaseConfirmed === 'Y' ? 'N' : 'Y';
  }
};

/** 施工监管识别结果状态变更 */
const handleSupervisionConfirmedChange = async (row: RoadInspectionSupervisionRecordVO, val: string) => {
  // 如果正在初始化，则跳过执行
  if (isInitializing.value) {
    return;
  }

  try {
    const updateData: RoadInspectionSupervisionRecordForm = {
      id: row.id,
      recognitionResultConfirmed: val
    };
    await updateRoadInspectionSupervisionRecord(updateData);
    proxy?.$modal.msgSuccess('施工监管确认状态更新成功');
  } catch (error) {
    proxy?.$modal.msgError('更新施工监管确认状态失败');
    // 恢复原状态
    row.recognitionResultConfirmed = row.recognitionResultConfirmed === 'Y' ? 'N' : 'Y';
  }
};

/** 侵占路权识别结果状态变更 */
const handleOccupationConfirmedChange = async (row: RoadRightOccupationInfoVO, val: string) => {
  // 如果正在初始化，则跳过执行
  if (isInitializing.value) {
    return;
  }

  try {
    const updateData: RoadRightOccupationInfoForm = {
      id: row.id,
      occupationConfirmed: val
    };
    await updateRoadRightOccupationInfo(updateData);
    proxy?.$modal.msgSuccess('侵占路权确认状态更新成功');
  } catch (error) {
    proxy?.$modal.msgError('更新侵占路权确认状态失败');
    // 恢复原状态
    row.occupationConfirmed = row.occupationConfirmed === '1' ? '0' : '1';
  }
};

/** 抛洒物识别结果状态变更 */
const handleThrowConfirmedChange = async (row: RoadThrowObjectInfoVO, val: string) => {
  // 如果正在初始化，则跳过执行
  if (isInitializing.value) {
    return;
  }

  try {
    const updateData: RoadThrowObjectInfoForm = {
      id: row.id,
      throwConfirmed: val
    };
    await updateRoadThrowObjectInfo(updateData);
    proxy?.$modal.msgSuccess('抛洒物确认状态更新成功');
  } catch (error) {
    proxy?.$modal.msgError('更新抛洒物确认状态失败');
    // 恢复原状态
    row.throwConfirmed = row.throwConfirmed === '1' ? '0' : '1';
  }
};

/** 道路灾害识别结果状态变更 */
const handleSlopeConfirmedChange = async (row: RoadEmergencyEvenInfoVO, val: string) => {
  // 如果正在初始化，则跳过执行
  if (isInitializing.value) {
    return;
  }

  try {
    const updateData: RoadEmergencyEvenInfoForm = {
      id: row.id,
      isConfirmed: val
    };
    await updateRoadEmergencyEvenInfo(updateData);
    proxy?.$modal.msgSuccess('道路灾害确认状态更新成功');
  } catch (error) {
    proxy?.$modal.msgError('更新道路灾害确认状态失败');
    // 恢复原状态
    row.isConfirmed = row.isConfirmed === '1' ? '0' : '1';
  }
};

/** 交通事件识别结果状态变更 */
const handleEventConfirmedChange = async (row: RoadAccidentInfoVO, val: string) => {
  // 如果正在初始化，则跳过执行
  if (isInitializing.value) {
    return;
  }

  try {
    const updateData: RoadAccidentInfoForm = {
      id: row.id,
      isConfirmed: val
    };
    await updateRoadAccidentInfo(updateData);
    proxy?.$modal.msgSuccess('交通事件确认状态更新成功');
  } catch (error) {
    proxy?.$modal.msgError('更新交通事件确认状态失败');
    // 恢复原状态
    row.isConfirmed = row.isConfirmed === '1' ? '0' : '1';
  }
};
</script>

<style scoped>
/* 紧凑布局样式 */
.filter-card {
  margin-bottom: 8px;
}

.compact-form {
  margin-bottom: 0;
}

.compact-form .el-form-item {
  margin-bottom: 4px;
  margin-right: 8px;
}

.image-card {
  margin-top: 4px;
}

.compact-table {
  font-size: 12px;
}

.compact-table .el-table__header th {
  padding: 4px 0;
  font-size: 12px;
  font-weight: 600;
}

.compact-table .el-table__body td {
  padding: 2px 0;
}

.compact-pagination {
  margin-top: 6px;
}

.compact-pagination .el-pagination {
  justify-content: center;
  font-size: 12px;
}

/* 图片缩略图样式优化 */
.el-image {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.el-image:hover {
  transform: scale(1.05);
}

/* 表格行高度优化 */
.el-table--small .el-table__body td {
  padding: 0px 0;
}

/* 进一步压缩表格行高 */
.el-table--small .el-table__row {
  height: 20px;
}

.el-table--small .el-table__header th {
  padding: 0px 0;
  font-size: 9px;
}

/* 进一步压缩表格 */
.compact-table .el-table__header th {
  padding: 0px 0;
  font-size: 9px;
  font-weight: 600;
}

.compact-table .el-table__body td {
  padding: 0px 0;
  font-size: 9px;
}

.compact-table .el-table__row {
  height: 20px;
}

/* 搜索表单紧凑化 */
.el-form--inline .el-form-item {
  margin-right: 2px;
  margin-bottom: 1px;
}

/* 进一步压缩搜索表单 */
.compact-form .el-form-item {
  margin-bottom: 0px;
  margin-right: 3px;
}

.compact-form .el-form-item__label {
  padding-bottom: 0px;
  line-height: 1.0;
  font-size: 11px;
}

.compact-form .el-input__wrapper {
  padding: 0px 4px;
}

.compact-form .el-select .el-input__wrapper {
  padding: 0px 4px;
}

.compact-form .el-button {
  padding: 1px 6px;
  height: 20px;
  font-size: 10px;
}

.compact-form .el-form-item__content {
  line-height: 1.0;
}

/* 进一步减小卡片间距 */
.el-card {
  margin-bottom: 2px;
}

/* 压缩卡片头部 */
.el-card__header {
  padding: 4px 8px;
}

.el-card__body {
  padding: 4px 8px;
}

/* 优化表格内容显示 */
.compact-table .cell {
  padding: 4px 8px;
}

/* 减小分页器高度 */
.compact-pagination .el-pagination .el-pager li {
  min-width: 28px;
  height: 28px;
  line-height: 28px;
}

/* 弹窗高度控制 */
:deep(.el-dialog) {
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

:deep(.el-dialog__body) {
  flex: 1;
  overflow-y: auto;
  max-height: calc(90vh - 140px);
  padding: 20px;
}

/* 表格容器高度优化 */
.image-card .el-card__body {
  max-height: calc(90vh - 200px);
  overflow-y: auto;
  padding: 3px;
}

/* 确保表格在容器内正确显示 */
.compact-table {
  max-height: calc(90vh - 240px);
  overflow-y: auto;
}

/* 优化表格滚动条 */
.compact-table .el-table__body-wrapper {
  overflow-y: auto;
  max-height: calc(90vh - 270px);
}

/* 确保分页器在底部 */
.compact-pagination {
  margin-top: 3px;
  padding: 3px 0;
}

.compact-pagination .el-pagination {
  font-size: 9px;
}

.compact-pagination .el-pagination .el-pager li {
  min-width: 18px;
  height: 18px;
  line-height: 18px;
}

/* 优化表格头部固定 */
.compact-table .el-table__header-wrapper {
  position: sticky;
  top: 0;
  z-index: 1;
  background: #fafafa;
}

/* 改善表格行间距 */
.compact-table .el-table__row {
  height: 20px;
}

/* 优化表格内容对齐 */
.compact-table .cell {
  padding: 0px 2px;
  line-height: 0.9;
}

/* 确保弹窗内容可以滚动 */
:deep(.el-dialog__body) {
  overflow-y: auto;
  overflow-x: hidden;
}

/* 优化tabs样式 */
:deep(.el-tabs__content) {
  padding: 2px 0;
}

:deep(.el-tabs__header) {
  margin-bottom: 4px;
}

:deep(.el-tabs__nav-wrap) {
  padding: 0;
}

:deep(.el-tabs__item) {
  padding: 4px 12px;
  font-size: 12px;
  line-height: 1.2;
}

:deep(.el-tabs__header) {
  padding: 0;
}

/* 改善卡片间距 */
.image-card {
  margin-top: 2px;
  margin-bottom: 2px;
}

/* 统计卡片样式 */
.statistics-card {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-top: 4px;
}

.statistics-card .el-card__header {
  padding: 3px 8px;
}

.statistics-card .el-card__body {
  padding: 3px 8px;
}

.statistics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.statistics-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.statistics-content {
  padding: 6px 0;
}

.statistics-item {
  text-align: center;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 6px;
  margin-bottom: 6px;
}

.statistics-label {
  margin-bottom: 6px;
}

.statistics-value {
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
}

/* 一行内紧凑展示统计信息 */
.statistics-row {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.statistics-item-inline {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  min-width: fit-content;
}

.statistics-label-inline {
  margin-bottom: 0;
}

.statistics-value-inline {
  font-size: 16px;
  font-weight: 600;
  color: #409eff;
  min-width: 20px;
  text-align: center;
}

/* 总数项特殊样式 */
.statistics-total {
  background: #e6f7ff !important;
  border-color: #91d5ff !important;
  font-weight: 600;
}

.statistics-total .statistics-value-inline {
  color: #1890ff;
  font-size: 18px;
}

/* 表格底部布局 */
.table-footer {
  margin-top: 4px;
}

.pagination-section {
  margin-bottom: 4px;
}

.statistics-section {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.statistics-section .statistics-row {
  justify-content: flex-start;
  gap: 6px;
}

.statistics-section .statistics-item-inline {
  padding: 3px 6px;
  font-size: 11px;
}

.statistics-section .statistics-value-inline {
  font-size: 14px;
}

.statistics-section .statistics-total .statistics-value-inline {
  font-size: 16px;
}
.nowrap-header .cell {
  white-space: nowrap;
}
</style>

