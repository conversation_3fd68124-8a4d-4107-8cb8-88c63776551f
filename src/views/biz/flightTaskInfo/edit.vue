<template>
  <el-dialog :title="dialogTitle" v-model="dialogVisible" width="680px" append-to-body @close="handleClose">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="125px">
      <!-- 关联的申请信息显示 -->
      <el-form-item label="关联申请" v-if="props.applicationInfo">
        <el-input :value="getApplicationDisplayText()" readonly placeholder="关联的设备使用申请" />
      </el-form-item>

      <el-form-item label="任务名称" prop="taskName">
        <el-input v-model="form.taskName" placeholder="请输入任务名称" />
      </el-form-item>

      <el-form-item label="所属项目" prop="projectId">
        <el-select v-model="form.projectId" placeholder="请选择所属项目" filterable @change="handleProjectChange">
          <el-option
            v-for="project in projectList"
            :key="project.id"
            :label="`${project.projectName}(${project.projectDesc || '无描述'})`"
            :value="project.id"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="任务场景" prop="taskType">
        <el-select v-model="form.taskType" placeholder="请选择任务场景">
          <el-option v-for="dict in flight_task_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="AI识别模型" prop="aiModelsId">
        <el-select v-model="aiModelsArray" placeholder="请先选择项目，再选择AI识别模型" multiple filterable :disabled="!form.projectId">
          <el-option v-for="model in aiModelList" :key="model.id" :label="model.name" :value="model.id"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="任务频率" prop="taskFrequency">
        <el-select v-model="form.taskFrequency" placeholder="请选择任务频率">
          <el-option v-for="dict in flight_task_frequency" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="计划执行日期" prop="plannedStartTime" v-if="form.taskFrequency === '1' || form.taskFrequency === '2'">
        <el-date-picker clearable v-model="form.plannedStartTime" type="date" value-format="YYYY-MM-DD" placeholder="请选择计划开始日期">
        </el-date-picker>
        <span style="margin-right: 5px; margin-left: 5px"> ~ </span>
        <el-date-picker clearable v-model="form.plannedEndTime" type="date" value-format="YYYY-MM-DD" placeholder="请选择计划结束日期">
        </el-date-picker>
      </el-form-item>

      <el-form-item label="计划执行时间" prop="taskPeriodsJson" v-if="form.taskFrequency === '1' || form.taskFrequency === '2'">
        <div style="width: 100%">
          <div v-for="(period, index) in taskPeriodsArray" :key="index" style="display: flex; align-items: center; margin-bottom: 8px">
            <el-time-picker v-model="period.startTime" placeholder="开始时间" format="HH:mm:ss" value-format="HH:mm:ss" style="width: 120px" />
            <span style="margin: 0 8px">~</span>
            <el-time-picker v-model="period.endTime" placeholder="结束时间" format="HH:mm:ss" value-format="HH:mm:ss" style="width: 120px" />
            <el-button
              type="danger"
              size="small"
              icon="Delete"
              style="margin-left: 8px"
              @click="removeTimePeriod(index)"
              :disabled="taskPeriodsArray.length === 1"
            />
          </div>
          <el-button type="primary" size="small" icon="Plus" @click="addTimePeriod"> 添加时间段 </el-button>
        </div>
      </el-form-item>

      <el-form-item label="电池容量达到" prop="batteryCapacityReached" v-if="form.taskFrequency === '2'">
        <el-input v-model="form.batteryCapacityReached" placeholder="请输入电池容量达到" />
      </el-form-item>

      <el-form-item label="储存容量达到" prop="storageCapacityReached" v-if="form.taskFrequency === '2'">
        <el-input v-model="form.storageCapacityReached" placeholder="请输入储存容量达到" />
      </el-form-item>

      <el-form-item label="执行航线" prop="routeNo">
        <el-select v-model="form.routeNo" placeholder="请选择执行航线" filterable>
          <el-option v-for="route in flightRouteInfoList" :key="route.id" :label="route.routeName" :value="route.routeNo"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="航线类型" prop="waylineType">
        <el-select v-model="form.waylineType" placeholder="请选择航线类型">
          <el-option v-for="dict in flight_task_wayline_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="执行设备" prop="deviceSn">
        <el-select v-model="form.deviceSn" placeholder="请选择执行设备" filterable :disabled="isDevicePreset">
          <el-option
            v-for="device in flightDeviceList"
            :key="device.id"
            :label="device.nickname || device.deviceName"
            :value="device.deviceSn"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="返航高度(m)(相对机场)" prop="returnAltitude">
        <el-input v-model="form.returnAltitude" placeholder="请输入返航高度(20-500)" type="number">
          <template #append>米</template>
        </el-input>
      </el-form-item>

      <el-form-item label="失联操作" prop="vectorOperation">
        <el-select v-model="form.vectorOperation" placeholder="请选择失联操作">
          <el-option v-for="dict in flight_task_vector_operation" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="备注" prop="notes">
        <el-input v-model="form.notes" type="textarea" placeholder="请输入内容" />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
          {{ isEditMode ? '更新计划' : '创建计划' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { addFlightTaskInfo, updateFlightTaskInfo, getFlightTaskInfo, createFlightTaskWithFlightControl } from '@/api/biz/flightTaskInfo';
import { listFlightDevice } from '@/api/biz/flightDevice';
import { listFlightRouteInfo } from '@/api/biz/flightRouteInfo';
import { listTenantProjects } from '@/api/biz/project';
import { getAiModelList } from '@/api/biz/aiModel';
import { FlightTaskInfoForm } from '@/api/biz/flightTaskInfo/types';
import { FlightDeviceVO } from '@/api/biz/flightDevice/types';
import { FlightRouteInfoVO } from '@/api/biz/flightRouteInfo/types';
import { ProjectVO } from '@/api/biz/project/types';
import { BizAiModelVo } from '@/api/biz/aiModel/types';
import { useUserStore } from '@/store/modules/user';
import { getCurrentEffectiveTenantId } from '@/utils/tenant';

// Props 定义
interface Props {
  visible: boolean;
  editId?: string | number; // 编辑的计划ID
  applicationInfo?: any; // 关联的设备使用申请信息
  presetDeviceSn?: string; // 预设的设备序列号
  deviceName?: string; // 设备名称
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  editId: '',
  applicationInfo: null,
  presetDeviceSn: '',
  deviceName: ''
});

// Emits 定义
const emit = defineEmits<{
  success: [];
  close: [];
}>();

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const userStore = useUserStore();
const { flight_task_frequency, flight_task_vector_operation, flight_task_type, flight_task_wayline_type } = toRefs<any>(
  proxy?.useDict('flight_task_frequency', 'flight_task_vector_operation', 'flight_task_type', 'flight_task_wayline_type')
);

const formRef = ref<ElFormInstance>();
const submitLoading = ref(false);
const flightDeviceList = ref<FlightDeviceVO[]>([]);
const flightRouteInfoList = ref<FlightRouteInfoVO[]>([]);
const projectList = ref<ProjectVO[]>([]);
const aiModelList = ref<BizAiModelVo[]>([]);

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => {
    if (!value) {
      emit('close');
    }
  }
});

const isEditMode = computed(() => !!props.editId);
const isDevicePreset = computed(() => !!props.presetDeviceSn);

const dialogTitle = computed(() => {
  if (props.applicationInfo) {
    return `安排飞行计划：${props.deviceName || '未知设备'}`;
  }
  return isEditMode.value ? '编辑飞行计划' : '新增飞行计划';
});

// 表单数据
// 生成默认任务名称
const generateDefaultTaskName = () => {
  const now = new Date();
  const dateTimeStr = now
    .toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
    .replace(/\//g, '-');
  return `飞行任务_${dateTimeStr}`;
};

const initFormData: FlightTaskInfoForm = {
  id: undefined,
  taskName: undefined, // 不在这里设置默认值，在resetForm中动态生成
  taskNo: undefined,
  taskType: '0', // 默认任务场景
  taskFrequency: '0', // 默认任务频率
  plannedStartTime: undefined,
  plannedEndTime: undefined,
  dailyScheduledStartTime: undefined,
  dailyScheduledEndTime: undefined,
  batteryCapacityReached: undefined,
  storageCapacityReached: undefined,
  routeId: undefined,
  routeNo: undefined,
  executionDeviceId: undefined,
  deviceSn: undefined,
  waylineType: '0', // 默认航线类型
  executor: undefined,
  estimatedPayload: undefined,
  notes: undefined,
  taskStatus: undefined,
  actualStartTime: undefined,
  actualEndTime: undefined,
  returnAltitude: 120, // 默认返航高度120米
  vectorOperation: '0', // 默认失联操作
  aiModelsId: undefined,
  aiModelsName: undefined,
  aiRecognitionModels: undefined, // 兼容旧版本
  projectId: undefined,
  projectName: undefined,
  taskPeriodsJson: undefined
};

const form = ref<FlightTaskInfoForm>({ ...initFormData });

// AI模型多选数组（用于界面显示）
const aiModelsArray = ref<number[]>([]);

// 计划执行时间段数组（用于界面显示）
const taskPeriodsArray = ref<Array<{ startTime: string; endTime: string }>>([]);

// 表单验证规则
const rules = ref({
  taskName: [{ required: true, message: '任务名称不能为空', trigger: 'submit' }],
  projectId: [{ required: true, message: '所属项目不能为空', trigger: 'submit' }],
  taskType: [{ required: true, message: '任务场景不能为空', trigger: 'submit' }],
  aiModelsId: [{ required: true, message: 'AI识别模型不能为空', trigger: 'submit' }],
  taskFrequency: [{ required: true, message: '任务频率不能为空', trigger: 'submit' }],
  plannedStartTime: [
    {
      required: false,
      message: '计划执行日期不能为空',
      trigger: 'submit',
      validator: (rule: any, value: any, callback: any) => {
        if ((form.value.taskFrequency === '1' || form.value.taskFrequency === '2') && !value) {
          callback(new Error('计划执行日期不能为空'));
        } else {
          callback();
        }
      }
    }
  ],
  taskPeriodsJson: [
    {
      required: false,
      message: '计划执行时间不能为空',
      trigger: 'submit',
      validator: (rule: any, value: any, callback: any) => {
        if ((form.value.taskFrequency === '1' || form.value.taskFrequency === '2') && !value) {
          callback(new Error('计划执行时间不能为空'));
        } else {
          callback();
        }
      }
    }
  ],
  routeNo: [{ required: true, message: '执行航线不能为空', trigger: 'submit' }],
  waylineType: [{ required: true, message: '航线类型不能为空', trigger: 'submit' }],
  deviceSn: [{ required: true, message: '执行设备不能为空', trigger: 'submit' }],
  returnAltitude: [
    { required: true, message: '返航高度不能为空', trigger: 'submit' },
    {
      validator: (rule: any, value: any, callback: any) => {
        if (value && (value < 20 || value > 500)) {
          callback(new Error('返航高度必须在20-500米之间'));
        } else {
          callback();
        }
      },
      trigger: 'submit'
    }
  ],
  vectorOperation: [{ required: true, message: '失联操作不能为空', trigger: 'submit' }]
});

// 监听AI模型数组变化，转换为逗号分隔字符串
watch(aiModelsArray, (newVal) => {
  if (newVal.length > 0) {
    form.value.aiModelsId = newVal.join(',');
    // 根据选中的ID获取对应的名称
    const selectedNames = newVal
      .map((id) => {
        const model = aiModelList.value.find((m) => m.id === id);
        return model ? model.name : '';
      })
      .filter((name) => name);
    form.value.aiModelsName = selectedNames.join(',');
  } else {
    form.value.aiModelsId = undefined;
    form.value.aiModelsName = undefined;
  }
});

// 监听时间段数组变化，转换为JSON字符串
watch(
  taskPeriodsArray,
  (newVal) => {
    if (newVal.length > 0 && newVal.some((period) => period.startTime && period.endTime)) {
      const validPeriods = newVal.filter((period) => period.startTime && period.endTime);
      const periodsData = validPeriods.map((period) => [period.startTime, period.endTime]);
      form.value.taskPeriodsJson = JSON.stringify(periodsData);
    } else {
      form.value.taskPeriodsJson = undefined;
    }
  },
  { deep: true }
);

// 监听航线选择变化，自动填充routeId
watch(
  () => form.value.routeNo,
  (newRouteNo) => {
    if (newRouteNo) {
      const selectedRoute = flightRouteInfoList.value.find((route) => route.routeNo === newRouteNo);
      if (selectedRoute) {
        form.value.routeId = selectedRoute.id;
      }
    } else {
      form.value.routeId = undefined;
    }
  }
);

// 监听设备选择变化，自动填充executionDeviceId
watch(
  () => form.value.deviceSn,
  (newDeviceSn) => {
    if (newDeviceSn) {
      const selectedDevice = flightDeviceList.value.find((device) => device.deviceSn === newDeviceSn);
      if (selectedDevice) {
        form.value.executionDeviceId = selectedDevice.id;
      }
    } else {
      form.value.executionDeviceId = undefined;
    }
  }
);

// 监听props变化
watch(
  () => props.visible,
  async (newVal) => {
    if (newVal) {
      await loadInitialData();

      // 先清除验证状态和重置字段
      formRef.value?.clearValidate();
      formRef.value?.resetFields();

      // 然后重置表单数据
      resetForm();

      if (isEditMode.value) {
        // 编辑模式：加载现有数据
        await loadTaskData();
      } else {
        // 新建模式：设置默认值
        await setDefaultValues();
      }
    }
  }
);

// 加载初始数据
const loadInitialData = async () => {
  try {
    // 并行加载设备、航线和项目数据
    const promises = [listFlightDevice({ pageNum: 1, pageSize: 1000 }), listFlightRouteInfo({ pageNum: 1, pageSize: 1000 })];

    // 只有在有租户ID的情况下才获取项目列表
    if (userStore.tenantId) {
      promises.push(listTenantProjects());
    }

    const results = await Promise.all(promises);

    flightDeviceList.value = results[0].rows || [];
    flightRouteInfoList.value = results[1].rows || [];

    // 如果有项目数据，则设置项目列表
    if (results.length > 2) {
      projectList.value = results[2].data || [];
    }
  } catch (error) {
    console.error('加载数据失败:', error);
    proxy?.$modal.msgError('加载数据失败');
  }
};

// 获取AI模型列表
const loadAiModelList = async (projectId?: string | number) => {
  if (!projectId) {
    aiModelList.value = [];
    return;
  }

  try {
    // 不要强制转换为Number，保持原始类型
    const res = await getAiModelList(projectId);
    aiModelList.value = res.data || [];
  } catch (error) {
    console.error('获取AI模型列表失败:', error);
    aiModelList.value = [];
    proxy?.$modal.msgError('获取AI模型列表失败');
  }
};

// 加载任务数据
const loadTaskData = async () => {
  try {
    const res = await getFlightTaskInfo(props.editId);
    Object.assign(form.value, res.data);

    // 处理AI模型：将逗号分隔字符串转换为数组
    if (form.value.aiModelsId) {
      aiModelsArray.value = form.value.aiModelsId.split(',').map((id) => Number(id));
    } else if (form.value.aiRecognitionModels) {
      // 兼容旧版本数据，但这里需要根据实际情况处理
      console.warn('检测到旧版本AI模型数据，建议更新数据格式');
      aiModelsArray.value = [];
    } else {
      aiModelsArray.value = [];
    }

    // 如果有项目ID，加载对应的AI模型列表
    if (form.value.projectId) {
      await loadAiModelList(form.value.projectId);
    }

    // 处理计划执行时间：将JSON字符串转换为时间段数组
    if (form.value.taskPeriodsJson) {
      try {
        const periodsData = JSON.parse(form.value.taskPeriodsJson);
        taskPeriodsArray.value = periodsData.map((period: string[]) => ({
          startTime: period[0],
          endTime: period[1]
        }));
      } catch (e) {
        console.error('解析时间段JSON失败:', e);
        taskPeriodsArray.value = [{ startTime: '', endTime: '' }];
      }
    } else {
      taskPeriodsArray.value = [{ startTime: '', endTime: '' }];
    }
  } catch (error) {
    console.error('加载任务数据失败:', error);
    proxy?.$modal.msgError('加载任务数据失败');
  }
};

// 设置默认值
const setDefaultValues = async () => {
  // 设置默认任务名称
  if (props.applicationInfo) {
    form.value.taskName = `设备使用计划_${props.deviceName || '未知设备'}_${new Date().toLocaleDateString()}`;
  }
  // 如果不是从申请创建的任务，taskName已经在initFormData中设置了默认值

  // 设置字典字段的默认值（都设置为'0'）
  form.value.taskType = '0'; // 任务场景默认为第一个选项
  form.value.taskFrequency = '0'; // 任务频率默认为第一个选项
  form.value.waylineType = '0'; // 航线类型默认为第一个选项
  form.value.vectorOperation = '0'; // 失联操作默认为第一个选项（返航）

  // 如果有预设设备序列号，设置默认设备
  if (props.presetDeviceSn) {
    form.value.deviceSn = props.presetDeviceSn;
  }

  // 设置租户相关字段
  if (props.applicationInfo) {
    // 场景2：从申请安排任务
    form.value.ownerTenantId = props.applicationInfo.ownerTenantId;
    form.value.targetTenantId = props.applicationInfo.targetTenantId;
    form.value.useApplicationId = props.applicationInfo.id;
  } else {
    // 场景1：自己安排任务 - 使用当前有效租户ID
    try {
      const currentTenantId = await getCurrentEffectiveTenantId();
      form.value.ownerTenantId = currentTenantId;
      form.value.targetTenantId = currentTenantId;
      console.log('使用当前有效租户ID:', currentTenantId);
    } catch (error) {
      console.warn('获取当前有效租户ID失败，使用登录租户ID:', error);
      form.value.ownerTenantId = userStore.tenantId;
      form.value.targetTenantId = userStore.tenantId;
    }
    form.value.useApplicationId = undefined;
  }

  // 只有定时执行(1)和条件执行(2)才设置计划时间
  if (form.value.taskFrequency === '1' || form.value.taskFrequency === '2') {
    // 设置默认计划时间（基于申请时间或当前时间）
    if (props.applicationInfo?.applyStartTime && props.applicationInfo?.applyEndTime) {
      form.value.plannedStartTime = props.applicationInfo.applyStartTime.split(' ')[0];
      form.value.plannedEndTime = props.applicationInfo.applyEndTime.split(' ')[0];

      // 设置执行时间（基于申请时间）
      const startTime = props.applicationInfo.applyStartTime.split(' ')[1];
      const endTime = props.applicationInfo.applyEndTime.split(' ')[1];
      form.value.dailyScheduledStartTime = startTime;
      form.value.dailyScheduledEndTime = endTime;
    } else {
      // 默认设置为明天开始
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      form.value.plannedStartTime = tomorrow.toISOString().split('T')[0];
      form.value.plannedEndTime = tomorrow.toISOString().split('T')[0];
    }
  } else {
    // 立即执行(0)和按需执行(3)不设置计划时间
    form.value.plannedStartTime = undefined;
    form.value.plannedEndTime = undefined;
    form.value.taskPeriodsJson = undefined;
    form.value.dailyScheduledStartTime = undefined;
    form.value.dailyScheduledEndTime = undefined;
  }

  // 设置其他默认值
  form.value.returnAltitude = 120; // 默认返航高度120米

  // 如果有项目ID，加载对应的AI模型列表
  if (form.value.projectId) {
    await loadAiModelList(form.value.projectId);
  }
};

// 重置表单
const resetForm = () => {
  form.value = { ...initFormData };
  // 重新生成任务名称，确保每次都是最新的时间
  form.value.taskName = generateDefaultTaskName();
  aiModelsArray.value = [];
  aiModelList.value = [];
  taskPeriodsArray.value = [{ startTime: '', endTime: '' }];
};

// 获取申请信息显示文本
const getApplicationDisplayText = () => {
  if (!props.applicationInfo) return '';
  return `申请ID: ${props.applicationInfo.id} | 设备: ${props.deviceName} | 时间: ${props.applicationInfo.applyStartTime} ~ ${props.applicationInfo.applyEndTime}`;
};

// 提交表单
const handleSubmit = () => {
  formRef.value?.validate(async (valid: boolean) => {
    if (!valid) return;

    try {
      submitLoading.value = true;

      if (isEditMode.value) {
        await updateFlightTaskInfo(form.value);
        proxy?.$modal.msgSuccess('飞行计划更新成功');
      } else {
        await createFlightTaskWithFlightControl(form.value);
        proxy?.$modal.msgSuccess('飞行计划创建成功');
      }

      emit('success');
    } catch (error) {
      console.error('操作失败:', error);
      proxy?.$modal.msgError('操作失败，请重试');
    } finally {
      submitLoading.value = false;
    }
  });
};

// 添加时间段
const addTimePeriod = () => {
  taskPeriodsArray.value.push({ startTime: '', endTime: '' });
};

// 删除时间段
const removeTimePeriod = (index: number) => {
  if (taskPeriodsArray.value.length > 1) {
    taskPeriodsArray.value.splice(index, 1);
  }
};

// 处理项目选择变化
const handleProjectChange = async (projectId: string | number) => {
  if (projectId) {
    const selectedProject = projectList.value.find((project) => project.id === projectId);
    if (selectedProject) {
      form.value.projectName = selectedProject.projectName;
    }

    // 加载对应项目的AI模型列表
    await loadAiModelList(projectId);

    // 清空之前选择的AI模型
    aiModelsArray.value = [];
    form.value.aiModelsId = undefined;
    form.value.aiModelsName = undefined;
  } else {
    form.value.projectName = undefined;
    aiModelList.value = [];
    aiModelsArray.value = [];
    form.value.aiModelsId = undefined;
    form.value.aiModelsName = undefined;
  }
};

// 关闭弹窗
const handleClose = () => {
  resetForm();
  emit('close');
};
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-textarea__inner) {
  min-height: 80px;
}
</style>
