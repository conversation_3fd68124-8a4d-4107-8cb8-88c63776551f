<template>
  <div class="p-2">
    <!-- 标签页导航 -->
    <el-tabs v-model="activeTab" @tab-change="handleTabChange" class="mb-4">
      <el-tab-pane label="自建任务" name="my_tasks" />
      <el-tab-pane label="申请任务" name="assigned_tasks" />
      <el-tab-pane label="全部任务" name="all" />
    </el-tabs>
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="任务名称" prop="taskName">
              <el-input v-model="queryParams.taskName" placeholder="请输入任务名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="任务编号" prop="taskNo">
              <el-input v-model="queryParams.taskNo" placeholder="请输入任务编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <!--            <el-form-item label="任务类型" prop="taskType">-->
            <!--              <el-select v-model="queryParams.taskType" placeholder="请选择任务类型" clearable >-->
            <!--                <el-option v-for="dict in flight_task_type" :key="dict.value" :label="dict.label" :value="dict.value"/>-->
            <!--              </el-select>-->
            <!--            </el-form-item>-->
            <el-form-item label="任务频率" prop="taskFrequency">
              <el-select v-model="queryParams.taskFrequency" placeholder="请选择任务频率" clearable>
                <el-option v-for="dict in flight_task_frequency" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="执行航线" prop="routeId">
              <el-input v-model="queryParams.routeId" placeholder="请输入执行航线id" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="执行设备" prop="executionDeviceId">
              <el-input v-model="queryParams.executionDeviceId" placeholder="请输入执行设备id" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <!--            <el-form-item label="备注" prop="notes">-->
            <!--              <el-input v-model="queryParams.notes" placeholder="请输入备注" clearable @keyup.enter="handleQuery" />-->
            <!--            </el-form-item>-->
            <!--            <el-form-item label="任务状态" prop="taskStatus">-->
            <!--              <el-select v-model="queryParams.taskStatus" placeholder="请选择任务状态" clearable >-->
            <!--                <el-option v-for="dict in flight_task_status" :key="dict.value" :label="dict.label" :value="dict.value"/>-->
            <!--              </el-select>-->
            <!--            </el-form-item>-->
            <el-form-item label="矢量操作" prop="vectorOperation">
              <el-select v-model="queryParams.vectorOperation" placeholder="请选择矢量操作" clearable>
                <el-option v-for="dict in flight_task_vector_operation" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5" v-if="activeTab === 'my_tasks'">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['biz:flightTaskInfo:add']">新增任务</el-button>
          </el-col>
          <el-col :span="1.5" v-if="activeTab === 'my_tasks'">
            <el-button type="success" plain icon="Position" @click="handleQuickPatrol" v-hasPermi="['biz:flightTaskInfo:add']">一键巡查</el-button>
          </el-col>
          <el-col :span="1.5" v-if="activeTab === 'my_tasks'">
            <el-button type="warning" plain icon="Warning" @click="handleEmergencyFlight" v-hasPermi="['biz:flightTaskInfo:add']">应急飞行</el-button>
          </el-col>
          <el-col :span="1.5" v-if="activeTab === 'my_tasks'">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['biz:flightTaskInfo:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5" v-if="activeTab === 'my_tasks'">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['biz:flightTaskInfo:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['biz:flightTaskInfo:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="flightTaskInfoList" @selection-change="handleSelectionChange">
        <el-table-column type="index" label="序号" width="50" align="center" />
        <el-table-column label="任务名称" align="center" prop="taskName" />
        <el-table-column label="任务类型" align="center" prop="taskType">
          <template #default="scope">
            <dict-tag :options="flight_task_type" :value="scope.row.taskType" />
          </template>
        </el-table-column>
        <el-table-column label="AI模型" align="center"> -- </el-table-column>
        <el-table-column label="任务频率" align="center" prop="taskFrequency">
          <template #default="scope">
            <dict-tag :options="flight_task_frequency" :value="scope.row.taskFrequency" />
          </template>
        </el-table-column>
        <el-table-column label="航线名称" align="center" prop="routeName" />
        <el-table-column label="执行设备" align="center" prop="executionDeviceName" />
        <el-table-column label="设备拥有方" align="center" prop="ownerTenantName">
          <template #default="scope">
            <div>{{ scope.row.ownerTenantName || '未知租户' }}</div>
            <small class="text-gray-500">ID: {{ scope.row.ownerTenantId }}</small>
          </template>
        </el-table-column>
        <el-table-column label="任务执行方" align="center" prop="targetTenantName">
          <template #default="scope">
            <div>{{ scope.row.targetTenantName || '未知租户' }}</div>
            <small class="text-gray-500">ID: {{ scope.row.targetTenantId }}</small>
          </template>
        </el-table-column>
        <el-table-column label="任务状态" align="center" prop="taskStatus">
          <template #default="scope">
            <dict-tag :options="flight_task_status" :value="scope.row.taskStatus" />
          </template>
        </el-table-column>
        <el-table-column label="计划任务时间" align="center" prop="plannedStartTime" width="320">
          <template #default="scope">
            <div>
              {{ scope.row.plannedStartTime ? parseTime(scope.row.plannedStartTime, '{y}-{m}-{d} {h}:{i}:{s}') : '-' }}
              ~
              {{ scope.row.plannedEndTime ? parseTime(scope.row.plannedEndTime, '{y}-{m}-{d} {h}:{i}:{s}') : '-' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <!-- 保留原有操作按钮逻辑 -->
            <template v-if="activeTab === 'my_tasks'">
              <el-tooltip content="编辑任务" placement="top">
                <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['biz:flightTaskInfo:edit']">编辑</el-button>
              </el-tooltip>
              <el-tooltip content="删除任务" placement="top">
                <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['biz:flightTaskInfo:remove']"
                  >删除</el-button
                >
              </el-tooltip>
              <el-tooltip content="查看关联申请" placement="top" v-if="scope.row.useApplicationId">
                <el-button link type="primary" icon="Document" @click="handleViewApplication(scope.row)">查看申请</el-button>
              </el-tooltip>
            </template>
            <template v-if="activeTab === 'assigned_tasks'">
              <el-tooltip content="查看详情" placement="top">
                <el-button link type="primary" icon="View" @click="handleViewTask(scope.row)">查看</el-button>
              </el-tooltip>
              <el-tooltip content="开始执行" placement="top" v-if="scope.row.taskStatus !== 'completed'">
                <el-button link type="success" icon="VideoPlay" @click="handleExecuteTask(scope.row)">执行</el-button>
              </el-tooltip>
              <el-tooltip content="查看关联申请" placement="top" v-if="scope.row.useApplicationId">
                <el-button link type="primary" icon="Document" @click="handleViewApplication(scope.row)">查看申请</el-button>
              </el-tooltip>
            </template>
            <template v-if="activeTab === 'all'">
              <el-tooltip content="查看详情" placement="top">
                <el-button link type="primary" icon="View" @click="handleViewTask(scope.row)">查看</el-button>
              </el-tooltip>
              <el-tooltip content="编辑任务" placement="top" v-if="String(userStore.tenantId) === String(scope.row.ownerTenantId)">
                <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['biz:flightTaskInfo:edit']">编辑</el-button>
              </el-tooltip>
            </template>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 使用独立的编辑组件 -->
    <FlightTaskEdit :visible="dialog.visible" :edit-id="editTaskId" @success="handleEditSuccess" @close="handleEditClose" />

    <!-- 一键巡查对话框 -->
    <el-dialog title="一键巡查" v-model="quickPatrolDialog.visible" width="1200px" append-to-body>
      <div class="quick-patrol-container">
        <!-- 上半部分：三栏布局 -->
        <div class="three-column-layout">
          <!-- 第一栏：任务分类 -->
          <div class="column task-category-column">
            <div class="column-header">任务分类</div>
            <div class="category-list">
              <div
                v-for="category in taskCategories"
                :key="category.value"
                class="category-item"
                :class="{ 'active': quickPatrolForm.inspectionType === category.value }"
                @click="selectTaskCategory(category.value)"
              >
                <i class="category-icon" :class="category.icon"></i>
                <span class="category-label">{{ category.label }}</span>
                <i class="el-icon-arrow-right category-arrow"></i>
              </div>
            </div>
          </div>

          <!-- 第二栏：机巢设备 -->
          <div class="column device-column">
            <div class="column-header">机巢</div>
            <div class="device-list" v-loading="deviceLoading">
              <div
                v-for="device in flightDeviceList"
                :key="device.id"
                class="device-item"
                :class="{
                  'active': selectedDeviceId === device.id,
                  'disabled': !quickPatrolForm.inspectionType
                }"
                @click="selectDevice(device)"
              >
                <div class="device-info">
                  <div class="device-name">{{ device.nickname }}</div>
                  <div class="device-status">
                    <span class="status-dot" :class="getDeviceStatusClass(device)"></span>
                    <span class="status-text">{{ getDeviceStatusText(device) }}</span>
                  </div>
                </div>
                <div class="device-icon">
                  <i class="el-icon-position"></i>
                </div>
              </div>
              <div v-if="flightDeviceList.length === 0 && !deviceLoading" class="empty-state">
                <el-empty description="暂无可用设备" :image-size="60" />
              </div>
            </div>
          </div>

          <!-- 第三栏：任务列表 -->
          <div class="column task-column">
            <div class="column-header">任务</div>
            <div class="task-list" v-loading="routeLoading">
              <div
                v-for="template in getTemplatesForDevice(selectedDeviceId)"
                :key="template.id"
                class="task-item"
                :class="{ 'selected': isTaskSelected(selectedDeviceId, template.id) }"
                @click="toggleTaskSelection(selectedDeviceId, template.id)"
              >
                <div class="task-info">
                  <div class="task-name">{{ template.taskName }}</div>
                  <div class="task-meta">
                    <span class="task-time">{{ formatTaskTime(template) }}</span>
                  </div>
                </div>
                <div class="task-action">
                  <el-checkbox
                    :model-value="isTaskSelected(selectedDeviceId, template.id)"
                    @change="toggleTaskSelection(selectedDeviceId, template.id)"
                  />
                </div>
              </div>
              <div v-if="!selectedDeviceId" class="empty-state">
                <div class="empty-text">请先选择机巢设备</div>
              </div>
              <div v-else-if="getTemplatesForDevice(selectedDeviceId).length === 0 && !routeLoading" class="empty-state">
                <el-empty description="该设备暂无可用任务" :image-size="60" />
              </div>
            </div>
          </div>
        </div>

        <!-- 下半部分：已分配任务 -->
        <div class="assigned-tasks-area">
          <div class="assigned-tasks-header">
            <h4 class="area-title">已分配任务</h4>
            <div class="header-actions">
              <el-button type="danger" size="small" @click="clearSelectedPlans" icon="Delete" :disabled="selectedPlansData.length === 0">
                一键清空
              </el-button>
            </div>
          </div>

          <div class="assigned-tasks-content">
            <div v-if="selectedPlansData.length === 0" class="no-assigned-tasks">
              <el-empty description="暂无已分配任务" :image-size="80" />
            </div>
            <div v-else class="assigned-tasks-grid">
              <div v-for="(task, index) in selectedPlansData" :key="`${task.deviceId}-${task.routeId}`" class="assigned-task-card">
                <div class="task-card-header">
                  <span class="task-number">{{ index + 1 }}号{{ task.inspectionType }}</span>
                  <el-button type="danger" size="small" icon="Close" circle @click="removeSelectedPlan(index)" />
                </div>
                <div class="task-card-content">
                  <div class="task-detail">
                    <span class="detail-label">设备:</span>
                    <span class="detail-value">{{ task.deviceName }}</span>
                  </div>
                  <div class="task-detail">
                    <span class="detail-label">任务:</span>
                    <span class="detail-value">{{ task.routeName }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="quickPatrolDialog.visible = false">取消</el-button>
          <el-button type="primary" :loading="quickPatrolDialog.loading" @click="handleQuickTakeoff" :disabled="selectedPlansData.length === 0">
            一键起飞 ({{ selectedPlansData.length }} 个任务)
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 应急飞行对话框 -->
    <el-dialog title="应急飞行" v-model="emergencyFlightDialog.visible" width="650px" append-to-body>
      <el-form :model="emergencyFlightForm" label-width="150px">
        <el-form-item label="飞行终点" required>
          <el-radio-group v-model="emergencyFlightForm.endpointType">
            <el-radio value="pile">桩号</el-radio>
            <el-radio value="coordinate">坐标</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 桩号输入区域 -->
        <template v-if="emergencyFlightForm.endpointType === 'pile'">
          <el-form-item label="所属项目">
            <el-select v-model="emergencyFlightForm.projectId" placeholder="请选择所属项目" filterable>
              <el-option
                v-for="project in projectList"
                :key="project.id"
                :label="`${project.projectName}(${project.projectDesc || '无描述'})`"
                :value="project.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="桩号">
            <el-input v-model="emergencyFlightForm.pileNo" placeholder="请输入桩号" />
          </el-form-item>
        </template>

        <!-- 坐标输入区域 -->
        <template v-if="emergencyFlightForm.endpointType === 'coordinate'">
          <el-form-item label="经度">
            <el-input v-model="emergencyFlightForm.longitude" placeholder="请输入经度" />
          </el-form-item>
          <el-form-item label="纬度">
            <el-input v-model="emergencyFlightForm.latitude" placeholder="请输入纬度" />
          </el-form-item>
        </template>

        <!-- 地图选点按钮 - 暂时屏蔽 -->
        <!-- <el-form-item label="">
          <el-button type="primary" plain icon="Location" @click="handleMapSelection"> 地图选点 </el-button>
        </el-form-item> -->

        <el-form-item label="执行设备" required>
          <el-select v-model="emergencyFlightForm.deviceId" placeholder="请选择执行设备" filterable>
            <el-option v-for="device in droneDeviceList" :key="device.id" :label="device.nickname" :value="device.id" />
          </el-select>
        </el-form-item>

        <!-- 飞行参数展示区域 -->
        <el-divider content-position="left">飞行参数</el-divider>
        <div class="flight-params-display">
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="返航高度">
                <el-input value="120米" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="目标高度">
                <el-input value="120米" readonly />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="安全起飞高度">
                <el-input value="120米" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="最高速度">
                <el-input value="10m/s" readonly />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="指点飞行高度">
                <el-input value="120米" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="指点飞行模式">
                <el-input value="设定高度飞行" readonly />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="遥控器失控动作">
                <el-input value="返航" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="信号丢失时退出航线">
                <el-input value="是" readonly />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="24">
              <el-form-item label="指点飞行失控动作">
                <el-input value="退出指点飞行任务，执行普通失控行为" readonly />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="emergencyFlightDialog.visible = false">取消</el-button>
          <el-button type="primary" :loading="emergencyFlightDialog.loading" @click="handleEmergencyTakeoff">起飞</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="FlightTaskInfo" lang="ts">
import { listFlightTaskInfo, listMyTasks, listAssignedTasks, delFlightTaskInfo, takeoffToPoint } from '@/api/biz/flightTaskInfo';
import { listFlightDevice } from '@/api/biz/flightDevice';
import { listDroneDevice } from '@/api/biz/droneDevice';
import { listFlightRouteInfo } from '@/api/biz/flightRouteInfo';
import { listFlightTaskInfoTemplate, executeBatchFromTemplates } from '@/api/biz/flightTaskInfoTemplate';
import { listTenant } from '@/api/system/tenant';
import { listTenantProjects } from '@/api/biz/project';
import { FlightTaskInfoVO, FlightTaskInfoQuery } from '@/api/biz/flightTaskInfo/types';
import { FlightDeviceVO } from '@/api/biz/flightDevice/types';
import { DroneDeviceVO } from '@/api/biz/droneDevice/types';
import { FlightRouteInfoVO } from '@/api/biz/flightRouteInfo/types';
import { FlightTaskInfoTemplateVO } from '@/api/biz/flightTaskInfoTemplate/types';
import { ProjectVO } from '@/api/biz/project/types';
import { useUserStore } from '@/store/modules/user';
import { useRouter } from 'vue-router';
import { watch, watchEffect } from 'vue';
import FlightTaskEdit from './edit.vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const userStore = useUserStore();
const router = useRouter();
const { flight_task_type, flight_task_frequency, flight_task_status, flight_task_vector_operation } = toRefs<any>(
  proxy?.useDict('flight_task_type', 'flight_task_frequency', 'flight_task_status', 'flight_task_vector_operation')
);

const flightTaskInfoList = ref<FlightTaskInfoVO[]>([]);
const flightDeviceList = ref<FlightDeviceVO[]>([]);
const droneDeviceList = ref<DroneDeviceVO[]>([]); // 应急飞行专用的无人机设备列表
const flightRouteInfoList = ref<FlightRouteInfoVO[]>([]);
const flightTaskTemplateList = ref<FlightTaskInfoTemplateVO[]>([]);
const projectList = ref<ProjectVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(false);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const activeTab = ref('my_tasks'); // 当前激活的标签页
const tenantMap = ref<Map<string, string>>(new Map()); // 租户ID到租户名称的映射
const editTaskId = ref<string | number>(''); // 编辑任务的ID

const queryFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const queryParams = ref<FlightTaskInfoQuery>({
  pageNum: 1,
  pageSize: 10,
  taskName: undefined,
  taskNo: undefined,
  taskType: undefined,
  taskFrequency: undefined,
  plannedStartTime: undefined,
  plannedEndTime: undefined,
  dailyScheduledStartTime: undefined,
  dailyScheduledEndTime: undefined,
  batteryCapacityReached: undefined,
  storageCapacityReached: undefined,
  routeId: undefined,
  executionDeviceId: undefined,
  executor: undefined,
  estimatedPayload: undefined,
  notes: undefined,
  taskStatus: undefined,
  actualStartTime: undefined,
  actualEndTime: undefined,
  returnAltitude: undefined,
  vectorOperation: undefined,
  ownerTenantId: undefined,
  targetTenantId: undefined,
  useApplicationId: undefined,
  params: {}
});

// 一键巡查相关数据
const quickPatrolDialog = reactive({
  visible: false,
  loading: false,
  progress: 0
});

const quickPatrolForm = reactive({
  inspectionType: '',
  devicePlans: {} as Record<string, string>, // 设备ID -> 计划ID的映射
  // 新增：按分类存储设备-任务映射
  categoryDevicePlans: {} as Record<string, Record<string, string>> // 分类 -> (设备ID -> 计划ID)
});

// 设备选择状态
const deviceSelectionStatus = ref<Record<string, boolean>>({});

// 设备对应的模板列表
const deviceTemplateMap = ref<Record<string, FlightTaskInfoTemplateVO[]>>({});

const batchOptions = reactive({
  taskPrefix: '一键巡查',
  executionTime: null,
  priority: 'medium'
});

const deviceLoading = ref(false);
const routeLoading = ref(false);
const planValidationLoading = ref(false);

// 新增：三栏布局相关数据
const selectedDeviceId = ref<string>('');

// 任务分类数据 - 使用字典数据
const taskCategories = computed(() => {
  if (!flight_task_type.value) return [];

  // 为字典项添加图标
  const iconMap: Record<string, string> = {
    'road_inspection': 'el-icon-truck',
    'slope_inspection': 'el-icon-warning',
    'construction_inspection': 'el-icon-setting'
  };

  return flight_task_type.value.map((dict: any) => ({
    value: dict.value,
    label: dict.label,
    icon: iconMap[dict.value] || 'el-icon-setting'
  }));
});

const selectedPlansData = ref<
  Array<{
    inspectionType: string;
    deviceName: string;
    routeName: string;
    deviceId: string;
    routeId: string;
    estimatedTime?: string;
    distance?: string;
    status?: string;
  }>
>([]);

/** 获取租户字典 */
const getTenantMap = async () => {
  try {
    const res = await listTenant({
      tenantId: '',
      contactUserName: '',
      contactPhone: '',
      companyName: '',
      pageNum: 1,
      pageSize: 1000
    });
    const tenantList = res.data || res.rows || [];
    const map = new Map<string, string>();
    tenantList.forEach((tenant: any) => {
      map.set(tenant.tenantId, tenant.companyName);
    });
    tenantMap.value = map;
  } catch (error) {
    console.error('获取租户字典失败:', error);
  }
};

/** 查询飞行任务计划管理列表 */
const getList = async () => {
  loading.value = true;

  let apiCall;
  switch (activeTab.value) {
    case 'my_tasks':
      apiCall = listMyTasks(queryParams.value);
      break;
    case 'assigned_tasks':
      apiCall = listAssignedTasks(queryParams.value);
      break;
    case 'all':
      apiCall = listFlightTaskInfo(queryParams.value);
      break;
    default:
      apiCall = listMyTasks(queryParams.value);
  }

  const res = await apiCall;
  const tasks = res.rows || [];

  // 处理数据，添加租户名称
  flightTaskInfoList.value = tasks.map((task) => {
    const processed = { ...task };

    // 添加设备拥有方租户名称
    if (task.ownerTenantId && tenantMap.value.has(task.ownerTenantId)) {
      processed.ownerTenantName = tenantMap.value.get(task.ownerTenantId);
    }

    // 添加任务执行方租户名称
    if (task.targetTenantId && tenantMap.value.has(task.targetTenantId)) {
      processed.targetTenantName = tenantMap.value.get(task.targetTenantId);
    }

    return processed;
  });
  total.value = res.total;
  loading.value = false;
};

/** 标签页切换 */
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName;
  queryParams.value.pageNum = 1;
  getList();
};

const getDeviceList = async () => {
  const res = await listFlightDevice();
  flightDeviceList.value = res.rows;
};

const getDroneDeviceList = async () => {
  const res = await listDroneDevice();
  droneDeviceList.value = res.rows;
};

const getRouteList = async () => {
  const res = await listFlightRouteInfo();
  flightRouteInfoList.value = res.rows;
};

const getProjectList = async () => {
  try {
    const res = await listTenantProjects();
    projectList.value = res.data || [];
  } catch (error) {
    console.error('获取项目列表失败:', error);
    projectList.value = [];
  }
};

/** 根据巡检类型和设备查询模板 */
const getTemplateList = async (taskType: string, deviceSns: string[]) => {
  if (!taskType || deviceSns.length === 0) {
    flightTaskTemplateList.value = [];
    deviceTemplateMap.value = {};
    return;
  }

  routeLoading.value = true;
  try {
    // 为每个设备查询对应的模板
    const templatePromises = deviceSns.map(async (deviceSn) => {
      const res = await listFlightTaskInfoTemplate({
        taskType,
        deviceSn,
        pageNum: 1,
        pageSize: 1000
      });
      return { deviceSn, templates: res.rows || [] };
    });

    const results = await Promise.all(templatePromises);
    const newDeviceTemplateMap: Record<string, FlightTaskInfoTemplateVO[]> = {};
    const allTemplates: FlightTaskInfoTemplateVO[] = [];

    // 构建设备到模板的映射
    results.forEach(({ deviceSn, templates }) => {
      // 找到对应的设备ID
      const device = flightDeviceList.value.find((d) => d.deviceSn === deviceSn);
      if (device) {
        newDeviceTemplateMap[device.id] = templates;
        allTemplates.push(...templates);
      }
    });

    // 去重（基于模板ID）
    const uniqueTemplates = allTemplates.filter((template, index, self) => index === self.findIndex((t) => t.id === template.id));

    deviceTemplateMap.value = newDeviceTemplateMap;
    flightTaskTemplateList.value = uniqueTemplates;
  } catch (error) {
    console.error('查询模板失败:', error);
    flightTaskTemplateList.value = [];
    deviceTemplateMap.value = {};
  } finally {
    routeLoading.value = false;
  }
};

/** 查询单个设备的模板 */
const getTemplateListForSingleDevice = async (taskType: string, deviceSn: string, deviceId: string) => {
  if (!taskType || !deviceSn) {
    return;
  }

  routeLoading.value = true;
  try {
    const res = await listFlightTaskInfoTemplate({
      taskType,
      deviceSn,
      pageNum: 1,
      pageSize: 1000
    });

    const templates = res.rows || [];

    // 更新设备模板映射
    deviceTemplateMap.value[deviceId] = templates;

    // 更新全局模板列表（去重）
    const existingTemplateIds = new Set(flightTaskTemplateList.value.map((t) => t.id));
    const newTemplates = templates.filter((t) => !existingTemplateIds.has(t.id));
    flightTaskTemplateList.value.push(...newTemplates);
  } catch (error) {
    console.error('查询单个设备模板失败:', error);
    deviceTemplateMap.value[deviceId] = [];
  } finally {
    routeLoading.value = false;
  }
};

/** 获取指定设备的模板列表 */
const getTemplatesForDevice = (deviceId: string): FlightTaskInfoTemplateVO[] => {
  return deviceTemplateMap.value[deviceId] || [];
};

/** 处理设备选择变化 */
const handleDeviceSelectionChange = (deviceId: string, selected: boolean) => {
  if (!selected) {
    // 取消选择设备时，清除对应的计划选择
    delete quickPatrolForm.devicePlans[deviceId];
    updateSelectedPlans();
  }
};

/** 处理计划选择变化 */
const handlePlanSelectionChange = (deviceId: string, templateId: string) => {
  if (templateId) {
    quickPatrolForm.devicePlans[deviceId] = templateId;
  } else {
    delete quickPatrolForm.devicePlans[deviceId];
  }
  updateSelectedPlans();
};

/** 更新已选计划列表 */
const updateSelectedPlans = () => {
  // 重新生成完整的已选计划列表，基于所有分类的选择
  const allPlans: Array<{
    inspectionType: string;
    deviceName: string;
    routeName: string;
    deviceId: string;
    routeId: string;
  }> = [];

  // 遍历所有分类的设备计划
  Object.entries(quickPatrolForm.categoryDevicePlans).forEach(([categoryType, devicePlans]) => {
    Object.entries(devicePlans).forEach(([deviceId, templateId]) => {
      const device = flightDeviceList.value.find((d) => d.id === deviceId);
      // 从设备对应的模板列表中查找
      const deviceTemplates = deviceTemplateMap.value[deviceId] || [];
      const template = deviceTemplates.find((t) => t.id === templateId);

      if (device && template) {
        allPlans.push({
          inspectionType: getInspectionTypeLabel(categoryType),
          deviceName: device.nickname,
          routeName: template.taskName,
          deviceId: device.id,
          routeId: template.id
        });
      }
    });
  });

  // 添加当前分类的选择（如果还没有保存到categoryDevicePlans中）
  if (quickPatrolForm.inspectionType && Object.keys(quickPatrolForm.devicePlans).length > 0) {
    Object.entries(quickPatrolForm.devicePlans).forEach(([deviceId, templateId]) => {
      const device = flightDeviceList.value.find((d) => d.id === deviceId);
      const deviceTemplates = deviceTemplateMap.value[deviceId] || [];
      const template = deviceTemplates.find((t) => t.id === templateId);

      if (device && template) {
        // 检查是否已经存在相同的计划
        const exists = allPlans.some(
          (plan) =>
            plan.deviceId === deviceId &&
            plan.routeId === templateId &&
            plan.inspectionType === getInspectionTypeLabel(quickPatrolForm.inspectionType)
        );

        if (!exists) {
          allPlans.push({
            inspectionType: getInspectionTypeLabel(quickPatrolForm.inspectionType),
            deviceName: device.nickname,
            routeName: template.taskName,
            deviceId: device.id,
            routeId: template.id
          });
        }
      }
    });
  }

  selectedPlansData.value = allPlans;
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: FlightTaskInfoVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  editTaskId.value = '';
  dialog.visible = true;
  dialog.title = '添加飞行任务计划';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: FlightTaskInfoVO) => {
  const _id = row?.id || ids.value[0];
  editTaskId.value = _id;
  dialog.visible = true;
  dialog.title = '修改飞行任务计划';
};

/** 编辑组件成功回调 */
const handleEditSuccess = async () => {
  dialog.visible = false;
  await getList();
};

/** 编辑组件关闭回调 */
const handleEditClose = () => {
  dialog.visible = false;
  editTaskId.value = '';
};

/** 删除按钮操作 */
const handleDelete = async (row?: FlightTaskInfoVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除飞行任务计划管理编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delFlightTaskInfo(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  let fileName = 'flightTaskInfo';

  // 根据当前标签页确定文件名
  switch (activeTab.value) {
    case 'my_tasks':
      fileName = '自建任务';
      break;
    case 'assigned_tasks':
      fileName = '申请任务';
      break;
    case 'all':
      fileName = '全部任务';
      break;
  }

  proxy?.download(
    'biz/flightTaskInfo/export',
    {
      ...queryParams.value,
      type: activeTab.value // 传递类型参数
    },
    `${fileName}_${new Date().getTime()}.xlsx`
  );
};

/** 查看任务详情 */
const handleViewTask = (row: FlightTaskInfoVO) => {
  // TODO: 实现查看任务详情功能
  proxy?.$modal.alert(`查看任务详情功能开发中...\n任务名称：${row.taskName}\n任务编号：${row.taskNo}`);
};

/** 执行任务 */
const handleExecuteTask = (row: FlightTaskInfoVO) => {
  // TODO: 实现执行任务功能
  proxy?.$modal.alert(`执行任务功能开发中...\n任务名称：${row.taskName}\n执行设备：${row.executionDeviceName || '未知设备'}`);
};

/** 查看关联申请 */
const handleViewApplication = (row: FlightTaskInfoVO) => {
  if (row.useApplicationId) {
    // 跳转到设备使用申请页面的"已审批"标签，并筛选对应的申请
    router.push({
      path: '/config/config_01/flightDeviceUseApplication',
      query: {
        tab: 'approved_applications',
        applicationId: row.useApplicationId
      }
    });
  }
};

/** 一键巡查 */
const handleQuickPatrol = () => {
  // 重置表单
  quickPatrolForm.inspectionType = '';
  quickPatrolForm.devicePlans = {};
  quickPatrolForm.categoryDevicePlans = {}; // 重置分类设备计划映射
  selectedDeviceId.value = '';
  deviceSelectionStatus.value = {};
  selectedPlansData.value = [];

  // 加载设备列表和航线列表
  getDeviceList();
  getRouteList();

  // 显示弹窗
  quickPatrolDialog.visible = true;
};

/** 选择任务分类 */
const selectTaskCategory = (categoryValue: string) => {
  // 保存当前分类的选择到categoryDevicePlans中
  if (quickPatrolForm.inspectionType && Object.keys(quickPatrolForm.devicePlans).length > 0) {
    quickPatrolForm.categoryDevicePlans[quickPatrolForm.inspectionType] = { ...quickPatrolForm.devicePlans };
  }

  // 切换到新分类
  quickPatrolForm.inspectionType = categoryValue;
  selectedDeviceId.value = '';

  // 恢复新分类的选择状态
  quickPatrolForm.devicePlans = quickPatrolForm.categoryDevicePlans[categoryValue] || {};

  // 不要清空模板数据！保持已加载的模板数据
  // deviceTemplateMap.value = {}; // 注释掉这行
  // flightTaskTemplateList.value = []; // 注释掉这行

  // 触发更新已选计划列表
  updateSelectedPlans();
};

/** 选择设备 */
const selectDevice = async (device: FlightDeviceVO) => {
  if (!quickPatrolForm.inspectionType) {
    proxy?.$modal.msgWarning('请先选择任务分类');
    return;
  }

  // 如果已经选择了这个设备，直接返回
  if (selectedDeviceId.value === device.id) {
    return;
  }

  selectedDeviceId.value = device.id;

  // 为了确保数据正确，每次选择设备时都重新查询当前分类的模板
  // 清空该设备的旧模板数据
  delete deviceTemplateMap.value[device.id];

  // 查询当前分类下该设备的模板
  await getTemplateListForSingleDevice(quickPatrolForm.inspectionType, device.deviceSn, device.id);

  const templates = getTemplatesForDevice(device.id);
  if (templates.length === 0) {
    proxy?.$modal.msgWarning('该设备暂无可用任务模板');
  }
};

/** 切换任务选择状态 */
const toggleTaskSelection = async (deviceId: string, templateId: string) => {
  if (!deviceId || !templateId) return;

  const isSelected = quickPatrolForm.devicePlans[deviceId] === templateId;

  if (isSelected) {
    // 取消选择
    delete quickPatrolForm.devicePlans[deviceId];
  } else {
    // 检查该设备是否已经在其他分类中被分配了任务
    const deviceConflict = await checkDeviceConflict(deviceId);

    if (deviceConflict) {
      const device = flightDeviceList.value.find((d) => d.id === deviceId);
      const deviceName = device?.nickname || '未知设备';

      try {
        await proxy?.$modal.confirm(`已为设备"${deviceName}"分配任务（${deviceConflict.conflictInfo}），是否确定覆盖？`, '设备冲突提示', {
          confirmButtonText: '确定覆盖',
          cancelButtonText: '取消',
          type: 'warning'
        });

        // 用户确认覆盖，移除其他分类中该设备的任务
        removeDeviceFromOtherCategories(deviceId);
      } catch {
        // 用户取消，不执行选择操作
        return;
      }
    }

    // 选择任务（每个设备只能选择一个任务）
    quickPatrolForm.devicePlans[deviceId] = templateId;
  }

  // 同步更新到categoryDevicePlans
  if (quickPatrolForm.inspectionType) {
    quickPatrolForm.categoryDevicePlans[quickPatrolForm.inspectionType] = { ...quickPatrolForm.devicePlans };
  }

  updateSelectedPlans();
};

/** 检查任务是否被选中 */
const isTaskSelected = (deviceId: string, templateId: string): boolean => {
  return quickPatrolForm.devicePlans[deviceId] === templateId;
};

/** 获取设备状态样式类 */
const getDeviceStatusClass = (device: FlightDeviceVO): string => {
  // 根据设备状态返回对应的样式类
  // 这里可以根据实际的设备状态字段来判断
  return 'online'; // 默认在线状态
};

/** 获取设备状态文本 */
const getDeviceStatusText = (device: FlightDeviceVO): string => {
  // 根据设备状态返回对应的文本
  // 这里可以根据实际的设备状态字段来判断
  return '待机'; // 默认待机状态
};

/** 格式化任务时间 */
const formatTaskTime = (template: FlightTaskInfoTemplateVO): string => {
  // 根据模板信息格式化显示时间
  // 这里可以根据实际的模板字段来格式化
  return '120min01s'; // 默认时间格式
};

/** 生成已选计划数据 */
const generateSelectedPlans = () => {
  selectedPlansData.value = [];

  if (quickPatrolForm.inspectionType && quickPatrolForm.selectedDevices.length && quickPatrolForm.selectedRoutes.length) {
    quickPatrolForm.selectedDevices.forEach((deviceId) => {
      const device = flightDeviceList.value.find((d) => d.id === deviceId);
      quickPatrolForm.selectedRoutes.forEach((routeId) => {
        const route = flightRouteInfoList.value.find((r) => r.id === routeId);
        if (device && route) {
          selectedPlansData.value.push({
            inspectionType: getInspectionTypeLabel(quickPatrolForm.inspectionType),
            deviceName: device.nickname,
            routeName: route.routeName,
            deviceId: device.id,
            routeId: route.id
          });
        }
      });
    });
  }
};

/** 获取巡检类型显示文本 */
const getInspectionTypeLabel = (type: string) => {
  if (!flight_task_type.value) return type;

  const dictItem = flight_task_type.value.find((item: any) => item.value === type);
  return dictItem ? dictItem.label : type;
};

/** 检查设备冲突 */
const checkDeviceConflict = async (deviceId: string) => {
  // 检查该设备是否已经在其他分类中被分配了任务
  for (const [categoryType, devicePlans] of Object.entries(quickPatrolForm.categoryDevicePlans)) {
    if (categoryType !== quickPatrolForm.inspectionType && devicePlans[deviceId]) {
      const templateId = devicePlans[deviceId];

      // 直接从已选计划列表中查找模板信息，这是最准确的数据源
      const existingPlan = selectedPlansData.value.find((plan) => plan.deviceId === deviceId && plan.routeId === templateId);

      let taskName = '未知任务';

      if (existingPlan) {
        taskName = existingPlan.routeName;
      } else {
        // 如果已选计划中没有，说明数据可能不一致，尝试重新查询
        try {
          const device = flightDeviceList.value.find((d) => d.id === deviceId);
          if (device && device.deviceSn) {
            const res = await listFlightTaskInfoTemplate({
              taskType: categoryType,
              deviceSn: device.deviceSn,
              pageNum: 1,
              pageSize: 1000
            });
            const templates = res.rows || [];
            const foundTemplate = templates.find((t) => t.id === templateId);
            if (foundTemplate) {
              taskName = foundTemplate.taskName;
            } else {
              // 如果还是找不到，显示模板ID
              taskName = `模板ID: ${templateId}`;
            }
          }
        } catch (error) {
          console.error('查询冲突模板信息失败:', error);
          taskName = `模板ID: ${templateId}`;
        }
      }

      return {
        categoryType,
        templateId,
        conflictInfo: `${getInspectionTypeLabel(categoryType)} - ${taskName}`
      };
    }
  }
  return null;
};

/** 移除设备在其他分类中的任务 */
const removeDeviceFromOtherCategories = (deviceId: string) => {
  // 从所有其他分类中移除该设备的任务分配
  Object.keys(quickPatrolForm.categoryDevicePlans).forEach((categoryType) => {
    if (categoryType !== quickPatrolForm.inspectionType) {
      delete quickPatrolForm.categoryDevicePlans[categoryType][deviceId];
    }
  });

  // 更新已选计划列表
  updateSelectedPlans();
};

/** 移除已选计划 */
const removeSelectedPlan = (index: number) => {
  const plan = selectedPlansData.value[index];
  if (plan) {
    // 需要找到该计划对应的分类类型
    let targetCategoryType = '';

    // 遍历所有分类，找到包含该设备和任务的分类
    for (const [categoryType, devicePlans] of Object.entries(quickPatrolForm.categoryDevicePlans)) {
      if (devicePlans[plan.deviceId] === plan.routeId) {
        targetCategoryType = categoryType;
        break;
      }
    }

    // 如果找到了对应的分类，从该分类中移除
    if (targetCategoryType) {
      delete quickPatrolForm.categoryDevicePlans[targetCategoryType][plan.deviceId];
    }

    // 如果是当前分类的计划，也要从当前分类中移除
    if (quickPatrolForm.devicePlans[plan.deviceId] === plan.routeId) {
      delete quickPatrolForm.devicePlans[plan.deviceId];
      // 更新设备选择状态
      deviceSelectionStatus.value[plan.deviceId] = false;
    }

    // 手动更新已选计划列表
    updateSelectedPlans();
  }
};

/** 清空所有选择 */
const clearSelection = () => {
  quickPatrolForm.inspectionType = '';
  quickPatrolForm.devicePlans = {};
  deviceSelectionStatus.value = {};
  selectedPlansData.value = [];
};

/** 清空已选计划 */
const clearSelectedPlans = () => {
  // 清空当前分类的设备计划
  quickPatrolForm.devicePlans = {};

  // 清空所有分类的设备计划映射
  quickPatrolForm.categoryDevicePlans = {};

  // 清空设备选择状态
  deviceSelectionStatus.value = {};

  // 清空已选计划列表
  selectedPlansData.value = [];

  // 重置当前选中的设备
  selectedDeviceId.value = '';
};

/** 一键起飞 */
const handleQuickTakeoff = async () => {
  if (selectedPlansData.value.length === 0) {
    proxy?.$modal.msgWarning('请至少选择一个计划');
    return;
  }

  // 提取模板ID列表
  const templateIds = selectedPlansData.value.map((plan) => plan.routeId);

  // 显示确认信息
  const planDetails = selectedPlansData.value
    .map((plan, index) => `${index + 1}. ${plan.inspectionType} - ${plan.deviceName} - ${plan.routeName}`)
    .join('\n');

  try {
    await proxy?.$modal.confirm(`即将提交 ${selectedPlansData.value.length} 个任务模板：\n\n${planDetails}`, '确认一键起飞', {
      confirmButtonText: '确认起飞',
      cancelButtonText: '取消',
      type: 'warning'
    });

    // 调用批量执行接口
    quickPatrolDialog.loading = true;

    const res = await executeBatchFromTemplates(templateIds);

    if (res.code === 200) {
      proxy?.$modal.msgSuccess(`成功创建 ${selectedPlansData.value.length} 个巡查任务并开始执行！`);
      quickPatrolDialog.visible = false;

      // 清空选择状态
      quickPatrolForm.inspectionType = '';
      quickPatrolForm.devicePlans = {};
      quickPatrolForm.categoryDevicePlans = {};
      selectedDeviceId.value = '';
      deviceSelectionStatus.value = {};
      selectedPlansData.value = [];

      // 刷新任务列表
      await getList();
    } else {
      proxy?.$modal.msgError(res.msg || '创建任务失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量执行任务失败:', error);
      proxy?.$modal.msgError('创建任务失败，请稍后重试');
    }
  } finally {
    quickPatrolDialog.loading = false;
  }
};

// 监听设备计划变化，自动更新已选计划列表
watch(
  () => quickPatrolForm.devicePlans,
  () => {
    updateSelectedPlans();
  },
  { deep: true }
);

// 检查是否可以预览
const canPreview = computed(() => {
  return quickPatrolForm.inspectionType && Object.keys(quickPatrolForm.devicePlans).length > 0;
});

/** 预览待添加的计划 */
const previewPlans = () => {
  if (!canPreview.value) return;

  // This function is no longer needed as preview is directly in the dialog
};

/** 添加预览的计划到已选计划 */
const addPreviewedPlans = () => {
  if (selectedPlansData.value.length === 0) return;

  let addedCount = 0;
  selectedPlansData.value.forEach((plan) => {
    // 检查是否已存在相同的组合
    const exists = selectedPlansData.value.some((p) => p.deviceId === plan.deviceId && p.routeId === plan.routeId);

    if (!exists) {
      selectedPlansData.value.push({
        inspectionType: plan.inspectionType,
        deviceName: plan.deviceName,
        routeName: plan.routeName,
        deviceId: plan.deviceId,
        routeId: plan.routeId
      });
      addedCount++;
    }
  });

  if (addedCount > 0) {
    proxy?.$modal.msgSuccess(`成功添加 ${addedCount} 个计划`);
  } else {
    proxy?.$modal.msgWarning('所有计划已存在');
  }

  // 清空预览和选择
  // previewPlansData.value = []; // This line is no longer needed
  quickPatrolForm.inspectionType = '';
  quickPatrolForm.selectedDevices = [];
  quickPatrolForm.selectedRoutes = [];
};

// 应急飞行弹窗数据
const emergencyFlightDialog = reactive({
  visible: false,
  loading: false
});
const emergencyFlightForm = reactive({
  endpointType: 'pile', // 'pile' 或 'coordinate'
  projectId: '', // 所属项目ID
  pileNo: '', // 桩号
  longitude: '', // 经度
  latitude: '', // 纬度
  deviceId: ''
});

// 打开应急飞行弹窗
const handleEmergencyFlight = () => {
  getDroneDeviceList();
  getProjectList();
  emergencyFlightForm.endpointType = 'pile';
  emergencyFlightForm.projectId = '';
  emergencyFlightForm.pileNo = '';
  emergencyFlightForm.longitude = '';
  emergencyFlightForm.latitude = '';
  emergencyFlightForm.deviceId = '';
  emergencyFlightDialog.visible = true;
};

// 应急飞行起飞按钮
const handleEmergencyTakeoff = async () => {
  // 验证表单
  let isValid = false;

  if (emergencyFlightForm.endpointType === 'pile') {
    // 桩号模式：需要所属项目和桩号
    isValid = emergencyFlightForm.projectId && emergencyFlightForm.pileNo;
    if (!isValid) {
      proxy?.$modal.msgWarning('请选择所属项目和填写桩号');
      return;
    }
  } else if (emergencyFlightForm.endpointType === 'coordinate') {
    // 坐标模式：需要经度和纬度
    isValid = emergencyFlightForm.longitude && emergencyFlightForm.latitude;
    if (!isValid) {
      proxy?.$modal.msgWarning('请填写经度和纬度');
      return;
    }
  }

  if (!emergencyFlightForm.deviceId) {
    proxy?.$modal.msgWarning('请选择执行设备');
    return;
  }

  try {
    emergencyFlightDialog.loading = true;

    // 获取选中设备的序列号
    const selectedDevice = droneDeviceList.value.find((device) => device.id === emergencyFlightForm.deviceId);
    if (!selectedDevice) {
      proxy?.$modal.msgError('未找到选中的设备信息');
      return;
    }

    // 构建请求参数
    const params: {
      targetType: number;
      projectCode?: string;
      pileNo?: string;
      targetLongitude?: number;
      targetLatitude?: number;
    } = {
      targetType: emergencyFlightForm.endpointType === 'pile' ? 0 : 1
    };

    if (emergencyFlightForm.endpointType === 'pile') {
      // 桩号模式
      const selectedProject = projectList.value.find((project) => project.id === emergencyFlightForm.projectId);
      params.projectCode = selectedProject?.projectCode || selectedProject?.projectName;
      params.pileNo = emergencyFlightForm.pileNo;
    } else {
      // 坐标模式
      params.targetLongitude = parseFloat(emergencyFlightForm.longitude);
      params.targetLatitude = parseFloat(emergencyFlightForm.latitude);

      // 验证坐标格式
      if (isNaN(params.targetLongitude) || isNaN(params.targetLatitude)) {
        proxy?.$modal.msgWarning('请输入有效的经纬度坐标');
        return;
      }

      // 验证坐标范围
      if (params.targetLongitude < -180 || params.targetLongitude > 180) {
        proxy?.$modal.msgWarning('经度范围应在-180到180之间');
        return;
      }
      if (params.targetLatitude < -90 || params.targetLatitude > 90) {
        proxy?.$modal.msgWarning('纬度范围应在-90到90之间');
        return;
      }
    }

    // 显示确认对话框
    const confirmMessage =
      emergencyFlightForm.endpointType === 'pile'
        ? `确认执行应急飞行？\n设备：${selectedDevice.nickname}\n目标：${params.projectCode} - ${params.pileNo}`
        : `确认执行应急飞行？\n设备：${selectedDevice.nickname}\n目标坐标：${params.targetLongitude}, ${params.targetLatitude}`;

    await proxy?.$modal.confirm(confirmMessage, '应急飞行确认', {
      confirmButtonText: '确认起飞',
      cancelButtonText: '取消',
      type: 'warning'
    });

    // 调用应急飞行接口
    const res = await takeoffToPoint(selectedDevice.deviceSn, params);

    if (res.code === 200) {
      proxy?.$modal.msgSuccess('应急飞行指令已发送，设备正在执行');
      emergencyFlightDialog.visible = false;

      // 重置表单
      emergencyFlightForm.endpointType = 'pile';
      emergencyFlightForm.projectId = '';
      emergencyFlightForm.pileNo = '';
      emergencyFlightForm.longitude = '';
      emergencyFlightForm.latitude = '';
      emergencyFlightForm.deviceId = '';

      // 刷新任务列表
      await getList();
    } else {
      proxy?.$modal.msgError(res.msg || '应急飞行指令发送失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('应急飞行失败:', error);
      proxy?.$modal.msgError('应急飞行指令发送失败，请稍后重试');
    }
  } finally {
    emergencyFlightDialog.loading = false;
  }
};

// 地图选点功能
const handleMapSelection = () => {
  // TODO: 实现地图选点功能
  proxy?.$modal.msgInfo('地图选点功能开发中...');
};

onMounted(async () => {
  // 先获取租户字典，再获取任务列表
  await getTenantMap();
  await getList();
});
</script>

<style scoped>
.text-gray-500 {
  color: #6b7280;
  font-size: 12px;
}

.quick-patrol-container {
  padding: 0;
}

/* 三栏布局样式 */
.three-column-layout {
  display: flex;
  height: 400px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
}

.column {
  display: flex;
  flex-direction: column;
  border-right: 1px solid #e4e7ed;
}

.column:last-child {
  border-right: none;
}

.task-category-column {
  width: 200px;
  background-color: #f8f9fa;
}

.device-column {
  width: 300px;
  background-color: #fff;
}

.task-column {
  flex: 1;
  background-color: #fff;
}

.column-header {
  padding: 15px 20px;
  background-color: #409eff;
  color: white;
  font-weight: 600;
  font-size: 16px;
  text-align: center;
  border-bottom: 1px solid #e4e7ed;
}

/* 任务分类列表样式 */
.category-list {
  flex: 1;
  overflow-y: auto;
}

.category-item {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 1px solid #f0f0f0;
}

.category-item:hover {
  background-color: #e8f4ff;
}

.category-item.active {
  background-color: #409eff;
  color: white;
}

.category-icon {
  margin-right: 10px;
  font-size: 18px;
}

.category-label {
  flex: 1;
  font-size: 14px;
}

.category-arrow {
  font-size: 12px;
  opacity: 0.6;
}

/* 设备列表样式 */
.device-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.device-item {
  display: flex;
  align-items: center;
  padding: 15px;
  margin-bottom: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #fff;
}

.device-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.device-item.active {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.device-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #f5f5f5;
}

.device-info {
  flex: 1;
}

.device-name {
  font-weight: 600;
  font-size: 14px;
  color: #333;
  margin-bottom: 5px;
}

.device-status {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #666;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 5px;
}

.status-dot.online {
  background-color: #67c23a;
}

.status-dot.offline {
  background-color: #f56c6c;
}

.device-icon {
  font-size: 20px;
  color: #409eff;
}

/* 任务列表样式 */
.task-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.task-item {
  display: flex;
  align-items: center;
  padding: 15px;
  margin-bottom: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #fff;
}

.task-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.task-item.selected {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.task-info {
  flex: 1;
}

.task-name {
  font-weight: 600;
  font-size: 14px;
  color: #333;
  margin-bottom: 5px;
}

.task-meta {
  font-size: 12px;
  color: #666;
}

.task-time {
  color: #67c23a;
}

.task-action {
  margin-left: 10px;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #999;
}

.empty-text {
  font-size: 14px;
  color: #999;
}

/* 已分配任务区域样式 */
.assigned-tasks-area {
  border-top: 1px solid #e4e7ed;
  padding-top: 20px;
}

.assigned-tasks-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.area-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.assigned-tasks-content {
  min-height: 150px;
}

.no-assigned-tasks {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 150px;
}

.assigned-tasks-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 15px;
}

.assigned-task-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 15px;
  background-color: #fff;
  transition: all 0.3s ease;
}

.assigned-task-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.task-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.task-number {
  font-weight: 600;
  color: #409eff;
  font-size: 14px;
}

.task-card-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.task-detail {
  display: flex;
  align-items: center;
  font-size: 13px;
}

.detail-label {
  color: #666;
  margin-right: 8px;
  min-width: 40px;
}

.detail-value {
  color: #333;
  font-weight: 500;
}

.dialog-footer {
  text-align: right;
}

/* 飞行参数展示区域样式 */
.flight-params-display {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin-top: 10px;
}

.flight-params-display .el-form-item {
  margin-bottom: 12px;
}

.flight-params-display .el-input__inner {
  background-color: #fff;
  border: 1px solid #e4e7ed;
  color: #606266;
  font-weight: 500;
}

.flight-params-display .el-form-item__label {
  color: #606266;
  font-weight: 600;
  width: 140px !important;
  min-width: 140px;
}
</style>
