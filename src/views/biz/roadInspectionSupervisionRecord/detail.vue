<template>
  <div class="detail-container">
    <el-card shadow="never" class="filter-card">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true" size="small" class="compact-form">
        <el-form-item label="安全事件类型" prop="evenType">
          <el-select v-model="queryParams.evenType" placeholder="安全事件类型" clearable>
            <el-option v-for="dict in work_even_type" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否确认" prop="recognitionResultConfirmed">
          <el-select v-model="queryParams.recognitionResultConfirmed" placeholder="是否确认" clearable>
            <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery" size="small">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery" size="small">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card v-if="loading" shadow="never" v-loading="loading" class="supervision-card">
      <p>加载中...</p>
    </el-card>
    <el-card v-else-if="supervisionList && supervisionList.length > 0" shadow="never" class="supervision-card">
      <el-table :data="supervisionList" border style="width: 100%" size="small" class="compact-table">
        <el-table-column type="index" label="序号" width="50" align="center" />
        <el-table-column label="安全事件类型" min-width="100" align="center" prop="evenType">
          <template #default="scope">
            <dict-tag :options="work_even_type" :value="scope.row.evenType" />
          </template>
        </el-table-column>
        <el-table-column prop="pileNo" label="桩号" min-width="80" align="center">
          <template #default="scope"> {{ scope.row.pileNo }} </template>
        </el-table-column>
        <el-table-column prop="siteLatitude" label="纬度" min-width="100" align="center" />
        <el-table-column prop="siteLongitude" label="经度" min-width="100" align="center" />

        <el-table-column label="图片" min-width="80" align="center">
          <template #default="scope">
            <image-preview :src="scope.row.imageUrl" :width="30" :height="30" :disable-preview="true" @click="openPreview(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column label="识别结果确认" width="80" align="center" fixed="right" v-hasPermi="['biz:flightTaskResult:edit']">
          <template #default="scope">
            <el-switch
              v-model="scope.row.recognitionResultConfirmed"
              :active-value="'Y'"
              :inactive-value="'N'"
              inline-prompt
              active-text="是"
              inactive-text="否"
              @change="handleSupervisionConfirmedChange(scope.row)"
            />
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
        class="compact-pagination"
      />
    </el-card>
    <el-card v-else shadow="never" class="supervision-card">
      <p>暂无记录。</p>
      <div style="margin-top: 10px; text-align: right">
        <el-button @click="handleClose" size="small">关闭</el-button>
      </div>
    </el-card>

    <!-- 图片预览弹窗 -->
    <PreviewModal v-model="previewVisible" :image-url="currentImageUrl" :labels="currentLabels" @close="handlePreviewClose" />
  </div>
</template>

<script setup name="SupervisionRecordDetail" lang="ts">
import { ref, getCurrentInstance, reactive, toRefs, watchEffect, defineProps, defineEmits } from 'vue';
import { listRoadInspectionSupervisionRecord, updateRoadInspectionSupervisionRecord } from '@/api/biz/roadInspectionSupervisionRecord';
import {
  RoadInspectionSupervisionRecordVO,
  RoadInspectionSupervisionRecordQuery,
  RoadInspectionSupervisionRecordForm
} from '@/api/biz/roadInspectionSupervisionRecord/types';
import PreviewModal from '@/components/PreviewModal/index.vue';
import { useImageLabels } from '@/hooks/useImageLabels';

const props = defineProps({
  taskResultId: {
    type: [String, Number],
    required: true
  }
});

const emit = defineEmits(['close-dialog']);

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { work_even_type, sys_yes_no } = toRefs<any>(proxy?.useDict('work_even_type', 'sys_yes_no'));

// 使用图片标签 Hook
const { currentLabels, labelLoading, loadLabels } = useImageLabels();

const loading = ref(true);
const supervisionList = ref<RoadInspectionSupervisionRecordVO[]>([]);
const total = ref(0);
const queryFormRef = ref<ElFormInstance>();

// 预览相关
const previewVisible = ref(false);
const currentImageUrl = ref('');

// 查询参数
const queryParams = reactive<RoadInspectionSupervisionRecordQuery>({
  pageNum: 1,
  pageSize: 10,
  taskResultId: undefined,
  isWearingSafetyEquipment: undefined,
  recognitionResultConfirmed: undefined
});

const getList = async () => {
  console.log('getList函数被调用，查询参数:', queryParams);

  if (!props.taskResultId) {
    console.log('taskResultId为空，不执行查询');
    supervisionList.value = [];
    loading.value = false;
    return;
  }

  // 确保taskResultId始终存在于查询参数中
  queryParams.taskResultId = props.taskResultId;

  loading.value = true;
  try {
    const res = await listRoadInspectionSupervisionRecord(queryParams);
    supervisionList.value = res.rows || [];
    total.value = res.total || 0;
  } catch (error) {
    proxy?.$modal.msgError('获取监管记录列表失败');
    supervisionList.value = []; // 出错时清空
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  // 保留taskResultId，重置其他查询条件
  queryParams.taskResultId = props.taskResultId;
  handleQuery();
};

// 监听taskResultId变化
let isInitialized = false;
watchEffect(() => {
  console.log('watchEffect触发');
  console.log('props:', props);
  console.log('props.taskResultId的值:', props.taskResultId);
  console.log('props.taskResultId的类型:', typeof props.taskResultId);

  if (props.taskResultId) {
    console.log('监听到taskResultId变化:', props.taskResultId);
    queryParams.taskResultId = props.taskResultId;
    
    // 只在初始化或taskResultId真正变化时重置分页
    if (!isInitialized) {
      queryParams.pageNum = 1;
      isInitialized = true;
    }
    
    getList();
  } else {
    // 如果 taskResultId 为 null 或 undefined，则重置状态
    console.log('taskResultId为空，重置状态');
    supervisionList.value = [];
    total.value = 0;
    loading.value = false; // 确保 loading 状态正确
    isInitialized = false;
  }
});

const handleClose = () => {
  emit('close-dialog');
};

/** 识别结果状态变更 */
const handleSupervisionConfirmedChange = async (row: RoadInspectionSupervisionRecordVO) => {
  try {
    const updateData: RoadInspectionSupervisionRecordForm = {
      id: row.id,
      recognitionResultConfirmed: row.recognitionResultConfirmed
    };
    await updateRoadInspectionSupervisionRecord(updateData);
    // proxy?.$modal.msgSuccess('监管记录确认状态更新成功');
  } catch (error) {
    proxy?.$modal.msgError('更新监管记录确认状态失败');
    // 恢复原状态
    row.recognitionResultConfirmed = row.recognitionResultConfirmed === 'Y' ? 'N' : 'Y';
  }
};

/** 打开预览 */
const openPreview = async (supervisionInfo: RoadInspectionSupervisionRecordVO) => {
  currentImageUrl.value = supervisionInfo.imageUrl;
  previewVisible.value = true;

  if (supervisionInfo.imageUrl) {
    await loadLabels(supervisionInfo.imageUrl, supervisionInfo.evenType);
  }
};

/** 关闭预览 */
const handlePreviewClose = () => {
  previewVisible.value = false;
  currentImageUrl.value = '';
};
</script>

<style lang="scss" scoped>
.detail-container {
  padding: 8px;
  height: 100%;
  overflow: hidden;
}

.filter-card {
  margin-bottom: 3px;

  :deep(.el-card__body) {
    padding: 8px;
  }
}

.compact-form {
  :deep(.el-form-item) {
    margin-bottom: 0;
    margin-right: 8px;
  }

  :deep(.el-form-item__label) {
    padding-right: 3px;
    font-size: 13px;
  }
}

.supervision-card {
  height: calc(65vh);
  display: flex;
  flex-direction: column;
  overflow: hidden;

  :deep(.el-card__body) {
    padding: 8px;
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
}

.compact-table {
  flex: 1;

  :deep(.el-table__header-wrapper th) {
    padding: 5px 0;
    height: 40px;
  }

  :deep(.el-table__body-wrapper td) {
    padding: 5px 0;
    height: 40px;
  }
}

.compact-pagination {
  padding-top: 2px;
  margin-top: 2px;

  :deep(.el-pagination) {
    justify-content: flex-end;
    padding: 0;
    margin: 0;
  }

  :deep(.el-pagination .el-pager li) {
    min-width: 24px;
    height: 24px;
    line-height: 24px;
    font-size: 12px;
    margin: 0 2px;
  }

  :deep(.el-pagination .btn-prev),
  :deep(.el-pagination .btn-next) {
    min-width: 24px;
    height: 24px;
    line-height: 24px;
    font-size: 12px;
    margin: 0 2px;
  }

  :deep(.el-pagination .el-pagination__sizes) {
    margin: 0 5px 0 0;
  }

  :deep(.el-pagination .el-pagination__sizes .el-select .el-input) {
    width: 80px;
  }

  :deep(.el-pagination .el-pagination__sizes .el-select .el-input .el-input__inner) {
    height: 24px;
    line-height: 24px;
    font-size: 12px;
    padding: 0 8px;
  }

  :deep(.el-pagination .el-pagination__total) {
    margin-right: 5px;
    font-size: 12px;
  }

  :deep(.el-pagination .el-pagination__jump) {
    margin-left: 5px;
    font-size: 12px;
  }

  :deep(.el-pagination .el-pagination__jump .el-input) {
    width: 40px;
  }

  :deep(.el-pagination .el-pagination__jump .el-input .el-input__inner) {
    height: 24px;
    line-height: 24px;
    font-size: 12px;
    padding: 0 4px;
    text-align: center;
  }
}

.detail-item {
  margin-bottom: 10px;
  font-size: 14px;
  .label {
    font-weight: bold;
    color: #606266;
    margin-right: 8px;
  }
  .content {
    color: #303133;
  }
}
</style>
