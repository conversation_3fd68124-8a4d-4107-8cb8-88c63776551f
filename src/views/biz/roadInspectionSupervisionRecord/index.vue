<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="任务名称" prop="taskName">
              <el-input v-model="queryParams.taskName" placeholder="请输入任务名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="航线名称" prop="routeName">
              <el-input v-model="queryParams.routeName" placeholder="请输入航线名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="时间范围" prop="timeRange">
              <el-date-picker
                v-model="queryParams.timeRange"
                type="daterange"
                value-format="YYYY-MM-DD HH:mm:ss"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                range-separator="至"
                clearable
                @change="handleTimeRangeChange"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['biz:flightTaskResult:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="flightTaskResultList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="任务名称" align="center" prop="taskName" />
        <el-table-column label="航线" align="center" prop="routeName" />
        <el-table-column label="巡检开始时间" align="center" prop="actualStartTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.actualStartTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="巡检结束时间" align="center" prop="actualEndTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.actualEndTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>

        <el-table-column label="监管记录" align="center" prop="taskStatus">
          <template #default="scope">
            <el-tooltip content="查看" placement="top">
              <el-button link type="primary" @click="handleSupervisionRecord(scope.row)" v-hasPermi="['biz:flightTaskResult:list']">查看</el-button>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="报告" placement="top">
              <el-button link type="primary" @click="viewReport(scope.row)" v-hasPermi="['biz:flightTaskResult:list']">报告</el-button>
            </el-tooltip>
            <!-- <el-tooltip content="详情" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['biz:flightTaskResult:edit']">详情</el-button>
            </el-tooltip> -->
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>

    <!-- 监管记录弹窗 -->
    <el-dialog
      title="监管记录"
      v-model="detailDialogVisible"
      width="70%"
      append-to-body
      destroy-on-close
    >
      <SupervisionRecordDetail
        v-if="detailDialogVisible"
        :task-result-id="currentTaskResultId"
        @close-dialog="detailDialogVisible = false"
      />
    </el-dialog>
    <!-- BaskReport报表预览弹窗 -->
    <el-dialog
      title="报告详情"
      v-model="reportDialogVisible"
      width="900px"
      append-to-body
      destroy-on-close
      :close-on-click-modal="false"
      draggable
      align-center
    >
      <BaskReportPreview
        v-if="reportDialogVisible"
        :reportCode="'jx_jt_zy_report'"
        :parameters="reportParameters"
        :showToolbar="true"
        :height="'700px'"
        :fileName="'巡检报告'"
      />
    </el-dialog>
  </div>
</template>

<script setup name="FlightTaskResult" lang="ts">
// BaskReport组件导入
import BaskReportPreview from '@/components/BaskReportPreview/index.vue';

import {
  listFlightTaskResult,
  getFlightTaskResult,
  delFlightTaskResult,
  addFlightTaskResult,
  updateFlightTaskResult
} from '@/api/biz/flightTaskResult';
import { FlightTaskResultVO, FlightTaskResultQuery, FlightTaskResultForm } from '@/api/biz/flightTaskResult/types';
import SupervisionRecordDetail from './detail.vue';
import { listAccidentFlightTaskResult } from '@/api/biz/roadAccidentInfo';
import { listSupervisionFlightTaskResult } from '@/api/biz/roadInspectionSupervisionRecord'; // 导入详情组件

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { flight_task_type, flight_task_frequency, flight_task_status, flight_task_vector_operation } = toRefs<any>(proxy?.useDict('flight_task_type', 'flight_task_frequency', 'flight_task_status', 'flight_task_vector_operation'));

const flightTaskResultList = ref<FlightTaskResultVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(false);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const flightTaskResultFormRef = ref<ElFormInstance>();

const detailDialogVisible = ref(false); // 控制详情弹窗的显示
const currentTaskResultId = ref<string | number | null>(null); // 当前查看的病害ID

// BaskReport相关响应式变量
const reportDialogVisible = ref(false);
const reportParameters = ref({});

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: FlightTaskResultForm = {
  id: undefined,
  taskId: undefined,
  taskName: undefined,
  taskNo: undefined,
  taskType: undefined,
  taskFrequency: undefined,
  plannedStartTime: undefined,
  plannedEndTime: undefined,
  dailyScheduledStartTime: undefined,
  dailyScheduledEndTime: undefined,
  batteryCapacityReached: undefined,
  storageCapacityReached: undefined,
  routeId: undefined,
  routeName: undefined,
  executionDeviceId: undefined,
  executionDeviceName: undefined,
  executor: undefined,
  estimatedPayload: undefined,
  notes: undefined,
  taskStatus: undefined,
  actualStartTime: undefined,
  actualEndTime: undefined,
  returnAltitude: undefined,
  vectorOperation: undefined,
}
const data = reactive<PageData<FlightTaskResultForm, FlightTaskResultQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    taskId: undefined,
    taskName: undefined,
    taskNo: undefined,
    taskType: undefined,
    taskFrequency: undefined,
    plannedStartTime: undefined,
    plannedEndTime: undefined,
    dailyScheduledStartTime: undefined,
    dailyScheduledEndTime: undefined,
    batteryCapacityReached: undefined,
    storageCapacityReached: undefined,
    routeId: undefined,
    routeName: undefined,
    executionDeviceId: undefined,
    executionDeviceName: undefined,
    executor: undefined,
    estimatedPayload: undefined,
    notes: undefined,
    taskStatus: undefined,
    actualStartTime: undefined,
    actualEndTime: undefined,
    returnAltitude: undefined,
    vectorOperation: undefined,
    timeRange: undefined, // 添加时间范围字段
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "任务执行结果ID，主键不能为空", trigger: "blur" }
    ],
    taskId: [
      { required: true, message: "任务计划id不能为空", trigger: "blur" }
    ],
    taskName: [
      { required: true, message: "任务名称不能为空", trigger: "blur" }
    ],
    taskNo: [
      { required: true, message: "任务编号不能为空", trigger: "blur" }
    ],
    taskType: [
      { required: true, message: "任务类型不能为空", trigger: "change" }
    ],
    taskFrequency: [
      { required: true, message: "任务频率不能为空", trigger: "change" }
    ],
    plannedStartTime: [
      { required: true, message: "任务计划开始时间不能为空", trigger: "blur" }
    ],
    plannedEndTime: [
      { required: true, message: "任务计划结束时间不能为空", trigger: "blur" }
    ],

  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询飞行任务结果记录列表 */
const getList = async () => {
  loading.value = true;
  const res = await listSupervisionFlightTaskResult(queryParams.value);
  flightTaskResultList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  flightTaskResultFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  queryParams.value.timeRange = undefined;
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: FlightTaskResultVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加飞行任务结果记录";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: FlightTaskResultVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getFlightTaskResult(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改飞行任务结果记录";
}

/** 提交按钮 */
const submitForm = () => {
  flightTaskResultFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateFlightTaskResult(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addFlightTaskResult(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: FlightTaskResultVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除飞行任务结果记录编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delFlightTaskResult(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('biz/flightTaskResult/export', {
    ...queryParams.value
  }, `flightTaskResult_${new Date().getTime()}.xlsx`)
}

const viewReport = async (row: FlightTaskResultVO) => {
  try {
    // 设置报表参数
    reportParameters.value = { taskNo: row.taskNo };
    // 显示报表预览弹窗
    reportDialogVisible.value = true;
  } catch (error) {
    console.error('加载报告失败:', error);
    proxy?.$modal.msgError('加载报告失败，请稍后再试。');
  }
};

const handleSupervisionRecord = (row: any) => {
  currentTaskResultId.value = row.taskResultId || row.id;
  detailDialogVisible.value = true;
};

onMounted(() => {
  getList();
});


/** 处理时间范围变化 */
const handleTimeRangeChange = (val: any) => {
  if (val) {
    // 设置开始时间为当天的00:00:00
    queryParams.value.actualStartTime = val[0].substring(0, 10) + ' 00:00:00';
    // 设置结束时间为当天的23:59:59
    queryParams.value.actualEndTime = val[1].substring(0, 10) + ' 23:59:59';
  } else {
    queryParams.value.actualStartTime = undefined;
    queryParams.value.actualEndTime = undefined;
  }
}
</script>
