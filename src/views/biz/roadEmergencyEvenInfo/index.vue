<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="巡检任务" prop="taskResultName">
              <el-input v-model="queryParams.taskResultName" placeholder="请输入巡检任务名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="事件时间" style="width: 308px">
              <el-date-picker
                v-model="dateRangeEventTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              />
            </el-form-item>
            <el-form-item label="项目名称" prop="projectName">
              <el-input v-model="queryParams.projectName" placeholder="请输入项目名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="桩号" prop="pileNo">
              <el-input v-model="queryParams.pileNo" placeholder="请输入桩号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="是否推送" prop="isPush">
              <el-select v-model="queryParams.isPush" placeholder="请选择是否推送" clearable >
                <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <!-- <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['biz:roadEmergencyEvenInfo:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['biz:roadEmergencyEvenInfo:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['biz:roadEmergencyEvenInfo:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['biz:roadEmergencyEvenInfo:export']">导出</el-button>
          </el-col> -->
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="roadEmergencyEvenInfoList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <!-- <el-table-column label="巡检任务" align="center" prop="taskResultName" /> -->
        <el-table-column label="事件时间" align="center" prop="eventTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.eventTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="事件类型" align="center" prop="evenType" />
        <el-table-column label="图片" align="center" prop="ossIdUrl" width="100">
          <template #default="scope">
            <image-preview :src="scope.row.ossIdUrl" :width="50" :height="50"/>
          </template>
        </el-table-column>
<!--        <el-table-column label="经纬度" align="center">-->
<!--          <template #default="scope">-->
<!--            <span>{{ scope.row.latitude }}, {{ scope.row.longitude }}</span>-->
<!--          </template>-->
<!--        </el-table-column>-->
        <el-table-column label="项目名称" align="center" prop="projectName" />
        <el-table-column label="桩号" align="center" prop="pileNo" />
        <!-- <el-table-column label="置信度" align="center" prop="confidence" /> -->
        <!-- <el-table-column label="是否确认" align="center" prop="isConfirmed" >
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.isPush"/>
          </template>
        </el-table-column> -->
        <el-table-column label="是否推送" align="center" prop="isPush">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.isPush"/>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary"  @click="handleUpdate(scope.row)" v-hasPermi="['biz:roadEmergencyEvenInfo:edit']">修改</el-button>
            </el-tooltip>
            <!-- <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['biz:roadEmergencyEvenInfo:remove']"></el-button>
            </el-tooltip> -->
<!--            <el-tooltip content="报告" placement="top">-->
<!--              <el-button link type="primary"  @click="viewReport(scope.row)" v-hasPermi="['biz:flightTaskResult:edit']">报告</el-button>-->
<!--            </el-tooltip>-->
             <el-tooltip content="短信通知" placement="top">
              <el-button
                link
                type="primary"
                @click="handleSendSmsNotify(scope.row)"
                v-hasPermi="['biz:roadEmergencyEvenInfo:edit']"
              >短信通知</el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改应急事件对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="roadEmergencyEvenInfoFormRef" :model="form" :rules="rules" label-width="80px">
        <!-- <el-form-item label="巡检任务id" prop="taskResultId">
          <el-input v-model="form.taskResultId" placeholder="请输入巡检任务id" />
        </el-form-item>
        <el-form-item label="巡检任务名称" prop="taskResultName">
          <el-input v-model="form.taskResultName" placeholder="请输入巡检任务名称" />
        </el-form-item> -->
        <el-form-item label="事件时间" prop="eventTime">
          <el-date-picker clearable
            v-model="form.eventTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择事件时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="纬度" prop="latitude">
          <el-input v-model="form.latitude" placeholder="请输入纬度" />
        </el-form-item>
        <el-form-item label="经度" prop="longitude">
          <el-input v-model="form.longitude" placeholder="请输入经度" />
        </el-form-item>
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model="form.projectName" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="桩号" prop="pileNo">
          <el-input v-model="form.pileNo" placeholder="请输入桩号" />
        </el-form-item>
        <el-form-item label="图片URL" prop="imageUrl">
            <el-input v-model="form.imageUrl" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="置信度" prop="confidence">
          <el-input v-model="form.confidence" placeholder="请输入置信度" />
        </el-form-item>
        <el-form-item label="是否推送" prop="isPush">
          <el-select v-model="form.isPush" placeholder="请选择是否推送">
            <el-option
                v-for="dict in sys_yes_no"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- BaskReport报表预览弹窗 -->
    <el-dialog
      title="报告详情"
      v-model="reportDialogVisible"
      width="900px"
      append-to-body
      destroy-on-close
      :close-on-click-modal="false"
      draggable
      align-center
    >
      <BaskReportPreview
        v-if="reportDialogVisible"
        :reportCode="'jx_jt_report'"
        :parameters="reportParameters"
        :showToolbar="true"
        :height="'700px'"
        :fileName="'应急事件报告'"
      />
    </el-dialog>
  </div>
</template>

<script setup name="RoadEmergencyEvenInfo" lang="ts">
// BaskReport组件导入
import BaskReportPreview from '@/components/BaskReportPreview/index.vue';

import {
  listRoadEmergencyEvenInfo,
  getRoadEmergencyEvenInfo,
  delRoadEmergencyEvenInfo,
  addRoadEmergencyEvenInfo,
  updateRoadEmergencyEvenInfo,
  pushEvenSms
} from '@/api/biz/roadEmergencyEvenInfo';
import { RoadEmergencyEvenInfoVO, RoadEmergencyEvenInfoQuery, RoadEmergencyEvenInfoForm } from '@/api/biz/roadEmergencyEvenInfo/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_yes_no } = toRefs<any>(proxy?.useDict('sys_yes_no'));

const roadEmergencyEvenInfoList = ref<RoadEmergencyEvenInfoVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(false);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRangeEventTime = ref<[DateModelType, DateModelType]>(['', '']);

// BaskReport相关响应式变量
const reportDialogVisible = ref(false);
const reportParameters = ref({});

const queryFormRef = ref<ElFormInstance>();
const roadEmergencyEvenInfoFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: RoadEmergencyEvenInfoForm = {
  id: undefined,
  taskResultId: undefined,
  taskResultName: undefined,
  eventTime: undefined,
  evenType: undefined,
  latitude: undefined,
  longitude: undefined,
  projectName: undefined,
  pileNo: undefined,
  imageUrl: undefined,
  confidence: undefined,
  isConfirmed: undefined,
  isPush: undefined,
}
const data = reactive<PageData<RoadEmergencyEvenInfoForm, RoadEmergencyEvenInfoQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    taskResultName: undefined,
    evenType: undefined,
    latitude: undefined,
    longitude: undefined,
    projectName: undefined,
    pileNo: undefined,
    imageUrl: undefined,
    confidence: undefined,
    isConfirmed: undefined,
    isPush: undefined,
    params: {
      eventTime: undefined,
    }
  },
  rules: {
    id: [
      { required: true, message: "ID不能为空", trigger: "blur" }
    ],
    evenType: [
      { required: true, message: "事件类型不能为空", trigger: "change" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询应急事件列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeEventTime.value, 'EventTime');
  const res = await listRoadEmergencyEvenInfo(queryParams.value);
  roadEmergencyEvenInfoList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  roadEmergencyEvenInfoFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeEventTime.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: RoadEmergencyEvenInfoVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加应急事件";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: RoadEmergencyEvenInfoVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getRoadEmergencyEvenInfo(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改应急事件";
}

/** 提交按钮 */
const submitForm = () => {
  roadEmergencyEvenInfoFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateRoadEmergencyEvenInfo(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addRoadEmergencyEvenInfo(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: RoadEmergencyEvenInfoVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除应急事件编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delRoadEmergencyEvenInfo(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}


/** 发送短信按钮操作 */
const handleSendSmsNotify = async (row?: RoadEmergencyEvenInfoVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认发送短信通知？').finally(() => loading.value = false);
  await pushEvenSms(_ids);
  proxy?.$modal.msgSuccess('发送成功');
  await getList();
}


/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('biz/roadEmergencyEvenInfo/export', {
    ...queryParams.value
  }, `roadEmergencyEvenInfo_${new Date().getTime()}.xlsx`)
}

const viewReport = async (row: RoadEmergencyEvenInfoVO) => {
  try {
    // 设置报表参数
    reportParameters.value = { eventId: row.id };
    // 显示报表预览弹窗
    reportDialogVisible.value = true;
  } catch (error) {
    console.error('加载报告失败:', error);
    proxy?.$modal.msgError('加载报告失败，请稍后再试。');
  }
};

onMounted(() => {
  getList();
});
</script>
