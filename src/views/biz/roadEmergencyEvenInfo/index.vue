<template>
  <div class="p-2">
    <el-tabs v-model="activeTab" @tab-change="handleTabChange">
      <!-- 路面病害标签页 -->
      <el-tab-pane label="路面病害" name="disease">
        <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
          <div v-show="showSearch" class="mb-[10px]">
            <el-card shadow="hover">
              <el-form ref="queryFormRef" :model="queryParams" :inline="true">
                <el-form-item label="巡检任务" prop="taskResultName">
                  <el-input v-model="queryParams.taskResultName" placeholder="请输入巡检任务名称" clearable @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item label="事件时间" style="width: 308px">
                  <el-date-picker
                    v-model="dateRangeEventTime"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    type="daterange"
                    range-separator="-"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                  />
                </el-form-item>
                <el-form-item label="项目名称" prop="projectName">
                  <el-input v-model="queryParams.projectName" placeholder="请输入项目名称" clearable @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item label="桩号" prop="pileNo">
                  <el-input v-model="queryParams.pileNo" placeholder="请输入桩号" clearable @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item label="是否推送" prop="isPush">
                  <el-select v-model="queryParams.isPush" placeholder="请选择是否推送" clearable >
                    <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value"/>
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                  <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                </el-form-item>
              </el-form>
            </el-card>
          </div>
        </transition>

        <el-card shadow="never">
          <template #header>
            <el-row :gutter="10" class="mb8">
              <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
            </el-row>
          </template>

          <el-table v-loading="loading" border :data="roadEmergencyEvenInfoList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="事件时间" align="center" prop="eventTime" width="180">
              <template #default="scope">
                <span>{{ parseTime(scope.row.eventTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="事件类型" align="center" prop="evenType" />
            <el-table-column label="图片" align="center" prop="ossIdUrl" width="100">
              <template #default="scope">
                <image-preview :src="scope.row.ossIdUrl" :width="50" :height="50"/>
              </template>
            </el-table-column>
            <el-table-column label="项目名称" align="center" prop="projectName" />
            <el-table-column label="桩号" align="center" prop="pileNo" />
            <el-table-column label="是否推送" align="center" prop="isPush">
              <template #default="scope">
                <dict-tag :options="sys_yes_no" :value="scope.row.isPush"/>
              </template>
            </el-table-column>

            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
              <template #default="scope">
                <el-tooltip content="修改" placement="top">
                  <el-button link type="primary"  @click="handleUpdate(scope.row)" v-hasPermi="['biz:roadEmergencyEvenInfo:edit']">修改</el-button>
                </el-tooltip>
                <el-tooltip content="短信通知" placement="top">
                  <el-button
                    link
                    type="primary"
                    @click="handleSendSmsNotify(scope.row)"
                    v-hasPermi="['biz:roadEmergencyEvenInfo:edit']"
                  >短信通知</el-button>
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>

          <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
        </el-card>

        <!-- 添加或修改应急事件对话框 -->
        <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
          <el-form ref="roadEmergencyEvenInfoFormRef" :model="form" :rules="rules" label-width="80px">
            <el-form-item label="事件时间" prop="eventTime">
              <el-date-picker clearable
                              v-model="form.eventTime"
                              type="datetime"
                              value-format="YYYY-MM-DD HH:mm:ss"
                              placeholder="请选择事件时间">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="纬度" prop="latitude">
              <el-input v-model="form.latitude" placeholder="请输入纬度" />
            </el-form-item>
            <el-form-item label="经度" prop="longitude">
              <el-input v-model="form.longitude" placeholder="请输入经度" />
            </el-form-item>
            <el-form-item label="项目名称" prop="projectName">
              <el-input v-model="form.projectName" placeholder="请输入项目名称" />
            </el-form-item>
            <el-form-item label="桩号" prop="pileNo">
              <el-input v-model="form.pileNo" placeholder="请输入桩号" />
            </el-form-item>
            <el-form-item label="图片URL" prop="imageUrl">
              <el-input v-model="form.imageUrl" type="textarea" placeholder="请输入内容" />
            </el-form-item>
            <el-form-item label="置信度" prop="confidence">
              <el-input v-model="form.confidence" placeholder="请输入置信度" />
            </el-form-item>
            <el-form-item label="是否推送" prop="isPush">
              <el-select v-model="form.isPush" placeholder="请选择是否推送">
                <el-option
                  v-for="dict in sys_yes_no"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <template #footer>
            <div class="dialog-footer">
              <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
              <el-button @click="cancel">取 消</el-button>
            </div>
          </template>
        </el-dialog>

        <!-- BaskReport报表预览弹窗 -->
        <el-dialog
          title="报告详情"
          v-model="reportDialogVisible"
          width="900px"
          append-to-body
          destroy-on-close
          :close-on-click-modal="false"
          draggable
          align-center
        >
          <BaskReportPreview
            v-if="reportDialogVisible"
            :reportCode="'jx_jt_report'"
            :parameters="reportParameters"
            :showToolbar="true"
            :height="'700px'"
            :fileName="'应急事件报告'"
          />
        </el-dialog>
      </el-tab-pane>

      <!-- 抛洒物标签页 -->
      <el-tab-pane label="抛洒物" name="throw">
        <el-card shadow="never" class="filter-card">
          <el-form ref="throwQueryFormRef" :model="throwQueryParams" :inline="true" size="small" class="compact-form">
            <el-form-item label="抛洒物类型" prop="throwType" label-width="90px">
              <el-input v-model="throwQueryParams.throwType" placeholder="请输入抛洒物类型" style="width: 140px" />
            </el-form-item>
            <el-form-item label="确认状态" prop="throwConfirmed">
              <el-select v-model="throwQueryParams.throwConfirmed" placeholder="请选择">
                <el-option label="全部" value="" />
                <el-option label="已确认" value="1" />
                <el-option label="未确认" value="0" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleThrowQuery" size="small">搜索</el-button>
              <el-button icon="Refresh" @click="resetThrowQuery" size="small">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <el-card shadow="never" class="image-card">
          <el-table :data="throwList" border v-loading="throwLoading" size="small" class="compact-table" style="margin-top: 8px">
            <el-table-column type="index" label="序号" width="50" align="center" />
            <el-table-column label="抛洒物类型" min-width="140" align="center" label-class-name="nowrap-header">
              <template #default="scope">
                <dict-tag :options="ai_image_tag" :value="scope.row.throwType" />
              </template>
            </el-table-column>
            <el-table-column prop="latitude" label="纬度" min-width="80" align="center" />
            <el-table-column prop="longitude" label="经度" min-width="80" align="center" />
            <el-table-column prop="pileNo" label="桩号" min-width="80" align="center" />
            <el-table-column prop="eventTime" label="发生时间" min-width="120" align="center" />
            <el-table-column prop="roadNum" label="路线编号" min-width="80" align="center" />
            <el-table-column prop="remark" label="备注" min-width="100" align="center" />
            <el-table-column label="识别结果确认" width="100" align="center" fixed="right" v-hasPermi="['biz:roadThrowObjectInfo:edit']">
              <template #default="scope">
                <el-switch
                  v-model="scope.row.throwConfirmed"
                  :active-value="'1'"
                  :inactive-value="'0'"
                  inline-prompt
                  active-text="是"
                  inactive-text="否"
                  @change="(val) => handleThrowConfirmedChange(scope.row, val)"
                />
              </template>
            </el-table-column>
            <el-table-column prop="imageUrl" label="图片" min-width="80" align="center">
              <template #default="scope">
                <el-image
                  :src="scope.row.imageUrl"
                  fit="cover"
                  style="width: 40px; height: 40px; border-radius: 3px"
                  :initial-index="0"
                  @click="openPreview(scope.row)"
                />
              </template>
            </el-table-column>
          </el-table>
          <div class="table-footer">
            <div class="pagination-section">
              <pagination
                v-show="throwTotal > 0"
                :total="throwTotal"
                v-model:page="throwQueryParams.pageNum"
                v-model:limit="throwQueryParams.pageSize"
                @pagination="getThrowList"
                class="compact-pagination"
              />
            </div>
            <div v-if="throwStatistics.length > 0" class="statistics-section">
              <div class="statistics-row">
                <div class="statistics-item-inline statistics-total">
                  <div class="statistics-label-inline">抛洒物问题</div>
                  <div class="statistics-value-inline">{{ throwStatistics.reduce((sum, item) => sum + item.count, 0) }}</div>
                </div>
                <div v-for="item in throwStatistics" :key="item.throwType" class="statistics-item-inline">
                  <div class="statistics-label-inline">
                    <dict-tag :options="ai_image_tag" :value="item.throwType" />
                  </div>
                  <div class="statistics-value-inline">{{ item.count }}</div>
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 图片预览弹窗 -->
        <PreviewModal v-model="previewVisible" :image-url="previewImageUrl" :labels="currentLabels" :showResultsArea="showResultsArea" @closed="handlePreviewClose" @confirmStatusChange="handleConfirmStatusChange"/>
      </el-tab-pane>

      <!-- 侵占路权标签页 -->
      <el-tab-pane label="侵占路权" name="occupation">
        <el-card shadow="never" class="filter-card">
          <el-form ref="occupationQueryFormRef" :model="occupationQueryParams" :inline="true" size="small" class="compact-form">
            <el-form-item label="侵占类型" prop="occupationType">
              <el-input v-model="occupationQueryParams.occupationType" placeholder="请输入侵占类型" style="width: 140px" />
            </el-form-item>
            <el-form-item label="确认状态" prop="occupationConfirmed">
              <el-select v-model="occupationQueryParams.occupationConfirmed" placeholder="请选择">
                <el-option label="全部" value="" />
                <el-option label="已确认" value="1" />
                <el-option label="未确认" value="0" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleOccupationQuery" size="small">搜索</el-button>
              <el-button icon="Refresh" @click="resetOccupationQuery" size="small">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <el-card shadow="never" class="image-card">
          <el-table :data="occupationList" border v-loading="occupationLoading" size="small" class="compact-table" style="margin-top: 8px">
            <el-table-column type="index" label="序号" width="50" align="center" />
            <el-table-column label="侵占类型" min-width="100" align="center">
              <template #default="scope">
                <dict-tag :options="ai_image_tag" :value="scope.row.occupationType" />
              </template>
            </el-table-column>
            <el-table-column prop="latitude" label="纬度" min-width="80" align="center" />
            <el-table-column prop="longitude" label="经度" min-width="80" align="center" />
            <el-table-column prop="pileNo" label="桩号" min-width="80" align="center" />
            <el-table-column prop="eventTime" label="发生时间" min-width="120" align="center" />
            <el-table-column prop="roadNum" label="路线编号" min-width="80" align="center" />
            <el-table-column prop="remark" label="备注" min-width="100" align="center" />
            <el-table-column label="识别结果确认" width="100" align="center" fixed="right" v-hasPermi="['biz:roadRightOccupationInfo:edit']">
              <template #default="scope">
                <el-switch
                  v-model="scope.row.occupationConfirmed"
                  :active-value="'1'"
                  :inactive-value="'0'"
                  inline-prompt
                  active-text="是"
                  inactive-text="否"
                  @change="(val) => handleOccupationConfirmedChange(scope.row, val)"
                />
              </template>
            </el-table-column>
            <el-table-column prop="imageUrl" label="图片" min-width="80" align="center">
              <template #default="scope">
                <el-image
                  :src="scope.row.imageUrl"
                  fit="cover"
                  style="width: 40px; height: 40px; border-radius: 3px"
                  :initial-index="0"
                  @click="openPreview(scope.row)"
                />
              </template>
            </el-table-column>
          </el-table>
          <div class="table-footer">
            <div class="pagination-section">
              <pagination
                v-show="occupationTotal > 0"
                :total="occupationTotal"
                v-model:page="occupationQueryParams.pageNum"
                v-model:limit="occupationQueryParams.pageSize"
                @pagination="getOccupationList"
                class="compact-pagination"
              />
            </div>
            <div v-if="occupationStatistics.length > 0" class="statistics-section">
              <div class="statistics-row">
                <div class="statistics-item-inline statistics-total">
                  <div class="statistics-label-inline">侵占路权问题</div>
                  <div class="statistics-value-inline">{{ occupationStatistics.reduce((sum, item) => sum + item.count, 0) }}</div>
                </div>
                <div v-for="item in occupationStatistics" :key="item.occupationType" class="statistics-item-inline">
                  <div class="statistics-label-inline">
                    <dict-tag :options="ai_image_tag" :value="item.occupationType" />
                  </div>
                  <div class="statistics-value-inline">{{ item.count }}</div>
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 图片预览弹窗 -->
        <PreviewModal v-model="previewVisible" :image-url="previewImageUrl" :labels="currentLabels" :showResultsArea="showResultsArea" @closed="handlePreviewClose" @confirmStatusChange="handleConfirmStatusChange"/>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup name="RoadEmergencyEvenInfo" lang="ts">
// BaskReport组件导入
import BaskReportPreview from '@/components/BaskReportPreview/index.vue';
import PreviewModal from '@/components/PreviewModal/index.vue';
import { useImageLabels } from '@/hooks/useImageLabels';

import {
  listRoadEmergencyEvenInfo,
  getRoadEmergencyEvenInfo,
  delRoadEmergencyEvenInfo,
  addRoadEmergencyEvenInfo,
  updateRoadEmergencyEvenInfo,
  pushEvenSms
} from '@/api/biz/roadEmergencyEvenInfo';

import {
  listRoadThrowObjectInfo,
  getThrowObjectStatistics,
  updateRoadThrowObjectInfo
} from '@/api/biz/roadThrowObjectInfo';

import {
  listRoadRightOccupationInfo,
  getOccupationStatistics,
  updateRoadRightOccupationInfo
} from '@/api/biz/roadRightOccupationInfo';

import { RoadEmergencyEvenInfoVO, RoadEmergencyEvenInfoQuery, RoadEmergencyEvenInfoForm } from '@/api/biz/roadEmergencyEvenInfo/types';
import { RoadThrowObjectInfoVO, RoadThrowObjectInfoQuery, BizRoadThrowObjectStatisticsVo, RoadThrowObjectInfoForm } from '@/api/biz/roadThrowObjectInfo/types';
import { RoadRightOccupationInfoVO, RoadRightOccupationInfoQuery, BizRoadRightOccupationStatisticsVo, RoadRightOccupationInfoForm } from '@/api/biz/roadRightOccupationInfo/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_yes_no, ai_image_tag } = toRefs<any>(proxy?.useDict('sys_yes_no', 'ai_image_tag'));

// 使用图片标签 Hook
const { currentLabels, labelLoading, loadLabels } = useImageLabels();

const roadEmergencyEvenInfoList = ref<RoadEmergencyEvenInfoVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(false);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRangeEventTime = ref<[DateModelType, DateModelType]>(['', '']);
const activeTab = ref('disease');

// BaskReport相关响应式变量
const reportDialogVisible = ref(false);
const reportParameters = ref({});

const queryFormRef = ref<ElFormInstance>();
const roadEmergencyEvenInfoFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: RoadEmergencyEvenInfoForm = {
  id: undefined,
  taskResultId: undefined,
  taskResultName: undefined,
  eventTime: undefined,
  evenType: undefined,
  latitude: undefined,
  longitude: undefined,
  projectName: undefined,
  pileNo: undefined,
  imageUrl: undefined,
  confidence: undefined,
  isConfirmed: undefined,
  isPush: undefined,
}
const data = reactive<PageData<RoadEmergencyEvenInfoForm, RoadEmergencyEvenInfoQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    taskResultName: undefined,
    evenType: undefined,
    latitude: undefined,
    longitude: undefined,
    projectName: undefined,
    pileNo: undefined,
    imageUrl: undefined,
    confidence: undefined,
    isConfirmed: undefined,
    isPush: undefined,
    params: {
      eventTime: undefined,
    }
  },
  rules: {
    id: [
      { required: true, message: "ID不能为空", trigger: "blur" }
    ],
    evenType: [
      { required: true, message: "事件类型不能为空", trigger: "change" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询应急事件列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeEventTime.value, 'EventTime');
  const res = await listRoadEmergencyEvenInfo(queryParams.value);
  roadEmergencyEvenInfoList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  roadEmergencyEvenInfoFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeEventTime.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: RoadEmergencyEvenInfoVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加应急事件";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: RoadEmergencyEvenInfoVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getRoadEmergencyEvenInfo(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改应急事件";
}

/** 提交按钮 */
const submitForm = () => {
  roadEmergencyEvenInfoFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateRoadEmergencyEvenInfo(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addRoadEmergencyEvenInfo(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: RoadEmergencyEvenInfoVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除应急事件编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delRoadEmergencyEvenInfo(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 发送短信按钮操作 */
const handleSendSmsNotify = async (row?: RoadEmergencyEvenInfoVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认发送短信通知？').finally(() => loading.value = false);
  await pushEvenSms(_ids);
  proxy?.$modal.msgSuccess('发送成功');
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('biz/roadEmergencyEvenInfo/export', {
    ...queryParams.value
  }, `roadEmergencyEvenInfo_${new Date().getTime()}.xlsx`)
}

const viewReport = async (row: RoadEmergencyEvenInfoVO) => {
  try {
    // 设置报表参数
    reportParameters.value = { eventId: row.id };
    // 显示报表预览弹窗
    reportDialogVisible.value = true;
  } catch (error) {
    console.error('加载报告失败:', error);
    proxy?.$modal.msgError('加载报告失败，请稍后再试。');
  }
};

// 抛洒物相关
const throwList = ref<RoadThrowObjectInfoVO[]>([]);
const throwTotal = ref(0);
const throwLoading = ref(false);
const throwQueryParams = reactive<RoadThrowObjectInfoQuery>({
  pageNum: 1,
  pageSize: 10,
  taskResultId: undefined
});
const throwQueryFormRef = ref<ElFormInstance>();

// 抛洒物统计数据
const throwStatistics = ref<BizRoadThrowObjectStatisticsVo[]>([]);
const throwStatisticsLoading = ref(false);

const getThrowList = async () => {
  throwLoading.value = true;
  try {
    const res = await listRoadThrowObjectInfo(throwQueryParams);
    throwList.value = res.rows || [];
    throwTotal.value = res.total || 0;
  } catch (e) {
    throwList.value = [];
    throwTotal.value = 0;
  } finally {
    throwLoading.value = false;
  }
};

const fetchThrowStatistics = async () => {
  throwStatisticsLoading.value = true;
  try {
    const res = await getThrowObjectStatistics(throwQueryParams.taskResultId);
    throwStatistics.value = res.data || [];
  } catch (e) {
    console.error('获取抛洒物统计信息失败:', e);
    throwStatistics.value = [];
  } finally {
    throwStatisticsLoading.value = false;
  }
};

const handleThrowQuery = () => {
  throwQueryParams.pageNum = 1;
  getThrowList();
};

const resetThrowQuery = () => {
  throwQueryFormRef.value?.resetFields();
  throwQueryParams.pageNum = 1;
  getThrowList();
};

// 侵占路权相关
const occupationList = ref<RoadRightOccupationInfoVO[]>([]);
const occupationTotal = ref(0);
const occupationLoading = ref(false);
const occupationQueryParams = reactive<RoadRightOccupationInfoQuery>({
  pageNum: 1,
  pageSize: 10,
  taskResultId: undefined
});
const occupationQueryFormRef = ref<ElFormInstance>();

// 侵占路权统计数据
const occupationStatistics = ref<BizRoadRightOccupationStatisticsVo[]>([]);
const occupationStatisticsLoading = ref(false);

const getOccupationList = async () => {
  occupationLoading.value = true;
  try {
    const res = await listRoadRightOccupationInfo(occupationQueryParams);
    occupationList.value = res.rows || [];
    occupationTotal.value = res.total || 0;
  } catch (e) {
    occupationList.value = [];
    occupationTotal.value = 0;
  } finally {
    occupationLoading.value = false;
  }
};

const fetchOccupationStatistics = async () => {
  occupationStatisticsLoading.value = true;
  try {
    const res = await getOccupationStatistics(occupationQueryParams.taskResultId);
    occupationStatistics.value = res.data || [];
  } catch (e) {
    console.error('获取侵占路权统计信息失败:', e);
    occupationStatistics.value = [];
  } finally {
    occupationStatisticsLoading.value = false;
  }
};

const handleOccupationQuery = () => {
  occupationQueryParams.pageNum = 1;
  getOccupationList();
};

const resetOccupationQuery = () => {
  occupationQueryFormRef.value?.resetFields();
  occupationQueryParams.pageNum = 1;
  getOccupationList();
};

// 图片预览相关
const previewVisible = ref(false);
const previewImageUrl = ref('');
const showResultsArea = ref(false);

const openPreview = async (row: any) => {
  previewImageUrl.value = row.imageUrl;
  previewVisible.value = true;
  showResultsArea.value = false;

  if (row.imageUrl) {
    // 根据不同数据类型确定标签类型参数
    let labelType = '';
    if (row.labelType) {
      // 图片列表中的数据
      labelType = row.labelType;
    } else if (row.diseaseType) {
      // 路面病害数据
      labelType = row.diseaseType;
    } else if (row.evenType) {
      // 施工监管、道路灾害、交通事件数据
      labelType = row.evenType;
    } else if (row.occupationType) {
      // 侵占路权数据
      labelType = row.occupationType;
    } else if (row.throwType) {
      // 抛洒物数据
      labelType = row.throwType;
    }

    await loadLabels(row, labelType);
  }
};

const handlePreviewClose = () => {
  previewVisible.value = false;
  previewImageUrl.value = '';
};

// 处理确认状态变更
const handleConfirmStatusChange = (isConfirmed: boolean) => {
  // 这里可以处理确认状态变更的逻辑
};

// 抛洒物识别结果状态变更
const handleThrowConfirmedChange = async (row: RoadThrowObjectInfoVO, val: string) => {
  try {
    const updateData: RoadThrowObjectInfoForm = {
      id: row.id,
      throwConfirmed: val
    };
    await updateRoadThrowObjectInfo(updateData);
    proxy?.$modal.msgSuccess('抛洒物确认状态更新成功');
  } catch (error) {
    proxy?.$modal.msgError('更新抛洒物确认状态失败');
    // 恢复原状态
    row.throwConfirmed = row.throwConfirmed === '1' ? '0' : '1';
  }
};

// 侵占路权识别结果状态变更
const handleOccupationConfirmedChange = async (row: RoadRightOccupationInfoVO, val: string) => {
  try {
    const updateData: RoadRightOccupationInfoForm = {
      id: row.id,
      occupationConfirmed: val
    };
    await updateRoadRightOccupationInfo(updateData);
    proxy?.$modal.msgSuccess('侵占路权确认状态更新成功');
  } catch (error) {
    proxy?.$modal.msgError('更新侵占路权确认状态失败');
    // 恢复原状态
    row.occupationConfirmed = row.occupationConfirmed === '1' ? '0' : '1';
  }
};

const handleTabChange = (tabName: string) => {
  if (tabName === 'disease') {
    getList();
  } else if (tabName === 'throw') {
    getThrowList();
    fetchThrowStatistics();
  } else if (tabName === 'occupation') {
    getOccupationList();
    fetchOccupationStatistics();
  }
};

onMounted(() => {
  getList();
});
</script>

<style scoped>

/* 紧凑布局样式 */
.filter-card {
  margin-bottom: 8px;
}

.compact-form {
  margin-bottom: 0;
}

.compact-form .el-form-item {
  margin-bottom: 4px;
  margin-right: 8px;
}

.image-card {
  margin-top: 4px;
}

.compact-table {
  font-size: 12px;
}

.compact-table .el-table__header th {
  padding: 4px 0;
  font-size: 12px;
  font-weight: 600;
}

.compact-table .el-table__body td {
  padding: 2px 0;
}

.compact-pagination {
  margin-top: 6px;
}

.compact-pagination .el-pagination {
  justify-content: center;
  font-size: 12px;
}

/* 图片缩略图样式优化 */
.el-image {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.el-image:hover {
  transform: scale(1.05);
}

/* 表格行高度优化 */
.el-table--small .el-table__body td {
  padding: 0px 0;
}

/* 进一步压缩表格行高 */
.el-table--small .el-table__row {
  height: 20px;
}

.el-table--small .el-table__header th {
  padding: 0px 0;
  font-size: 9px;
}

/* 进一步压缩表格 */
.compact-table .el-table__header th {
  padding: 0px 0;
  font-size: 9px;
  font-weight: 600;
}

.compact-table .el-table__body td {
  padding: 0px 0;
  font-size: 9px;
}

.compact-table .el-table__row {
  height: 20px;
}

/* 搜索表单紧凑化 */
.el-form--inline .el-form-item {
  margin-right: 2px;
  margin-bottom: 1px;
}

/* 进一步压缩搜索表单 */
.compact-form .el-form-item {
  margin-bottom: 0px;
  margin-right: 3px;
}

.compact-form .el-form-item__label {
  padding-bottom: 0px;
  line-height: 1.0;
  font-size: 11px;
}

.compact-form .el-input__wrapper {
  padding: 0px 4px;
}

.compact-form .el-select .el-input__wrapper {
  padding: 0px 4px;
}

.compact-form .el-button {
  padding: 1px 6px;
  height: 20px;
  font-size: 10px;
}

.compact-form .el-form-item__content {
  line-height: 1.0;
}

/* 进一步减小卡片间距 */
.el-card {
  margin-bottom: 2px;
}

/* 压缩卡片头部 */
.el-card__header {
  padding: 4px 8px;
}

.el-card__body {
  padding: 4px 8px;
}

/* 优化表格内容显示 */
.compact-table .cell {
  padding: 4px 8px;
}

/* 减小分页器高度 */
.compact-pagination .el-pagination .el-pager li {
  min-width: 28px;
  height: 28px;
  line-height: 28px;
}

/* 弹窗高度控制 */
:deep(.el-dialog) {
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

:deep(.el-dialog__body) {
  flex: 1;
  overflow-y: auto;
  max-height: calc(90vh - 140px);
  padding: 20px;
}

/* 表格容器高度优化 */
.image-card .el-card__body {
  max-height: calc(90vh - 200px);
  overflow-y: auto;
  padding: 3px;
}

/* 确保表格在容器内正确显示 */
.compact-table {
  max-height: calc(90vh - 240px);
  overflow-y: auto;
}

/* 优化表格滚动条 */
.compact-table .el-table__body-wrapper {
  overflow-y: auto;
  max-height: calc(90vh - 270px);
}

/* 确保分页器在底部 */
.compact-pagination {
  margin-top: 3px;
  padding: 3px 0;
}

.compact-pagination .el-pagination {
  font-size: 9px;
}

.compact-pagination .el-pagination .el-pager li {
  min-width: 18px;
  height: 18px;
  line-height: 18px;
}

/* 优化表格头部固定 */
.compact-table .el-table__header-wrapper {
  position: sticky;
  top: 0;
  z-index: 1;
  background: #fafafa;
}

/* 改善表格行间距 */
.compact-table .el-table__row {
  height: 20px;
}

/* 优化表格内容对齐 */
.compact-table .cell {
  padding: 0px 2px;
  line-height: 0.9;
}

/* 确保弹窗内容可以滚动 */
:deep(.el-dialog__body) {
  overflow-y: auto;
  overflow-x: hidden;
}

/* 优化tabs样式 */
:deep(.el-tabs__content) {
  padding: 2px 0;
}

:deep(.el-tabs__header) {
  margin-bottom: 4px;
}

:deep(.el-tabs__nav-wrap) {
  padding: 0;
}

:deep(.el-tabs__item) {
  padding: 4px 12px;
  font-size: 16px;
  line-height: 1.2;
}

:deep(.el-tabs__header) {
  padding: 0;
}

/* 改善卡片间距 */
.image-card {
  margin-top: 2px;
  margin-bottom: 2px;
}

/* 统计卡片样式 */
.statistics-card {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-top: 4px;
}

.statistics-card .el-card__header {
  padding: 3px 8px;
}

.statistics-card .el-card__body {
  padding: 3px 8px;
}

.statistics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.statistics-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.statistics-content {
  padding: 6px 0;
}

.statistics-item {
  text-align: center;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 6px;
  margin-bottom: 6px;
}

.statistics-label {
  margin-bottom: 6px;
}

.statistics-value {
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
}

/* 一行内紧凑展示统计信息 */
.statistics-row {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.statistics-item-inline {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  min-width: fit-content;
}

.statistics-label-inline {
  margin-bottom: 0;
}

.statistics-value-inline {
  font-size: 16px;
  font-weight: 600;
  color: #409eff;
  min-width: 20px;
  text-align: center;
}

/* 总数项特殊样式 */
.statistics-total {
  background: #e6f7ff !important;
  border-color: #91d5ff !important;
  font-weight: 600;
}

.statistics-total .statistics-value-inline {
  color: #1890ff;
  font-size: 18px;
}

/* 表格底部布局 */
.table-footer {
  margin-top: 4px;
}

.pagination-section {
  margin-bottom: 4px;
}

.statistics-section {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.statistics-section .statistics-row {
  justify-content: flex-start;
  gap: 6px;
}

.statistics-section .statistics-item-inline {
  padding: 3px 6px;
  font-size: 11px;
}

.statistics-section .statistics-value-inline {
  font-size: 14px;
}

.statistics-section .statistics-total .statistics-value-inline {
  font-size: 16px;
}
.nowrap-header .cell {
  white-space: nowrap;
}
</style>
