<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="关联的隐患排查记录ID" prop="inspectRecordId">
              <el-input v-model="queryParams.inspectRecordId" placeholder="请输入关联的隐患排查记录ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="关联的隐患点ID" prop="hazardPointId">
              <el-input v-model="queryParams.hazardPointId" placeholder="请输入关联的隐患点ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="拍摄点纬度" prop="latitude">
              <el-input v-model="queryParams.latitude" placeholder="请输入拍摄点纬度" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="拍摄点经度" prop="longitude">
              <el-input v-model="queryParams.longitude" placeholder="请输入拍摄点经度" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="桩号" prop="pileNo">
              <el-input v-model="queryParams.pileNo" placeholder="请输入桩号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="路线编号" prop="roadNum">
              <el-input v-model="queryParams.roadNum" placeholder="请输入路线编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="是否存在风险：Y=是 N=否" prop="hasRisk">
              <el-input v-model="queryParams.hasRisk" placeholder="请输入是否存在风险：Y=是 N=否" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="风险备注" prop="riskNote">
              <el-input v-model="queryParams.riskNote" placeholder="请输入风险备注" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="AI识别置信度" prop="confidence">
              <el-input v-model="queryParams.confidence" placeholder="请输入AI识别置信度" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="识别结果是否确认" prop="recognitionResultConfirmed">
              <el-input v-model="queryParams.recognitionResultConfirmed" placeholder="请输入识别结果是否确认" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="事件发生/图片拍摄时间" prop="eventTime">
              <el-date-picker clearable
                v-model="queryParams.eventTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择事件发生/图片拍摄时间"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['biz:roadHazardRecordItem:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['biz:roadHazardRecordItem:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['biz:roadHazardRecordItem:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['biz:roadHazardRecordItem:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="roadHazardRecordItemList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="条目ID，主键" align="center" prop="id" v-if="true" />
        <el-table-column label="关联的隐患排查记录ID" align="center" prop="inspectRecordId" />
        <el-table-column label="关联的隐患点ID" align="center" prop="hazardPointId" />
        <el-table-column label="拍摄点纬度" align="center" prop="latitude" />
        <el-table-column label="拍摄点经度" align="center" prop="longitude" />
        <el-table-column label="桩号" align="center" prop="pileNo" />
        <el-table-column label="路线编号" align="center" prop="roadNum" />
        <el-table-column label="是否存在风险：Y=是 N=否" align="center" prop="hasRisk">
          <template #default="scope">
            <dict-tag :options="road_hazard_has_risk" :value="scope.row.hasRisk"/>
          </template>
        </el-table-column>
        <el-table-column label="风险备注" align="center" prop="riskNote" />
        <el-table-column label="AI识别置信度" align="center" prop="confidence" />
        <el-table-column label="识别结果是否确认" align="center" prop="recognitionResultConfirmed" />
        <el-table-column label="事件发生/图片拍摄时间" align="center" prop="eventTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.eventTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="隐患类型" align="center" prop="hazardType" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['biz:roadHazardRecordItem:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['biz:roadHazardRecordItem:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改公路隐患排查条目对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="roadHazardRecordItemFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="关联的隐患排查记录ID" prop="inspectRecordId">
          <el-input v-model="form.inspectRecordId" placeholder="请输入关联的隐患排查记录ID" />
        </el-form-item>
        <el-form-item label="关联的隐患点ID" prop="hazardPointId">
          <el-input v-model="form.hazardPointId" placeholder="请输入关联的隐患点ID" />
        </el-form-item>
        <el-form-item label="拍摄点纬度" prop="latitude">
          <el-input v-model="form.latitude" placeholder="请输入拍摄点纬度" />
        </el-form-item>
        <el-form-item label="拍摄点经度" prop="longitude">
          <el-input v-model="form.longitude" placeholder="请输入拍摄点经度" />
        </el-form-item>
        <el-form-item label="桩号" prop="pileNo">
          <el-input v-model="form.pileNo" placeholder="请输入桩号" />
        </el-form-item>
        <el-form-item label="路线编号" prop="roadNum">
          <el-input v-model="form.roadNum" placeholder="请输入路线编号" />
        </el-form-item>
        <el-form-item label="是否存在风险：Y=是 N=否" prop="hasRisk">
          <el-input v-model="form.hasRisk" placeholder="请输入是否存在风险：Y=是 N=否" />
        </el-form-item>
        <el-form-item label="风险备注" prop="riskNote">
          <el-input v-model="form.riskNote" placeholder="请输入风险备注" />
        </el-form-item>
        <el-form-item label="AI识别置信度" prop="confidence">
          <el-input v-model="form.confidence" placeholder="请输入AI识别置信度" />
        </el-form-item>
        <el-form-item label="识别结果是否确认" prop="recognitionResultConfirmed">
          <el-input v-model="form.recognitionResultConfirmed" placeholder="请输入识别结果是否确认" />
        </el-form-item>
        <el-form-item label="事件发生/图片拍摄时间" prop="eventTime">
          <el-date-picker clearable
            v-model="form.eventTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择事件发生/图片拍摄时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="RoadHazardRecordItem" lang="ts">
import { listRoadHazardRecordItem, getRoadHazardRecordItem, delRoadHazardRecordItem, addRoadHazardRecordItem, updateRoadHazardRecordItem } from '@/api/biz/roadHazardRecordItem';
import { RoadHazardRecordItemVO, RoadHazardRecordItemQuery, RoadHazardRecordItemForm } from '@/api/biz/roadHazardRecordItem/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const roadHazardRecordItemList = ref<RoadHazardRecordItemVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(false);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const roadHazardRecordItemFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: RoadHazardRecordItemForm = {
  id: undefined,
  inspectRecordId: undefined,
  hazardPointId: undefined,
  latitude: undefined,
  longitude: undefined,
  pileNo: undefined,
  roadNum: undefined,
  hasRisk: undefined,
  riskNote: undefined,
  confidence: undefined,
  recognitionResultConfirmed: undefined,
  eventTime: undefined,
  hazardType: undefined
}
const data = reactive<PageData<RoadHazardRecordItemForm, RoadHazardRecordItemQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    inspectRecordId: undefined,
    hazardPointId: undefined,
    latitude: undefined,
    longitude: undefined,
    pileNo: undefined,
    roadNum: undefined,
    hasRisk: undefined,
    riskNote: undefined,
    confidence: undefined,
    recognitionResultConfirmed: undefined,
    eventTime: undefined,
    hazardType: undefined,
    params: {
    }
  },
  rules: {
    inspectRecordId: [
      { required: true, message: "关联的隐患排查记录ID不能为空", trigger: "blur" }
    ],
    hazardPointId: [
      { required: true, message: "关联的隐患点ID不能为空", trigger: "blur" }
    ],
    latitude: [
      { required: true, message: "拍摄点纬度不能为空", trigger: "blur" }
    ],
    longitude: [
      { required: true, message: "拍摄点经度不能为空", trigger: "blur" }
    ],
    pileNo: [
      { required: true, message: "桩号不能为空", trigger: "blur" }
    ],
    eventTime: [
      { required: true, message: "事件发生/图片拍摄时间不能为空", trigger: "blur" }
    ],
    hazardType: [
      { required: true, message: "隐患类型不能为空", trigger: "change" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公路隐患排查条目列表 */
const getList = async () => {
  loading.value = true;
  const res = await listRoadHazardRecordItem(queryParams.value);
  roadHazardRecordItemList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  roadHazardRecordItemFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: RoadHazardRecordItemVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加公路隐患排查条目";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: RoadHazardRecordItemVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getRoadHazardRecordItem(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改公路隐患排查条目";
}

/** 提交按钮 */
const submitForm = () => {
  roadHazardRecordItemFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateRoadHazardRecordItem(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addRoadHazardRecordItem(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: RoadHazardRecordItemVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除公路隐患排查条目编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delRoadHazardRecordItem(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('biz/roadHazardRecordItem/export', {
    ...queryParams.value
  }, `roadHazardRecordItem_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
