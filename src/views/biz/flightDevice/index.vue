<template>
  <div class="p-2">
    <el-row :gutter="20">
      <!-- 部门树 -->
      <el-col :lg="4" :xs="24">
        <el-card shadow="hover">
          <el-input v-model="deptName" placeholder="请输入部门名称" prefix-icon="Search" clearable />
          <div class="mt-2 mb-2">
                          <el-button 
                size="small" 
                :type="!queryParams.deptId ? 'primary' : 'info'"
                :plain="!!queryParams.deptId"
                @click="handleShowAllDepts"
                style="width: 100%"
              >
                {{ queryParams.deptId ? '📋 显示全部设备' : '📋 当前：全部设备' }}
              </el-button>
          </div>
          <el-tree
            ref="deptTreeRef"
            node-key="id"
            :data="deptOptions"
            :props="{ label: 'label', children: 'children' } as any"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            highlight-current
            default-expand-all
            @node-click="handleNodeClick"
          />
        </el-card>
      </el-col>
      
      <el-col :lg="20" :xs="24">
        <!-- 标签页导航 -->
        <el-tabs v-model="activeTab" @tab-change="handleTabChange" class="mb-4">
          <el-tab-pane label="我的设备列表" name="own" />
          <el-tab-pane label="共享出去的设备" name="shared_out" />
          <el-tab-pane label="共享给我的设备" name="shared_in" />
        </el-tabs>

        <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
          <div v-show="showSearch" class="mb-[10px]">
            <el-card shadow="hover">
              <el-form ref="queryFormRef" :model="queryParams" :inline="true">
                <el-form-item label="飞行设备名称" prop="deviceName">
                  <el-input v-model="queryParams.deviceName" placeholder="请输入飞行设备名称" clearable @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item label="设备状态" prop="status">
                  <el-select v-model="queryParams.status" placeholder="请选择设备状态" clearable>
                    <el-option v-for="dict in device_status" :key="dict.value" :label="dict.label" :value="dict.value" />
                  </el-select>
                </el-form-item>
                <el-form-item label="控制设备名称" prop="controller">
                  <el-input v-model="queryParams.controller" placeholder="请输入控制设备名称" clearable @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item v-if="queryParams.deptId">
                  <el-tag type="info" closable @close="handleShowAllDepts">
                    部门筛选：{{ getCurrentDeptName() }}
                  </el-tag>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                  <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                </el-form-item>
              </el-form>
            </el-card>
          </div>
        </transition>

        <el-card shadow="never">
          <template #header>
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5" v-if="activeTab === 'own'">
                <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['biz:flightDevice:add']">新增</el-button>
              </el-col>
              <el-col :span="1.5" v-if="activeTab === 'own'">
                <el-button
                  type="success"
                  plain
                  icon="Edit"
                  :disabled="single"
                  @click="handleUpdate()"
                  v-hasPermi="['biz:flightDevice:edit']"
                >
                  修改
                </el-button>
              </el-col>
              <el-col :span="1.5" v-if="activeTab === 'own'">
                <el-button
                  type="danger"
                  plain
                  icon="Delete"
                  :disabled="multiple"
                  @click="handleDelete()"
                  v-hasPermi="['biz:flightDevice:remove']"
                >
                  删除
                </el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['biz:flightDevice:export']">导出</el-button>
              </el-col>
              <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
            </el-row>
          </template>

          <el-table v-loading="loading" border :data="flightDeviceList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" v-if="activeTab === 'own'" />
            <el-table-column label="主键ID" align="center" prop="id" v-if="true" />
            <el-table-column label="飞行设备名称" align="center" prop="nickname" />
            <el-table-column label="归属部门" align="center" v-if="activeTab === 'own' || activeTab === 'shared_out'">
              <template #default="scope">
                {{ scope.row.deptName || '-' }}
              </template>
            </el-table-column>
            <el-table-column label="设备部门" align="center" v-if="activeTab === 'shared_in'" width="120">
              <template #default="scope">
                <el-tooltip :content="getSharedDeviceDeptTooltip(scope.row)" placement="top">
                  <span>{{ getSharedDeviceDeptDisplay(scope.row) }}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column label="设备状态" align="center" prop="status">
              <template #default="scope">
                <dict-tag :options="device_status" :value="scope.row.status" />
              </template>
            </el-table-column>
            <el-table-column label="绑定状态" align="center" prop="boundStatus">
              <template #default="scope">
                <dict-tag :options="bound_status" :value="scope.row.boundStatus" />
              </template>
            </el-table-column>
            <el-table-column label="绑定时间" align="center" prop="boundTime">
              <template #default="scope">
                {{ scope.row.boundTime ? formatDate(scope.row.boundTime) : '-' }}
              </template>
            </el-table-column>
            <el-table-column label="最后登录时间" align="center" prop="loginTime">
              <template #default="scope">
                {{ scope.row.loginTime ? formatDate(scope.row.loginTime) : '-' }}
              </template>
            </el-table-column>
            <el-table-column label="经度" align="center" prop="longitude" />
            <el-table-column label="纬度" align="center" prop="latitude" />
            <el-table-column label="控制设备名称" align="center" prop="controller" />
            <!-- 共享出去的设备显示被授权方信息 -->
            <el-table-column label="被授权方" align="center" prop="targetTenantName" v-if="activeTab === 'shared_out'" />
            <el-table-column label="授权状态" align="center" prop="shareStatus" v-if="activeTab === 'shared_out'">
              <template #default="scope">
                <el-tag :type="scope.row.shareStatus === '1' ? 'success' : 'warning'">
                  {{ scope.row.shareStatus === '1' ? '已授权' : '待授权' }}
                </el-tag>
              </template>
            </el-table-column>
            <!-- 共享给我的设备显示拥有者信息 -->
            <el-table-column label="设备拥有者" align="center" prop="ownerTenantName" v-if="activeTab === 'shared_in'" />
            <el-table-column label="授权状态" align="center" prop="shareStatus" v-if="activeTab === 'shared_in'">
              <template #default="scope">
                <el-tag :type="scope.row.shareStatus === '1' ? 'success' : 'warning'">
                  {{ scope.row.shareStatus === '1' ? '可使用' : '不可用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="授权时间" align="center" prop="shareStartTime" v-if="activeTab === 'shared_in'" width="160">
              <template #default="scope">
                {{ scope.row.shareStartTime ? formatDate(scope.row.shareStartTime) : '-' }}
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
              <template #default="scope">
                <el-tooltip content="修改" placement="top" v-if="activeTab === 'own'">
                  <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['biz:flightDevice:edit']"></el-button>
                </el-tooltip>
                <el-tooltip content="删除" placement="top" v-if="activeTab === 'own'">
                  <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['biz:flightDevice:remove']"></el-button>
                </el-tooltip>
                <el-tooltip content="授权共享" placement="top" v-if="activeTab === 'own'">
                  <el-button
                    link
                    type="primary"
                    icon="Share"
                    @click="handleShare(scope.row)"
                    v-hasPermi="['biz:flightDeviceSharePermission:add']"
                  >
                    授权
                  </el-button>
                </el-tooltip>
                <el-tooltip content="批量授权" placement="top" v-if="activeTab === 'own'">
                  <el-button
                    link
                    type="primary"
                    icon="Share"
                    @click="handleBatchShare(scope.row)"
                    v-hasPermi="['biz:flightDeviceSharePermission:add']"
                  >
                    批量授权
                  </el-button>
                </el-tooltip>
                <!-- 共享出去的设备操作 -->
                <el-tooltip content="编辑授权" placement="top" v-if="activeTab === 'shared_out'">
                  <el-button
                    link
                    type="primary"
                    icon="Edit"
                    @click="handleEditShare(scope.row)"
                    v-hasPermi="['biz:flightDeviceSharePermission:edit']"
                  >
                    编辑
                  </el-button>
                </el-tooltip>
                <el-tooltip content="删除授权" placement="top" v-if="activeTab === 'shared_out'">
                  <el-button
                    link
                    type="danger"
                    icon="Delete"
                    @click="handleDeleteShare(scope.row)"
                    v-hasPermi="['biz:flightDeviceSharePermission:remove']"
                  >
                    删除
                  </el-button>
                </el-tooltip>
                <!-- 共享给我的设备操作 -->
                <el-tooltip content="申请使用" placement="top" v-if="activeTab === 'shared_in'">
                  <el-button link type="primary" icon="Position" @click="handleRequestAccess(scope.row)">申请使用</el-button>
                </el-tooltip>
                <el-tooltip content="历史申请记录" placement="top" v-if="activeTab === 'shared_in'">
                  <el-button link type="primary" icon="History" @click="handleHistoryRequest(scope.row)">历史申请记录</el-button>
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>

          <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 添加或修改飞行设备对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="680px" append-to-body>
      <el-form ref="flightDeviceFormRef" :model="form" :rules="rules" label-width="128px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="飞行设备名称" prop="deviceName">
              <el-input v-model="form.deviceName" placeholder="请输入飞行设备名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="归属部门" prop="deptId">
              <el-tree-select
                v-model="form.deptId"
                :data="enabledDeptOptions"
                :props="{ value: 'id', label: 'label', children: 'children' } as any"
                value-key="id"
                placeholder="请选择归属部门"
                check-strictly
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="设备状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择设备状态">
                <el-option v-for="dict in device_status" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="控制设备名称" prop="controller">
              <el-input v-model="form.controller" placeholder="请输入控制设备名称" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 授权共享弹窗 -->
    <FlightDeviceSharePermissionEdit
      :visible="shareDialogVisible"
      :edit-id="selectedSharePermissionId"
      :preset-device-id="selectedDeviceId"
      :preset-owner-tenant-id="userStore.tenantId"
      @success="handleShareSuccess"
      @close="handleShareClose"
    />

    <!-- 申请使用弹窗 -->
    <FlightDeviceUseApplicationEdit
      :visible="applicationDialogVisible"
      :preset-device-id="selectedDeviceForApplication?.id"
      :preset-owner-tenant-id="String(selectedDeviceForApplication?.ownerTenantId || '')"
      :device-name="selectedDeviceForApplication?.deviceName"
      :owner-tenant-name="selectedDeviceForApplication?.ownerTenantName"
      @success="handleApplicationSuccess"
      @close="handleApplicationClose"
    />

    <!-- 批量授权弹窗 -->
    <FlightDeviceBatchShareEdit
      :visible="batchShareDialogVisible"
      :device-id="selectedDeviceId"
      @success="handleBatchShareSubmit"
      @close="handleBatchShareClose"
    />
  </div>
</template>

<script setup name="FlightDevice" lang="ts">
import { watchEffect } from 'vue';
import {
  listFlightDevice,
  listSharedOutDevices,
  listSharedInDevices,
  getFlightDevice,
  delFlightDevice,
  addFlightDevice,
  updateFlightDevice,
  deptTree
} from '@/api/biz/flightDevice';
import { FlightDeviceVO, FlightDeviceQuery, FlightDeviceForm } from '@/api/biz/flightDevice/types';
import { listTenant } from '@/api/system/tenant';
  import { delFlightDeviceSharePermission, batchGrantFlightDeviceSharePermission } from '@/api/biz/flightDeviceSharePermission';
  import FlightDeviceSharePermissionEdit from '@/views/biz/flightDeviceSharePermission/edit.vue';
import { useUserStore } from '@/store/modules/user';
import FlightDeviceUseApplicationEdit from '@/views/biz/flightDeviceUseApplication/edit.vue';
import { useRouter } from 'vue-router';
import FlightDeviceBatchShareEdit from '@/views/biz/flightDeviceSharePermission/batch-edit.vue';
import { DeptTreeVO } from '@/api/system/dept/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { device_status, bound_status } = toRefs<any>(proxy?.useDict('device_status', 'bound_status'));
const userStore = useUserStore();
const router = useRouter();

const flightDeviceList = ref<FlightDeviceVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(false);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const activeTab = ref('own'); // 当前激活的标签页
const tenantMap = ref<Map<string, string>>(new Map()); // 租户ID到租户名称的映射
const deptMap = ref<Map<string, string>>(new Map()); // 部门ID到部门名称的映射

// 部门树相关
const deptName = ref('');
const deptOptions = ref<DeptTreeVO[]>([]);
const enabledDeptOptions = ref<DeptTreeVO[]>([]);
const deptTreeRef = ref<ElTreeInstance>();
const selectedDeptId = ref<string | number | undefined>(undefined); // 当前选中的部门ID

const queryFormRef = ref<ElFormInstance>();
const flightDeviceFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const shareDialogVisible = ref(false);
const selectedDeviceId = ref<string | number>('');
const selectedSharePermissionId = ref<string | number>('');

const applicationDialogVisible = ref(false);
const selectedDeviceForApplication = ref<FlightDeviceVO | null>(null);

const batchShareDialogVisible = ref(false);
const selectedDeviceIds = ref<Array<string | number>>([]);
const selectedTargetTenantIds = ref<string[]>([]);

const initFormData: FlightDeviceForm = {
  id: undefined,
  deviceName: undefined,
  status: undefined,
  controller: undefined,
  deptId: undefined
};

const data = reactive<PageData<FlightDeviceForm, FlightDeviceQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    deviceName: undefined,
    status: undefined,
    controller: undefined,
    deptId: undefined,
    params: {}
  },
  rules: {
    id: [{ required: true, message: '主键ID不能为空', trigger: 'blur' }],
    deviceName: [{ required: true, message: '飞行设备名称不能为空', trigger: 'blur' }],
    controller: [{ required: true, message: '控制设备名称不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 通过条件过滤节点 */
const filterNode = (value: string, data: any) => {
  if (!value) return true;
  return data.label.indexOf(value) !== -1;
};

/** 根据名称筛选部门树 */
watchEffect(
  () => {
    deptTreeRef.value?.filter(deptName.value);
  },
  {
    flush: 'post' // watchEffect会在DOM挂载或者更新之前就会触发，此属性控制在DOM元素更新后运行
  }
);

/** 查询部门下拉树结构 */
const getDeptTree = async () => {
  try {
    const res = await deptTree();
    deptOptions.value = res.data;
    enabledDeptOptions.value = filterDisabledDept(res.data);
    buildDeptMap(res.data);
  } catch (error) {
    console.error('获取部门树失败:', error);
  }
};

/** 过滤禁用的部门 */
const filterDisabledDept = (deptList: DeptTreeVO[]) => {
  return deptList.filter((dept) => {
    if (dept.disabled) {
      return false;
    }
    if (dept.children && dept.children.length) {
      dept.children = filterDisabledDept(dept.children);
    }
    return true;
  });
};

/** 构建部门ID到部门名称的映射 */
const buildDeptMap = (deptList: DeptTreeVO[]) => {
  const map = new Map<string, string>();
  
  const traverse = (depts: DeptTreeVO[]) => {
    depts.forEach((dept) => {
      map.set(String(dept.id), dept.label);
      if (dept.children && dept.children.length) {
        traverse(dept.children);
      }
    });
  };
  
  traverse(deptList);
  deptMap.value = map;
};

/** 节点单击事件 */
const handleNodeClick = (data: DeptTreeVO) => {
  // 如果点击的是已选中的部门，则取消选择
  if (selectedDeptId.value === data.id) {
    selectedDeptId.value = undefined;
    queryParams.value.deptId = undefined;
    deptTreeRef.value?.setCurrentKey(undefined);
  } else {
    // 选择新的部门
    selectedDeptId.value = data.id;
    queryParams.value.deptId = data.id;
  }
  handleQuery();
};

/** 显示全部设备（取消部门筛选） */
const handleShowAllDepts = () => {
  selectedDeptId.value = undefined;
  queryParams.value.deptId = undefined;
  deptTreeRef.value?.setCurrentKey(undefined);
  handleQuery();
};

/** 获取当前选中部门的名称 */
const getCurrentDeptName = () => {
  if (!queryParams.value.deptId) return '';
  return deptMap.value.get(String(queryParams.value.deptId)) || '未知部门';
};

/** 获取共享设备的部门显示名称 */
const getSharedDeviceDeptDisplay = (row: any) => {
  // 如果后端已经返回了部门名称，直接显示
  if (row.deptName) {
    return row.deptName;
  }
  
  // 如果有部门ID但没有部门名称，显示外部部门标识
  if (row.deptId) {
    return '外部部门';
  }
  
  // 没有部门信息
  return '-';
};

/** 获取共享设备的部门提示信息 */
const getSharedDeviceDeptTooltip = (row: any) => {
  // 如果后端已经返回了部门名称
  if (row.deptName) {
    return `部门：${row.deptName}${row.deptId ? ` (ID: ${row.deptId})` : ''}`;
  }
  
  // 如果有部门ID但没有部门名称
  if (row.deptId) {
    return `外部租户部门 (ID: ${row.deptId})\n由于权限限制，无法获取具体部门名称`;
  }
  
  // 没有部门信息
  return '该设备未设置部门信息';
};

/** 获取租户字典 */
const getTenantMap = async () => {
  try {
    const res = await listTenant({
      tenantId: '',
      contactUserName: '',
      contactPhone: '',
      companyName: '',
      pageNum: 1,
      pageSize: 1000
    });
    const tenantList = res.data || res.rows || [];
    const map = new Map<string, string>();
    tenantList.forEach((tenant: any) => {
      map.set(tenant.tenantId, tenant.companyName);
    });
    tenantMap.value = map;
  } catch (error) {
    console.error('获取租户字典失败:', error);
  }
};

/** 处理设备列表数据，添加租户名称和部门名称 */
const processDeviceList = (devices: any[]) => {
  return devices.map((device) => {
    // 根据实际返回的数据结构进行映射
    const processed = { ...device };
    
    // 添加部门名称映射
    if (device.deptId && deptMap.value.has(String(device.deptId))) {
      processed.deptName = deptMap.value.get(String(device.deptId));
    }
    
    // 为共享出去的设备添加被授权方租户名称
    if (device.targetTenantId && tenantMap.value.has(device.targetTenantId)) {
      processed.targetTenantName = tenantMap.value.get(device.targetTenantId);
    }
    
    // 为共享给我的设备添加拥有者租户名称
    // 注意：共享给我的设备API返回的可能是不同的字段结构
    if (activeTab.value === 'shared_in') {
      // 共享给我的设备中，当前租户是被授权方，需要显示拥有者信息
      if (device.ownerTenantId && tenantMap.value.has(device.ownerTenantId)) {
        processed.ownerTenantName = tenantMap.value.get(device.ownerTenantId);
      } else if (device.tenantId && tenantMap.value.has(device.tenantId) && device.tenantId !== userStore.tenantId) {
        // 如果ownerTenantId不存在，但tenantId存在且不是当前租户，则将其作为拥有者
        processed.ownerTenantId = device.tenantId;
        processed.ownerTenantName = tenantMap.value.get(device.tenantId);
      }
    } else if (activeTab.value === 'shared_out') {
      // 共享出去的设备中，当前租户是拥有者
      if (device.ownerTenantId && tenantMap.value.has(device.ownerTenantId)) {
        processed.ownerTenantName = tenantMap.value.get(device.ownerTenantId);
      }
    }
    
    return processed;
  });
};

/** 查询飞行设备列表 */
const getList = async () => {
  loading.value = true;

  let apiCall;
  switch (activeTab.value) {
    case 'own':
      apiCall = listFlightDevice(queryParams.value);
      break;
    case 'shared_out':
      apiCall = listSharedOutDevices(queryParams.value);
      break;
    case 'shared_in':
      apiCall = listSharedInDevices(queryParams.value);
      break;
    default:
      apiCall = listFlightDevice(queryParams.value);
  }

  const res = await apiCall;
  const devices = res.rows || [];
  
  // 处理数据，添加租户名称
  flightDeviceList.value = processDeviceList(devices);
  total.value = res.total;
  loading.value = false;
};

/** 标签页切换 */
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName;
  queryParams.value.pageNum = 1;
  getList();
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  flightDeviceFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  queryParams.value.deptId = undefined;
  selectedDeptId.value = undefined;
  deptTreeRef.value?.setCurrentKey(undefined);
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: FlightDeviceVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加飞行设备';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: FlightDeviceVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getFlightDevice(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改飞行设备';
};

/** 提交按钮 */
const submitForm = () => {
  flightDeviceFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateFlightDevice(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addFlightDevice(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: FlightDeviceVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除飞行设备编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delFlightDevice(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  let exportUrl = 'biz/flightDevice/export';
  let fileName = 'flightDevice';
  
  // 根据当前标签页确定导出接口和文件名
  switch (activeTab.value) {
    case 'own':
      exportUrl = 'biz/flightDevice/export';
      fileName = '我的设备列表';
      break;
    case 'shared_out':
      exportUrl = 'biz/flightDevice/export'; // TODO: 可能需要专门的导出接口
      fileName = '共享出去的设备';
      break;
    case 'shared_in':
      exportUrl = 'biz/flightDevice/export'; // TODO: 可能需要专门的导出接口
      fileName = '共享给我的设备';
      break;
  }
  
  proxy?.download(
    exportUrl,
    {
      ...queryParams.value,
      type: activeTab.value // 传递类型参数
    },
    `${fileName}_${new Date().getTime()}.xlsx`
  );
};

/** 授权共享按钮操作 */
const handleShare = (row: FlightDeviceVO) => {
  selectedDeviceId.value = row.id;
  selectedSharePermissionId.value = ''; // 清空编辑ID，确保是新增模式
  shareDialogVisible.value = true;
};

/** 删除授权操作 */
const handleDeleteShare = async (row: FlightDeviceVO) => {
  if (!row.sharePermissionId) {
    proxy?.$modal.msgError('缺少授权记录ID，无法删除');
    return;
  }

  try {
    await proxy?.$modal.confirm(
      `是否确认删除设备"${row.deviceName}"的授权记录？\n删除后"${row.targetTenantName || '目标租户'}"将无法使用该设备。`,
      '删除授权确认',
      {
        confirmButtonText: '确认删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    // 调用删除授权API
    await delFlightDeviceSharePermission(row.sharePermissionId);
    proxy?.$modal.msgSuccess('删除授权成功');
    await getList();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除授权失败:', error);
      proxy?.$modal.msgError('删除授权失败，请重试');
    }
  }
};

/** 申请使用设备 */
const handleRequestAccess = async (row: FlightDeviceVO) => {
  // 设置选中的设备信息
  selectedDeviceForApplication.value = row;
  applicationDialogVisible.value = true;
};

/** 授权成功处理 */
const handleShareSuccess = () => {
  proxy?.$modal.msgSuccess('操作成功');
  shareDialogVisible.value = false;
  selectedSharePermissionId.value = '';
  getList();
};

/** 授权关闭处理 */
const handleShareClose = () => {
  shareDialogVisible.value = false;
  selectedSharePermissionId.value = '';
};

/** 申请成功处理 */
const handleApplicationSuccess = () => {
  applicationDialogVisible.value = false;
  selectedDeviceForApplication.value = null;
  // 刷新列表以更新状态
  getList();
};

/** 申请关闭处理 */
const handleApplicationClose = () => {
  applicationDialogVisible.value = false;
  selectedDeviceForApplication.value = null;
};

/** 格式化日期 */
const formatDate = (dateString: string) => {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

/** 编辑授权操作 */
const handleEditShare = (row: FlightDeviceVO) => {
  if (!row.sharePermissionId) {
    proxy?.$modal.msgError('缺少授权记录ID，无法编辑');
    return;
  }
  
  // 设置编辑模式的参数
  selectedDeviceId.value = row.id; // 设备ID
  selectedSharePermissionId.value = row.sharePermissionId; // 授权记录ID
  shareDialogVisible.value = true;
};

/** 历史申请记录操作 */
const handleHistoryRequest = (row: FlightDeviceVO) => {
  // 跳转到设备使用申请页面，并传递参数自动筛选
  router.push({
    path: '/config/flightDeviceUseApplication',
    query: {
      tab: 'my_applications', // 自动切换到"我发出的申请"标签
      deviceId: row.id, // 传递设备ID用于筛选
      deviceName: row.deviceName || '未知设备' // 传递设备名称用于显示
    }
  });
};

/** 修改批量授权处理函数 */
const handleBatchShare = (row: FlightDeviceVO) => {
  selectedDeviceId.value = row.id;
  batchShareDialogVisible.value = true;
};

/** 处理批量授权提交 */
const handleBatchShareSubmit = async (formData: any) => {
  try {
    await batchGrantFlightDeviceSharePermission({
      deviceId: selectedDeviceId.value,
      ownerTenantId: userStore.tenantId,
      targetTenantIds: formData.targetTenantIds,
      status: '1', // 默认有效状态
      permissionType: formData.permissionType,
      startTime: formData.timeRange?.[0],
      endTime: formData.timeRange?.[1],
      remark: formData.remark
    });
    proxy?.$modal.msgSuccess('批量授权成功');
    batchShareDialogVisible.value = false;
    getList();
  } catch (error) {
    console.error('批量授权失败:', error);
    proxy?.$modal.msgError('批量授权失败');
  }
};

/** 处理批量授权弹窗关闭 */
const handleBatchShareClose = () => {
  batchShareDialogVisible.value = false;
  selectedDeviceIds.value = [];
  selectedTargetTenantIds.value = [];
};

onMounted(async () => {
  // 先获取租户字典和部门树，再获取设备列表
  await Promise.all([getTenantMap(), getDeptTree()]);
  await getList();
});
</script>
