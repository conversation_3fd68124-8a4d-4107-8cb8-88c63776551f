<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="关联的航线ID" prop="routeId">
              <el-input v-model="queryParams.routeId" placeholder="请输入关联的航线ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="航点名称" prop="waypointName">
              <el-input v-model="queryParams.waypointName" placeholder="请输入航点名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="飞行高度" prop="flightAltitude">
              <el-input v-model="queryParams.flightAltitude" placeholder="请输入飞行高度" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="航点动作" prop="waypointAction">
              <el-select v-model="queryParams.waypointAction" placeholder="请选择航点动作" clearable >
                <el-option v-for="dict in flight_waypoint_action" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="云台俯仰角" prop="gimbalPitchAngle">
              <el-input v-model="queryParams.gimbalPitchAngle" placeholder="请输入云台俯仰角" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="云台平移角" prop="gimbalRollAngle">
              <el-input v-model="queryParams.gimbalRollAngle" placeholder="请输入云台平移角" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="经度" prop="longitude">
              <el-input v-model="queryParams.longitude" placeholder="请输入经度" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="纬度" prop="latitude">
              <el-input v-model="queryParams.latitude" placeholder="请输入纬度" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="是否为返航点" prop="isReturnPoint">
              <el-input v-model="queryParams.isReturnPoint" placeholder="请输入是否为返航点" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="垂直降落高度" prop="verticalLandingHeight">
              <el-input v-model="queryParams.verticalLandingHeight" placeholder="请输入垂直降落高度" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['biz:flightWaypoint:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['biz:flightWaypoint:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['biz:flightWaypoint:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['biz:flightWaypoint:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="flightWaypointList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="航点ID，主键" align="center" prop="id" v-if="true" />
        <el-table-column label="关联的航线ID" align="center" prop="routeId" />
        <el-table-column label="航点名称" align="center" prop="waypointName" />
        <el-table-column label="飞行高度" align="center" prop="flightAltitude" />
        <el-table-column label="航点动作" align="center" prop="waypointAction">
          <template #default="scope">
            <dict-tag :options="flight_waypoint_action" :value="scope.row.waypointAction"/>
          </template>
        </el-table-column>
        <el-table-column label="云台俯仰角" align="center" prop="gimbalPitchAngle" />
        <el-table-column label="云台平移角" align="center" prop="gimbalRollAngle" />
        <el-table-column label="经度" align="center" prop="longitude" />
        <el-table-column label="纬度" align="center" prop="latitude" />
        <el-table-column label="是否为返航点" align="center" prop="isReturnPoint" />
        <el-table-column label="垂直降落高度" align="center" prop="verticalLandingHeight" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['biz:flightWaypoint:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['biz:flightWaypoint:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改航点对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="680px" append-to-body>
      <el-form ref="flightWaypointFormRef" :model="form" :rules="rules" label-width="128px">
        <el-form-item label="关联的航线ID" prop="routeId">
          <el-input v-model="form.routeId" placeholder="请输入关联的航线ID" />
        </el-form-item>
        <el-form-item label="航点名称" prop="waypointName">
          <el-input v-model="form.waypointName" placeholder="请输入航点名称" />
        </el-form-item>
        <el-form-item label="飞行高度" prop="flightAltitude">
          <el-input v-model="form.flightAltitude" placeholder="请输入飞行高度" />
        </el-form-item>
        <el-form-item label="航点动作" prop="waypointAction">
          <el-select v-model="form.waypointAction" placeholder="请选择航点动作">
            <el-option
                v-for="dict in flight_waypoint_action"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="云台俯仰角" prop="gimbalPitchAngle">
          <el-input v-model="form.gimbalPitchAngle" placeholder="请输入云台俯仰角" />
        </el-form-item>
        <el-form-item label="云台平移角" prop="gimbalRollAngle">
          <el-input v-model="form.gimbalRollAngle" placeholder="请输入云台平移角" />
        </el-form-item>
        <el-form-item label="经度" prop="longitude">
          <el-input v-model="form.longitude" placeholder="请输入经度" />
        </el-form-item>
        <el-form-item label="纬度" prop="latitude">
          <el-input v-model="form.latitude" placeholder="请输入纬度" />
        </el-form-item>
        <el-form-item label="是否为返航点" prop="isReturnPoint">
          <el-input v-model="form.isReturnPoint" placeholder="请输入是否为返航点" />
        </el-form-item>
        <el-form-item label="垂直降落高度" prop="verticalLandingHeight">
          <el-input v-model="form.verticalLandingHeight" placeholder="请输入垂直降落高度" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="FlightWaypoint" lang="ts">
import { listFlightWaypoint, getFlightWaypoint, delFlightWaypoint, addFlightWaypoint, updateFlightWaypoint } from '@/api/biz/flightWaypoint';
import { FlightWaypointVO, FlightWaypointQuery, FlightWaypointForm } from '@/api/biz/flightWaypoint/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { flight_waypoint_action } = toRefs<any>(proxy?.useDict('flight_waypoint_action'));

const flightWaypointList = ref<FlightWaypointVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(false);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const flightWaypointFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: FlightWaypointForm = {
  id: undefined,
  routeId: undefined,
  waypointName: undefined,
  flightAltitude: undefined,
  waypointAction: undefined,
  gimbalPitchAngle: undefined,
  gimbalRollAngle: undefined,
  longitude: undefined,
  latitude: undefined,
  isReturnPoint: undefined,
  verticalLandingHeight: undefined,
}
const data = reactive<PageData<FlightWaypointForm, FlightWaypointQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    routeId: undefined,
    waypointName: undefined,
    flightAltitude: undefined,
    waypointAction: undefined,
    gimbalPitchAngle: undefined,
    gimbalRollAngle: undefined,
    longitude: undefined,
    latitude: undefined,
    isReturnPoint: undefined,
    verticalLandingHeight: undefined,
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "航点ID，主键不能为空", trigger: "blur" }
    ],
    routeId: [
      { required: true, message: "关联的航线ID不能为空", trigger: "blur" }
    ],
    waypointName: [
      { required: true, message: "航点名称不能为空", trigger: "blur" }
    ],
    longitude: [
      { required: true, message: "经度不能为空", trigger: "blur" }
    ],
    latitude: [
      { required: true, message: "纬度不能为空", trigger: "blur" }
    ],
    isReturnPoint: [
      { required: true, message: "是否为返航点不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询航点列表 */
const getList = async () => {
  loading.value = true;
  const res = await listFlightWaypoint(queryParams.value);
  flightWaypointList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  flightWaypointFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: FlightWaypointVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加航点";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: FlightWaypointVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getFlightWaypoint(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改航点";
}

/** 提交按钮 */
const submitForm = () => {
  flightWaypointFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateFlightWaypoint(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addFlightWaypoint(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: FlightWaypointVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除航点编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delFlightWaypoint(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('biz/flightWaypoint/export', {
    ...queryParams.value
  }, `flightWaypoint_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
