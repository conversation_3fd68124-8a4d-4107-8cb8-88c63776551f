<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="置信度，范围0-100%" prop="confidence">
              <el-input v-model="queryParams.confidence" placeholder="请输入置信度，范围0-100%" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="侵占确认，1为已确认，0为未确认" prop="occupationConfirmed">
              <el-input v-model="queryParams.occupationConfirmed" placeholder="请输入侵占确认，1为已确认，0为未确认" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="发生时间" prop="eventTime">
              <el-date-picker clearable
                v-model="queryParams.eventTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择发生时间"
              />
            </el-form-item>
            <el-form-item label="桩号" prop="pileNo">
              <el-input v-model="queryParams.pileNo" placeholder="请输入桩号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="任务记录编号" prop="taskResultNo">
              <el-input v-model="queryParams.taskResultNo" placeholder="请输入任务记录编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="路线编号" prop="roadNum">
              <el-input v-model="queryParams.roadNum" placeholder="请输入路线编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['biz:roadRightOccupationInfo:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['biz:roadRightOccupationInfo:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['biz:roadRightOccupationInfo:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['biz:roadRightOccupationInfo:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="roadRightOccupationInfoList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="侵占路权记录ID，主键" align="center" prop="id" v-if="true" />
        <el-table-column label="关联的智能巡检任务结果ID" align="center" prop="taskResultId" />
        <el-table-column label="侵占类型" align="center" prop="occupationType" />
        <el-table-column label="纬度" align="center" prop="latitude" />
        <el-table-column label="经度" align="center" prop="longitude" />
        <el-table-column label="图片URL" align="center" prop="imageUrl" />
        <el-table-column label="置信度，范围0-100%" align="center" prop="confidence" />
        <el-table-column label="侵占确认，1为已确认，0为未确认" align="center" prop="occupationConfirmed" />
        <el-table-column label="发生时间" align="center" prop="eventTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.eventTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="桩号" align="center" prop="pileNo" />
        <el-table-column label="任务记录编号" align="center" prop="taskResultNo" />
        <el-table-column label="路线编号" align="center" prop="roadNum" />
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['biz:roadRightOccupationInfo:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['biz:roadRightOccupationInfo:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改公路智能巡检侵占路权信息对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="roadRightOccupationInfoFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="关联的智能巡检任务结果ID" prop="taskResultId">
          <el-input v-model="form.taskResultId" placeholder="请输入关联的智能巡检任务结果ID" />
        </el-form-item>
        <el-form-item label="纬度" prop="latitude">
          <el-input v-model="form.latitude" placeholder="请输入纬度" />
        </el-form-item>
        <el-form-item label="经度" prop="longitude">
          <el-input v-model="form.longitude" placeholder="请输入经度" />
        </el-form-item>
        <el-form-item label="图片URL" prop="imageUrl">
            <el-input v-model="form.imageUrl" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="置信度，范围0-100%" prop="confidence">
          <el-input v-model="form.confidence" placeholder="请输入置信度，范围0-100%" />
        </el-form-item>
        <el-form-item label="侵占确认，1为已确认，0为未确认" prop="occupationConfirmed">
          <el-input v-model="form.occupationConfirmed" placeholder="请输入侵占确认，1为已确认，0为未确认" />
        </el-form-item>
        <el-form-item label="发生时间" prop="eventTime">
          <el-date-picker clearable
            v-model="form.eventTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择发生时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="桩号" prop="pileNo">
          <el-input v-model="form.pileNo" placeholder="请输入桩号" />
        </el-form-item>
        <el-form-item label="任务记录编号" prop="taskResultNo">
          <el-input v-model="form.taskResultNo" placeholder="请输入任务记录编号" />
        </el-form-item>
        <el-form-item label="路线编号" prop="roadNum">
          <el-input v-model="form.roadNum" placeholder="请输入路线编号" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="RoadRightOccupationInfo" lang="ts">
import { listRoadRightOccupationInfo, getRoadRightOccupationInfo, delRoadRightOccupationInfo, addRoadRightOccupationInfo, updateRoadRightOccupationInfo } from '@/api/biz/roadRightOccupationInfo';
import { RoadRightOccupationInfoVO, RoadRightOccupationInfoQuery, RoadRightOccupationInfoForm } from '@/api/biz/roadRightOccupationInfo/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const roadRightOccupationInfoList = ref<RoadRightOccupationInfoVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(false);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const roadRightOccupationInfoFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: RoadRightOccupationInfoForm = {
  id: undefined,
  taskResultId: undefined,
  occupationType: undefined,
  latitude: undefined,
  longitude: undefined,
  imageUrl: undefined,
  confidence: undefined,
  occupationConfirmed: undefined,
  eventTime: undefined,
  pileNo: undefined,
  taskResultNo: undefined,
  roadNum: undefined,
  remark: undefined
}
const data = reactive<PageData<RoadRightOccupationInfoForm, RoadRightOccupationInfoQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    confidence: undefined,
    occupationConfirmed: undefined,
    eventTime: undefined,
    pileNo: undefined,
    taskResultNo: undefined,
    roadNum: undefined,
    params: {
    }
  },
  rules: {
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公路智能巡检侵占路权信息列表 */
const getList = async () => {
  loading.value = true;
  const res = await listRoadRightOccupationInfo(queryParams.value);
  roadRightOccupationInfoList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  roadRightOccupationInfoFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: RoadRightOccupationInfoVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加公路智能巡检侵占路权信息";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: RoadRightOccupationInfoVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getRoadRightOccupationInfo(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改公路智能巡检侵占路权信息";
}

/** 提交按钮 */
const submitForm = () => {
  roadRightOccupationInfoFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateRoadRightOccupationInfo(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addRoadRightOccupationInfo(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: RoadRightOccupationInfoVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除公路智能巡检侵占路权信息编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delRoadRightOccupationInfo(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('biz/roadRightOccupationInfo/export', {
    ...queryParams.value
  }, `roadRightOccupationInfo_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
