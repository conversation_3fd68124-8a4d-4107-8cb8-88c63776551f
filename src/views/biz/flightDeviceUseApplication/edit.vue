<template>
  <el-dialog 
    :title="dialogTitle" 
    v-model="dialogVisible" 
    width="600px" 
    append-to-body 
    @close="handleClose"
  >
    <el-form 
      ref="formRef" 
      :model="form" 
      :rules="rules" 
      label-width="120px"
    >
      <!-- 设备信息显示 -->
      <el-form-item label="申请设备">
        <el-input 
          :value="deviceDisplayName" 
          readonly 
          placeholder="设备信息"
        />
      </el-form-item>
      
      <el-form-item label="设备拥有者" v-if="!isApprovalMode || isViewMode">
        <el-input 
          :value="props.ownerTenantName || '未知租户'" 
          readonly 
          placeholder="拥有者信息"
        />
      </el-form-item>

      <el-form-item label="申请方" v-if="isApprovalMode || isViewMode">
        <el-input 
          :value="props.applicantTenantName || '未知租户'" 
          readonly 
          placeholder="申请方信息"
        />
      </el-form-item>

      <!-- 申请信息 -->
      <!-- 申请相关字段 - 申请方可编辑，审批方和查看模式只读 -->
      <el-form-item label="申请说明" prop="applyReason">
        <el-input 
          v-model="form.applyReason" 
          type="textarea" 
          :rows="3"
          :readonly="isApprovalMode || isViewMode"
          placeholder="请说明申请使用该设备的原因和用途"
        />
      </el-form-item>

      <el-form-item label="开始时间" prop="applyStartTime">
        <el-date-picker
          v-model="form.applyStartTime"
          type="datetime"
          value-format="YYYY-MM-DD HH:mm:ss"
          placeholder="请选择申请占用开始时间"
          style="width: 100%"
          :disabled="isApprovalMode || isViewMode"
        />
      </el-form-item>

      <el-form-item label="结束时间" prop="applyEndTime">
        <el-date-picker
          v-model="form.applyEndTime"
          type="datetime"
          value-format="YYYY-MM-DD HH:mm:ss"
          placeholder="请选择申请占用结束时间"
          style="width: 100%"
          :disabled="isApprovalMode || isViewMode"
        />
      </el-form-item>

      <!-- 审批相关字段 - 审批模式可编辑，查看模式只读显示 -->
      <template v-if="isApprovalMode || (isViewMode && isEditMode)">
        <el-divider content-position="left">审批信息</el-divider>
        
        <el-form-item label="审批状态" prop="approveStatus">
          <el-select 
            v-if="!isViewMode" 
            v-model="form.approveStatus" 
            placeholder="请选择审批状态" 
            style="width: 100%"
          >
            <el-option label="待审批" value="0" />
            <el-option label="已通过" value="1" />
            <el-option label="已拒绝" value="2" />
          </el-select>
          <el-tag 
            v-else 
            :type="getStatusTagType(form.approveStatus)"
            size="large"
          >
            {{ getStatusText(form.approveStatus) }}
          </el-tag>
        </el-form-item>

        <el-form-item label="审批意见" prop="approveOpinion">
          <el-input 
            v-model="form.approveOpinion" 
            type="textarea" 
            :rows="3"
            :readonly="isViewMode"
            placeholder="请输入审批意见或备注"
          />
        </el-form-item>
      </template>

      <!-- 已有申请的状态显示 - 申请模式下显示当前状态 -->
      <template v-if="!isApprovalMode && isEditMode">
        <el-divider content-position="left">申请状态</el-divider>
        
        <el-form-item label="当前状态">
          <el-tag :type="getStatusTagType(form.approveStatus)">
            {{ getStatusText(form.approveStatus) }}
          </el-tag>
        </el-form-item>

        <el-form-item label="审批意见" v-if="form.approveOpinion">
          <el-input 
            :value="form.approveOpinion" 
            type="textarea" 
            :rows="2"
            readonly
            placeholder="暂无审批意见"
          />
        </el-form-item>
      </template>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{ isViewMode ? '关闭' : '取 消' }}</el-button>
        <el-button 
          v-if="showSubmitButton"
          type="primary" 
          :loading="submitLoading" 
          @click="handleSubmit"
        >
          {{ submitButtonText }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { addFlightDeviceUseApplication, updateFlightDeviceUseApplication, getFlightDeviceUseApplication } from '@/api/biz/flightDeviceUseApplication';
import { FlightDeviceUseApplicationForm } from '@/api/biz/flightDeviceUseApplication/types';
import { useUserStore } from '@/store/modules/user';

// Props 定义
interface Props {
  visible: boolean;
  mode?: 'apply' | 'approve' | 'view'; // 申请模式、审批模式或查看模式
  editId?: string | number; // 编辑的申请ID
  presetDeviceId?: string | number;
  presetOwnerTenantId?: string;
  deviceName?: string;
  ownerTenantName?: string;
  applicantTenantName?: string;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  mode: 'apply',
  editId: '',
  presetDeviceId: '',
  presetOwnerTenantId: '',
  deviceName: '',
  ownerTenantName: '',
  applicantTenantName: ''
});

// Emits 定义
const emit = defineEmits<{
  success: [];
  close: [];
}>();

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const userStore = useUserStore();
const formRef = ref<ElFormInstance>();
const submitLoading = ref(false);

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => {
    if (!value) {
      emit('close');
    }
  }
});

const isApprovalMode = computed(() => props.mode === 'approve');
const isViewMode = computed(() => props.mode === 'view');
const isEditMode = computed(() => !!props.editId);

const dialogTitle = computed(() => {
  if (isViewMode.value) {
    return `查看申请：${props.deviceName || '未知设备'}`;
  }
  if (isApprovalMode.value) {
    return `审批申请：${props.deviceName || '未知设备'}`;
  }
  return isEditMode.value 
    ? `编辑申请：${props.deviceName || '未知设备'}`
    : `申请使用设备：${props.deviceName || '未知设备'}`;
});

const submitButtonText = computed(() => {
  if (isViewMode.value) {
    return '关闭';
  }
  if (isApprovalMode.value) {
    return '确认审批';
  }
  return isEditMode.value ? '更新申请' : '提交申请';
});

const showSubmitButton = computed(() => !isViewMode.value);

const deviceDisplayName = computed(() => {
  return props.deviceName ? `${props.deviceName} (ID: ${props.presetDeviceId})` : `设备ID: ${props.presetDeviceId}`;
});

// 表单数据
const initFormData: FlightDeviceUseApplicationForm = {
  id: undefined,
  deviceId: undefined,
  ownerTenantId: undefined,
  targetTenantId: undefined,
  applyReason: undefined,
  applyStartTime: undefined,
  applyEndTime: undefined,
  approveStatus: '0', // 0=待审批 (修复类型：使用字符串)
  approveOpinion: undefined
};

const form = ref<FlightDeviceUseApplicationForm>({ ...initFormData });

// 表单验证规则
const rules = computed(() => {
  const baseRules: any = {};
  
  // 查看模式下不需要验证
  if (isViewMode.value) {
    return baseRules;
  }
  
  // 申请相关字段验证 - 申请模式需要验证
  if (!isApprovalMode.value) {
    baseRules.applyReason = [
      { required: true, message: '请输入申请说明', trigger: 'blur' },
      { min: 10, message: '申请说明至少10个字符', trigger: 'blur' }
    ];
    baseRules.applyStartTime = [
      { required: true, message: '请选择开始时间', trigger: 'change' }
    ];
    baseRules.applyEndTime = [
      { required: true, message: '请选择结束时间', trigger: 'change' }
    ];
  }
  
  // 审批相关字段验证 - 审批模式需要验证
  if (isApprovalMode.value) {
    baseRules.approveStatus = [
      { required: true, message: '请选择审批状态', trigger: 'change' }
    ];
    baseRules.approveOpinion = [
      { required: true, message: '请输入审批意见', trigger: 'blur' }
    ];
  }
  
  return baseRules;
});

// 监听props变化，重置表单和加载数据
watch(() => props.visible, async (newVal) => {
  if (newVal) {
    resetForm();
    
    if (isEditMode.value || isViewMode.value) {
      // 编辑模式或查看模式：加载现有数据
      await loadApplicationData();
    } else {
      // 新建模式：设置默认值
      setDefaultValues();
    }
  }
});

// 加载申请数据
const loadApplicationData = async () => {
  try {
    const res = await getFlightDeviceUseApplication(props.editId);
    Object.assign(form.value, res.data);
  } catch (error) {
    console.error('加载申请数据失败:', error);
    proxy?.$modal.msgError('加载申请数据失败');
  }
};

// 设置默认值
const setDefaultValues = () => {
  form.value.deviceId = props.presetDeviceId;
  form.value.ownerTenantId = props.presetOwnerTenantId;
  form.value.targetTenantId = userStore.tenantId; // 当前租户作为申请方
  form.value.approveStatus = '0'; // 默认待审批状态
  
  // 设置默认申请时间（当前时间开始，24小时后结束）
  const now = new Date();
  const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000);
  form.value.applyStartTime = formatDateTime(now);
  form.value.applyEndTime = formatDateTime(tomorrow);
};

// 重置表单
const resetForm = () => {
  form.value = { ...initFormData };
  formRef.value?.resetFields();
};

// 时间格式化
const formatDateTime = (date: Date): string => {
  return date.toISOString().slice(0, 19).replace('T', ' ');
};

// 状态相关方法
const getStatusText = (status: string | undefined) => {
  switch (status) {
    case '0': return '待审批';
    case '1': return '已通过';
    case '2': return '已拒绝';
    default: return '未知状态';
  }
};

const getStatusTagType = (status: string | undefined) => {
  switch (status) {
    case '0': return 'warning';
    case '1': return 'success';
    case '2': return 'danger';
    default: return 'info';
  }
};

// 自定义验证：结束时间必须大于开始时间
const validateEndTime = () => {
  if (form.value.applyStartTime && form.value.applyEndTime) {
    const startTime = new Date(form.value.applyStartTime);
    const endTime = new Date(form.value.applyEndTime);
    
    if (endTime <= startTime) {
      proxy?.$modal.msgError('结束时间必须大于开始时间');
      return false;
    }
    
    // 检查时间间隔不能太短（至少1小时）
    const timeDiff = endTime.getTime() - startTime.getTime();
    const oneHour = 60 * 60 * 1000;
    if (timeDiff < oneHour) {
      proxy?.$modal.msgError('申请时间至少需要1小时');
      return false;
    }
  }
  return true;
};

// 提交表单
const handleSubmit = () => {
  // 查看模式下不允许提交
  if (isViewMode.value) {
    handleClose();
    return;
  }
  
  formRef.value?.validate(async (valid: boolean) => {
    if (!valid) return;
    
    // 申请模式下验证时间
    if (!isApprovalMode.value && !validateEndTime()) return;
    
    try {
      submitLoading.value = true;
      
      if (isEditMode.value) {
        await updateFlightDeviceUseApplication(form.value);
      } else {
        await addFlightDeviceUseApplication(form.value);
      }
      
      const successMsg = isApprovalMode.value 
        ? '审批完成' 
        : isEditMode.value 
          ? '申请更新成功' 
          : '申请提交成功，请等待设备拥有者审批';
      
      proxy?.$modal.msgSuccess(successMsg);
      emit('success');
      
    } catch (error) {
      console.error('操作失败:', error);
      proxy?.$modal.msgError('操作失败，请重试');
    } finally {
      submitLoading.value = false;
    }
  });
};

// 关闭弹窗
const handleClose = () => {
  resetForm();
  emit('close');
};
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-textarea__inner) {
  min-height: 80px;
}

:deep(.el-divider__text) {
  font-weight: 600;
  color: var(--el-text-color-primary);
}
</style>
