<template>
  <div class="p-2">
    <!-- 标签页导航 -->
    <el-tabs v-model="activeTab" @tab-change="handleTabChange" class="mb-4">
      <el-tab-pane label="所有申请" name="all" />
      <el-tab-pane label="我发出的申请" name="my_applications" />
      <el-tab-pane label="待我审批" name="pending_approvals" />
      <el-tab-pane label="已审批" name="approved_applications" />
    </el-tabs>

    <!-- 筛选条件提示 -->
    <div v-if="routeDeviceId || queryParams.id" class="mb-4">
      <el-alert v-if="routeDeviceId" :title="`当前已筛选设备：${routeDeviceName} (ID: ${routeDeviceId})`" type="info" show-icon :closable="false">
        <template #default>
          <div class="flex items-center justify-between">
            <span>当前已筛选设备：{{ routeDeviceName }} (ID: {{ routeDeviceId }})</span>
            <el-button size="small" type="primary" link @click="clearDeviceFilter">
              <el-icon><Close /></el-icon>
              清除筛选
            </el-button>
          </div>
        </template>
      </el-alert>
      <el-alert v-if="queryParams.id" title="当前已定位到特定申请" type="warning" show-icon :closable="false">
        <template #default>
          <div class="flex items-center justify-between">
            <span>当前已定位到申请ID：{{ queryParams.id }}</span>
            <el-button size="small" type="primary" link @click="clearApplicationFilter">
              <el-icon><Close /></el-icon>
              返回列表
            </el-button>
          </div>
        </template>
      </el-alert>
    </div>

    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="申请使用的设备 ID，对应 biz_flight_device.id" prop="deviceId">
              <el-input
                v-model="queryParams.deviceId"
                placeholder="请输入申请使用的设备 ID，对应 biz_flight_device.id"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item label="设备拥有方租户 ID" prop="ownerTenantId">
              <el-input v-model="queryParams.ownerTenantId" placeholder="请输入设备拥有方租户 ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="被授权 / 申请方租户 ID" prop="targetTenantId">
              <el-input v-model="queryParams.targetTenantId" placeholder="请输入被授权 / 申请方租户 ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="申请说明或备注" prop="applyReason">
              <el-input v-model="queryParams.applyReason" placeholder="请输入申请说明或备注" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="申请占用开始时间" prop="applyStartTime">
              <el-date-picker
                clearable
                v-model="queryParams.applyStartTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择申请占用开始时间"
              />
            </el-form-item>
            <el-form-item label="申请占用结束时间" prop="applyEndTime">
              <el-date-picker
                clearable
                v-model="queryParams.applyEndTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择申请占用结束时间"
              />
            </el-form-item>
            <el-form-item label="审批意见/备注" prop="approveOpinion">
              <el-input v-model="queryParams.approveOpinion" placeholder="请输入审批意见/备注" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5" v-if="activeTab === 'my_applications'">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['biz:flightDeviceUseApplication:add']">新增申请</el-button>
          </el-col>
          <el-col :span="1.5" v-if="activeTab === 'my_applications'">
            <el-button
              type="success"
              plain
              icon="Edit"
              :disabled="single"
              @click="handleUpdate()"
              v-hasPermi="['biz:flightDeviceUseApplication:edit']"
            >
              批量编辑
            </el-button>
          </el-col>
          <el-col :span="1.5" v-if="activeTab === 'my_applications'">
            <el-button
              type="danger"
              plain
              icon="Delete"
              :disabled="multiple"
              @click="handleDelete()"
              v-hasPermi="['biz:flightDeviceUseApplication:remove']"
            >
              批量删除
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['biz:flightDeviceUseApplication:export']">
              导出
            </el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="flightDeviceUseApplicationList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" v-if="activeTab === 'my_applications'" />
        <el-table-column label="主键 ID" align="center" prop="id" v-if="true" />
        <el-table-column label="申请设备" align="center" prop="deviceName" width="200">
          <template #default="scope">
            <div>
              <div>{{ scope.row.deviceName || '未知设备' }}</div>
              <small class="text-gray-500">ID: {{ scope.row.deviceId }}</small>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="设备拥有方" align="center" prop="ownerTenantName" width="160">
          <template #default="scope">
            <div>
              <div>{{ scope.row.ownerTenantName || '未知租户' }}</div>
              <small class="text-gray-500">ID: {{ scope.row.ownerTenantId }}</small>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="申请方" align="center" prop="targetTenantName" width="160">
          <template #default="scope">
            <div>
              <div>{{ scope.row.targetTenantName || '未知租户' }}</div>
              <small class="text-gray-500">ID: {{ scope.row.targetTenantId }}</small>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="申请说明或备注" align="center" prop="applyReason" />
        <el-table-column label="申请占用开始时间" align="center" prop="applyStartTime" width="160">
          <template #default="scope">
            <span>{{ parseTime(scope.row.applyStartTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="申请占用结束时间" align="center" prop="applyEndTime" width="160">
          <template #default="scope">
            <span>{{ parseTime(scope.row.applyEndTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="审批状态" align="center" prop="approveStatus">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.approveStatus)">
              {{ getStatusText(scope.row.approveStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="审批意见/备注" align="center" prop="approveOpinion" />
        <el-table-column label="审批人用户 ID" align="center" prop="approveBy" />
        <el-table-column label="审批通过/拒绝时间" align="center" prop="approveTime" width="160">
          <template #default="scope">
            <span>{{ parseTime(scope.row.approveTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <!-- 所有申请 - 只能查看 -->
            <el-tooltip content="查看详情" placement="top" v-if="activeTab === 'all'">
              <el-button link type="primary" icon="View" @click="handleView(scope.row)">查看</el-button>
            </el-tooltip>

            <!-- 我发出的申请 - 可以编辑和删除 -->
            <template v-if="activeTab === 'my_applications'">
              <el-tooltip content="编辑申请" placement="top">
                <el-button
                  link
                  type="primary"
                  icon="Edit"
                  @click="handleEditApplication(scope.row)"
                  v-hasPermi="['biz:flightDeviceUseApplication:edit']"
                >
                  编辑
                </el-button>
              </el-tooltip>
              <el-tooltip content="删除申请" placement="top">
                <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['biz:flightDeviceUseApplication:remove']">
                  删除
                </el-button>
              </el-tooltip>
              <el-tooltip content="查看详情" placement="top">
                <el-button link type="primary" icon="View" @click="handleView(scope.row)">查看</el-button>
              </el-tooltip>
            </template>

            <!-- 待我审批 - 可以审批 -->
            <template v-if="activeTab === 'pending_approvals'">
              <el-tooltip content="审批申请" placement="top">
                <el-button
                  link
                  type="primary"
                  icon="CircleCheck"
                  @click="handleApproval(scope.row)"
                  v-hasPermi="['biz:flightDeviceUseApplication:edit']"
                >
                  审批
                </el-button>
              </el-tooltip>
              <el-tooltip content="查看详情" placement="top">
                <el-button link type="primary" icon="View" @click="handleView(scope.row)">查看</el-button>
              </el-tooltip>
            </template>

            <!-- 已审批 - 可以查看和安排飞行计划 -->
            <template v-if="activeTab === 'approved_applications'">
              <!-- 只有设备拥有方可以安排飞行计划 -->
              <el-tooltip content="安排飞行计划" placement="top" v-if="String(userStore.tenantId) === String(scope.row.ownerTenantId)">
                <el-button
                  link
                  type="success"
                  icon="Calendar"
                  @click="handleScheduleFlight(scope.row)"
                  v-hasPermi="['biz:flightDeviceUseApplication:edit']"
                >
                  安排飞行计划
                </el-button>
              </el-tooltip>
              <el-tooltip content="查看飞行计划" placement="top">
                <el-button link type="info" icon="List" @click="handleViewFlightPlan(scope.row)">查看飞行计划</el-button>
              </el-tooltip>
              <el-tooltip content="查看详情" placement="top">
                <el-button link type="primary" icon="View" @click="handleView(scope.row)">查看</el-button>
              </el-tooltip>
            </template>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改设备使用申请对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="flightDeviceUseApplicationFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="申请使用的设备 ID，对应 biz_flight_device.id" prop="deviceId">
          <el-input v-model="form.deviceId" placeholder="请输入申请使用的设备 ID，对应 biz_flight_device.id" />
        </el-form-item>
        <el-form-item label="设备拥有方租户 ID" prop="ownerTenantId">
          <el-input v-model="form.ownerTenantId" placeholder="请输入设备拥有方租户 ID" />
        </el-form-item>
        <el-form-item label="被授权 / 申请方租户 ID" prop="targetTenantId">
          <el-input v-model="form.targetTenantId" placeholder="请输入被授权 / 申请方租户 ID" />
        </el-form-item>
        <el-form-item label="申请说明或备注" prop="applyReason">
          <el-input v-model="form.applyReason" placeholder="请输入申请说明或备注" />
        </el-form-item>
        <el-form-item label="申请占用开始时间" prop="applyStartTime">
          <el-date-picker
            clearable
            v-model="form.applyStartTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择申请占用开始时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="申请占用结束时间" prop="applyEndTime">
          <el-date-picker
            clearable
            v-model="form.applyEndTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择申请占用结束时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="审批意见/备注" prop="approveOpinion">
          <el-input v-model="form.approveOpinion" placeholder="请输入审批意见/备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 编辑/查看/审批申请对话框 -->
    <FlightDeviceUseApplicationEdit
      :visible="editDialogVisible"
      :mode="editMode"
      :edit-id="editId"
      :device-name="selectedRow?.deviceName"
      :owner-tenant-name="selectedRow?.ownerTenantName"
      :applicant-tenant-name="selectedRow?.targetTenantName"
      @success="handleEditSuccess"
      @close="handleEditClose"
    />

    <!-- 飞行计划创建对话框 -->
    <FlightTaskInfoEdit
      :visible="flightPlanDialogVisible"
      :application-info="selectedApplicationForPlan"
      :preset-device-id="selectedApplicationForPlan?.deviceId"
      :device-name="selectedApplicationForPlan?.deviceName"
      @success="handleFlightPlanSuccess"
      @close="handleFlightPlanClose"
    />
  </div>
</template>

<script setup name="FlightDeviceUseApplication" lang="ts">
import {
  listFlightDeviceUseApplication,
  listMyApplications,
  listPendingApprovals,
  listApprovedApplications,
  getFlightDeviceUseApplication,
  delFlightDeviceUseApplication,
  addFlightDeviceUseApplication,
  updateFlightDeviceUseApplication
} from '@/api/biz/flightDeviceUseApplication';
import {
  FlightDeviceUseApplicationVO,
  FlightDeviceUseApplicationQuery,
  FlightDeviceUseApplicationForm
} from '@/api/biz/flightDeviceUseApplication/types';
import { listTenant } from '@/api/system/tenant';
import FlightDeviceUseApplicationEdit from './edit.vue';
import FlightTaskInfoEdit from '@/views/biz/flightTaskInfo/edit.vue';
import { useRoute, useRouter } from 'vue-router';
import { Close } from '@element-plus/icons-vue';
import { useUserStore } from '@/store/modules/user';
import { nextTick, watch } from 'vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const route = useRoute();
const router = useRouter();
const userStore = useUserStore();

const flightDeviceUseApplicationList = ref<FlightDeviceUseApplicationVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(false);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const activeTab = ref('all'); // 当前激活的标签页
const tenantMap = ref<Map<string, string>>(new Map()); // 租户ID到租户名称的映射

// 编辑组件相关状态
const editDialogVisible = ref(false);
const editMode = ref<'apply' | 'approve' | 'view'>('apply');
const editId = ref<string | number>('');
const selectedRow = ref<FlightDeviceUseApplicationVO | null>(null);

// 飞行计划相关状态
const flightPlanDialogVisible = ref(false);
const selectedApplicationForPlan = ref<FlightDeviceUseApplicationVO | null>(null);

// 路由传递的设备信息
const routeDeviceId = ref<string>('');
const routeDeviceName = ref<string>('');

const queryFormRef = ref<ElFormInstance>();
const flightDeviceUseApplicationFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: FlightDeviceUseApplicationForm = {
  id: undefined,
  deviceId: undefined,
  ownerTenantId: undefined,
  targetTenantId: undefined,
  applyReason: undefined,
  applyStartTime: undefined,
  applyEndTime: undefined,
  approveStatus: undefined,
  approveOpinion: undefined
};
const data = reactive<PageData<FlightDeviceUseApplicationForm, FlightDeviceUseApplicationQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    id: undefined,
    deviceId: undefined,
    ownerTenantId: undefined,
    targetTenantId: undefined,
    applyReason: undefined,
    applyStartTime: undefined,
    applyEndTime: undefined,
    approveStatus: undefined,
    approveOpinion: undefined,
    params: {}
  },
  rules: {
    id: [{ required: true, message: '主键 ID不能为空', trigger: 'blur' }],
    deviceId: [{ required: true, message: '申请使用的设备 ID，对应 biz_flight_device.id不能为空', trigger: 'blur' }],
    ownerTenantId: [{ required: true, message: '设备拥有方租户 ID不能为空', trigger: 'blur' }],
    targetTenantId: [{ required: true, message: '被授权 / 申请方租户 ID不能为空', trigger: 'blur' }],
    approveStatus: [{ required: true, message: '审批状态：0=待审批 1=已通过 2=已拒绝不能为空', trigger: 'change' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 获取租户字典 */
const getTenantMap = async () => {
  try {
    const res = await listTenant({
      tenantId: '',
      contactUserName: '',
      contactPhone: '',
      companyName: '',
      pageNum: 1,
      pageSize: 1000
    });
    const tenantList = res.data || res.rows || [];
    const map = new Map<string, string>();
    tenantList.forEach((tenant: any) => {
      map.set(tenant.tenantId, tenant.companyName);
    });
    tenantMap.value = map;
  } catch (error) {
    console.error('获取租户字典失败:', error);
  }
};

/** 查询设备使用申请列表 */
const getList = async () => {
  loading.value = true;

  let apiCall;
  switch (activeTab.value) {
    case 'all':
      apiCall = listFlightDeviceUseApplication(queryParams.value);
      break;
    case 'my_applications':
      apiCall = listMyApplications(queryParams.value);
      break;
    case 'pending_approvals':
      apiCall = listPendingApprovals(queryParams.value);
      break;
    case 'approved_applications':
      apiCall = listApprovedApplications(queryParams.value);
      break;
    default:
      apiCall = listFlightDeviceUseApplication(queryParams.value);
  }

  const res = await apiCall;
  const applications = res.rows || [];

  // 处理数据，添加字典名称
  flightDeviceUseApplicationList.value = applications.map((application) => {
    const processed = { ...application };

    // 添加拥有方租户名称
    if (application.ownerTenantId && tenantMap.value.has(application.ownerTenantId)) {
      processed.ownerTenantName = tenantMap.value.get(application.ownerTenantId);
    }

    // 添加申请方租户名称
    if (application.targetTenantId && tenantMap.value.has(application.targetTenantId)) {
      processed.targetTenantName = tenantMap.value.get(application.targetTenantId);
    }

    return processed;
  });
  total.value = res.total;
  loading.value = false;
};

/** 标签页切换 */
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName;
  queryParams.value.pageNum = 1;
  getList();
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  flightDeviceUseApplicationFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  // 手动清除不在表单中的查询参数
  queryParams.value.id = undefined;
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: FlightDeviceUseApplicationVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '新增设备使用申请';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: FlightDeviceUseApplicationVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getFlightDeviceUseApplication(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改设备使用申请';
};

/** 提交按钮 */
const submitForm = () => {
  flightDeviceUseApplicationFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateFlightDeviceUseApplication(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addFlightDeviceUseApplication(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: FlightDeviceUseApplicationVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除选中的设备使用申请？').finally(() => (loading.value = false));
  await delFlightDeviceUseApplication(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  let exportUrl = 'biz/flightDeviceUseApplication/export';
  let fileName = '设备使用申请';

  // 根据当前标签页确定导出接口和文件名
  switch (activeTab.value) {
    case 'all':
      exportUrl = 'biz/flightDeviceUseApplication/export';
      fileName = '所有申请列表';
      break;
    case 'my_applications':
      exportUrl = 'biz/flightDeviceUseApplication/export'; // TODO: 可能需要专门的导出接口
      fileName = '我发出的申请';
      break;
    case 'pending_approvals':
      exportUrl = 'biz/flightDeviceUseApplication/export'; // TODO: 可能需要专门的导出接口
      fileName = '待我审批的申请';
      break;
    case 'approved_applications':
      exportUrl = 'biz/flightDeviceUseApplication/export'; // TODO: 可能需要专门的导出接口
      fileName = '已审批的申请';
      break;
  }

  proxy?.download(
    exportUrl,
    {
      ...queryParams.value,
      type: activeTab.value // 传递类型参数
    },
    `${fileName}_${new Date().getTime()}.xlsx`
  );
};

/** 查看详情 */
const handleView = (row: FlightDeviceUseApplicationVO) => {
  selectedRow.value = row;
  editId.value = row.id;
  editMode.value = 'view';
  editDialogVisible.value = true;
};

/** 编辑申请 */
const handleEditApplication = (row: FlightDeviceUseApplicationVO) => {
  selectedRow.value = row;
  editId.value = row.id;
  editMode.value = 'apply';
  editDialogVisible.value = true;
};

/** 审批申请 */
const handleApproval = (row: FlightDeviceUseApplicationVO) => {
  selectedRow.value = row;
  editId.value = row.id;
  editMode.value = 'approve';
  editDialogVisible.value = true;
};

/** 安排飞行计划 */
const handleScheduleFlight = (row: FlightDeviceUseApplicationVO) => {
  // 再次检查权限：只有设备拥有方可以安排飞行计划
  if (String(userStore.tenantId) !== String(row.ownerTenantId)) {
    proxy?.$modal.msgError('只有设备拥有方可以安排飞行计划');
    return;
  }

  // 检查申请状态：只有已通过的申请才能安排飞行计划
  if (row.approveStatus !== '1') {
    proxy?.$modal.msgError('只有已通过的申请才能安排飞行计划');
    return;
  }

  // 设置选中的申请信息并打开飞行计划创建弹窗
  selectedApplicationForPlan.value = row;
  flightPlanDialogVisible.value = true;
};

/** 编辑成功处理 */
const handleEditSuccess = () => {
  editDialogVisible.value = false;
  selectedRow.value = null;
  editId.value = '';
  getList(); // 刷新列表
};

/** 编辑关闭处理 */
const handleEditClose = () => {
  editDialogVisible.value = false;
  selectedRow.value = null;
  editId.value = '';
};

/** 飞行计划成功处理 */
const handleFlightPlanSuccess = () => {
  flightPlanDialogVisible.value = false;
  selectedApplicationForPlan.value = null;
  proxy?.$modal.msgSuccess('飞行计划安排成功！');
  // 可以选择刷新列表或跳转到飞行计划页面
  // getList();
};

/** 飞行计划关闭处理 */
const handleFlightPlanClose = () => {
  flightPlanDialogVisible.value = false;
  selectedApplicationForPlan.value = null;
};

/** 获取状态文本 */
const getStatusText = (status: string | undefined) => {
  switch (status) {
    case '0':
      return '待审批';
    case '1':
      return '已通过';
    case '2':
      return '已拒绝';
    default:
      return '未知状态';
  }
};

/** 获取状态标签类型 */
const getStatusTagType = (status: string | undefined) => {
  switch (status) {
    case '0':
      return 'warning';
    case '1':
      return 'success';
    case '2':
      return 'danger';
    default:
      return 'info';
  }
};

/** 清除设备筛选 */
const clearDeviceFilter = () => {
  queryParams.value.deviceId = undefined;
  queryParams.value.ownerTenantId = undefined;
  queryParams.value.targetTenantId = undefined;
  queryParams.value.pageNum = 1;
  // 清除路由传递的设备信息
  routeDeviceId.value = '';
  routeDeviceName.value = '';
  getList();
};

/** 清除申请筛选 */
const clearApplicationFilter = () => {
  queryParams.value.id = undefined;
  queryParams.value.pageNum = 1;
  getList();
};

/** 查看飞行计划 */
const handleViewFlightPlan = (row: FlightDeviceUseApplicationVO) => {
  // 跳转到飞行计划页面
  router.push({
    path: '/Traffic/traffic_01/flightTaskInfo'
  });
};

/** 处理路由查询参数 */
const handleRouteParams = async () => {
  const queryTab = route.query.tab as string;
  const queryDeviceId = route.query.deviceId as string;
  const queryDeviceName = route.query.deviceName as string;
  const queryApplicationId = route.query.applicationId as string;

  // 重置查询参数
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    id: undefined,
    deviceId: undefined,
    ownerTenantId: undefined,
    targetTenantId: undefined,
    applyReason: undefined,
    applyStartTime: undefined,
    applyEndTime: undefined,
    approveStatus: undefined,
    approveOpinion: undefined,
    params: {}
  };

  // 清除路由传递的设备信息
  routeDeviceId.value = '';
  routeDeviceName.value = '';

  // 如果有传递标签参数，自动切换到指定标签
  if (queryTab && ['all', 'my_applications', 'pending_approvals', 'approved_applications'].includes(queryTab)) {
    activeTab.value = queryTab;
  }

  // 如果有传递设备ID参数，自动设置筛选条件
  if (queryDeviceId) {
    queryParams.value.deviceId = queryDeviceId;
    routeDeviceId.value = queryDeviceId;
    routeDeviceName.value = queryDeviceName || '未知设备';

    // 显示友好的提示信息
    if (queryDeviceName) {
      proxy?.$modal.msgSuccess(`已为您筛选设备：${queryDeviceName} (ID: ${queryDeviceId})`);
    }
  }

  // 如果有传递申请ID参数，设置查询条件并获取数据
  if (queryApplicationId) {
    // 设置查询参数，让后端筛选出包含目标申请的结果
    queryParams.value.id = queryApplicationId;
    queryParams.value.pageNum = 1; // 重置到第一页

    await getList();

    // 等待DOM更新后高亮对应的申请记录
    nextTick(() => {
      const targetRecord = flightDeviceUseApplicationList.value.find((item) => item.id === queryApplicationId);
      if (targetRecord) {
        proxy?.$modal.msgSuccess(`已成功定位到申请：${targetRecord.applyReason || '申请ID: ' + queryApplicationId}`);

        // 只显示成功消息，不自动打开弹窗
        // 用户可以手动点击"查看"按钮来查看详情
      } else {
        proxy?.$modal.msgWarning(`未找到申请ID为 ${queryApplicationId} 的记录，可能已被删除或权限不足`);
      }
    });
  } else {
    await getList();
  }
};

// 监听路由变化
watch(
  () => route.query,
  async () => {
    await handleRouteParams();
  },
  { deep: true }
);

onMounted(async () => {
  // 先获取字典，再获取列表数据
  await getTenantMap();
  // 处理路由查询参数
  await handleRouteParams();
});
</script>

<style scoped>
.text-gray-500 {
  color: #6b7280;
  font-size: 12px;
}
</style>
