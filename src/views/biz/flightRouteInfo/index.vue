<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="航线名称" prop="routeName">
              <el-input v-model="queryParams.routeName" placeholder="请输入航线名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
<!--            <el-form-item label="起点" prop="startPoint">-->
<!--              <el-input v-model="queryParams.startPoint" placeholder="请输入起点" clearable @keyup.enter="handleQuery" />-->
<!--            </el-form-item>-->
<!--            <el-form-item label="终点" prop="endPoint">-->
<!--              <el-input v-model="queryParams.endPoint" placeholder="请输入终点" clearable @keyup.enter="handleQuery" />-->
<!--            </el-form-item>-->
<!--            <el-form-item label="任务动作" prop="taskAction">-->
<!--              <el-select v-model="queryParams.taskAction" placeholder="请选择任务动作" clearable >-->
<!--                <el-option v-for="dict in flight_task_action" :key="dict.value" :label="dict.label" :value="dict.value"/>-->
<!--              </el-select>-->
<!--            </el-form-item>-->
<!--            <el-form-item label="总里程" prop="totalDistance">-->
<!--              <el-input v-model="queryParams.totalDistance" placeholder="请输入总里程" clearable @keyup.enter="handleQuery" />-->
<!--            </el-form-item>-->
            <el-form-item label="路段名称" prop="roadName">
              <el-input v-model="queryParams.roadName" placeholder="请输入路段名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
<!--            <el-form-item label="飞行时长" prop="flightDuration">-->
<!--              <el-date-picker clearable-->
<!--                v-model="queryParams.flightDuration"-->
<!--                type="date"-->
<!--                value-format="YYYY-MM-DD"-->
<!--                placeholder="请选择飞行时长"-->
<!--              />-->
<!--            </el-form-item>-->
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
<!--          <el-col :span="1.5">-->
<!--            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['biz:flightRouteInfo:add']">新增</el-button>-->
<!--          </el-col>-->
<!--          <el-col :span="1.5">-->
<!--            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['biz:flightRouteInfo:edit']">修改</el-button>-->
<!--          </el-col>-->
<!--          <el-col :span="1.5">-->
<!--            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['biz:flightRouteInfo:remove']">删除</el-button>-->
<!--          </el-col>-->
<!--          <el-col :span="1.5">-->
<!--            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['biz:flightRouteInfo:export']">导出</el-button>-->
<!--          </el-col>-->
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="flightRouteInfoList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
<!--        <el-table-column label="航线id: 主键" align="center" prop="id" v-if="true" />-->
        <el-table-column label="航线编号" align="center" prop="routeNo" />
        <el-table-column label="航线名称" align="center" prop="routeName" />
<!--        <el-table-column label="起点" align="center" prop="startPoint" />-->
<!--        <el-table-column label="终点" align="center" prop="endPoint" />-->
<!--        <el-table-column label="垂直起飞高度" align="center" prop="verticalTakeoffHeight" />-->
<!--        <el-table-column label="飞行高度" align="center" prop="flightAltitude" />-->
<!--        <el-table-column label="飞行速度" align="center" prop="flightSpeed" />-->
<!--        <el-table-column label="任务动作" align="center" prop="taskAction">-->
<!--          <template #default="scope">-->
<!--            <dict-tag :options="flight_task_action" :value="scope.row.taskAction"/>-->
<!--          </template>-->
<!--        </el-table-column>-->
<!--        <el-table-column label="总里程" align="center" prop="totalDistance" />-->
        <el-table-column label="路段名称" align="center" prop="roadName" />
        <el-table-column label="起止桩号" align="center" prop="startEndPileNo" />
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
<!--        <el-table-column label="更新时间" align="center" prop="updateTime" width="180">-->
<!--          <template #default="scope">-->
<!--            <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>-->
<!--          </template>-->
<!--        </el-table-column>-->
<!--        <el-table-column label="飞行时长" align="center" prop="flightDuration" width="180">-->
<!--          <template #default="scope">-->
<!--            <span>{{ parseTime(scope.row.flightDuration, '{y}-{m}-{d}') }}</span>-->
<!--          </template>-->
<!--        </el-table-column>-->
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['biz:flightRouteInfo:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['biz:flightRouteInfo:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改航线管理对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="680px" append-to-body>
      <el-form ref="flightRouteInfoFormRef" :model="form" :rules="rules" label-width="128px">
        <el-form-item label="航线编号" prop="routeNo">
          <el-input v-model="form.routeNo" placeholder="请输入航线编号" />
        </el-form-item>
        <el-form-item label="航线名称" prop="routeName">
          <el-input v-model="form.routeName" placeholder="请输入航线名称" />
        </el-form-item>
<!--        <el-form-item label="起点" prop="startPoint">-->
<!--          <el-input v-model="form.startPoint" placeholder="请输入起点" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="终点" prop="endPoint">-->
<!--          <el-input v-model="form.endPoint" placeholder="请输入终点" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="垂直起飞高度" prop="verticalTakeoffHeight">-->
<!--          <el-input v-model="form.verticalTakeoffHeight" placeholder="请输入垂直起飞高度" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="飞行高度" prop="flightAltitude">-->
<!--          <el-input v-model="form.flightAltitude" placeholder="请输入飞行高度" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="飞行速度" prop="flightSpeed">-->
<!--          <el-input v-model="form.flightSpeed" placeholder="请输入飞行速度" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="任务动作" prop="taskAction">-->
<!--          <el-select v-model="form.taskAction" placeholder="请选择任务动作">-->
<!--            <el-option-->
<!--                v-for="dict in flight_task_action"-->
<!--                :key="dict.value"-->
<!--                :label="dict.label"-->
<!--                :value="dict.value"-->
<!--            ></el-option>-->
<!--          </el-select>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="云台俯仰角" prop="gimbalPitchAngle">-->
<!--          <el-input v-model="form.gimbalPitchAngle" placeholder="请输入云台俯仰角" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="云台平移角" prop="gimbalRollAngle">-->
<!--          <el-input v-model="form.gimbalRollAngle" placeholder="请输入云台平移角" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="总里程" prop="totalDistance">-->
<!--          <el-input v-model="form.totalDistance" placeholder="请输入总里程" />-->
<!--        </el-form-item>-->
        <el-form-item label="路段名称" prop="roadName">
          <el-input v-model="form.roadName" placeholder="请输入路段名称" />
        </el-form-item>
        <el-form-item label="起止桩号" prop="startEndPileNo">
          <el-input v-model="form.startEndPileNo" placeholder="请输入起止桩号" />
        </el-form-item>
<!--        <el-form-item label="KML文件" prop="kmlFileUrl">-->
<!--          <file-upload v-model="form.kmlFileUrl"/>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="飞行时长" prop="flightDuration">-->
<!--          <el-date-picker clearable-->
<!--            v-model="form.flightDuration"-->
<!--            type="datetime"-->
<!--            value-format="YYYY-MM-DD HH:mm:ss"-->
<!--            placeholder="请选择飞行时长">-->
<!--          </el-date-picker>-->
<!--        </el-form-item>-->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="FlightRouteInfo" lang="ts">
import { listFlightRouteInfo, getFlightRouteInfo, delFlightRouteInfo, addFlightRouteInfo, updateFlightRouteInfo } from '@/api/biz/flightRouteInfo';
import { FlightRouteInfoVO, FlightRouteInfoQuery, FlightRouteInfoForm } from '@/api/biz/flightRouteInfo/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { flight_task_action } = toRefs<any>(proxy?.useDict('flight_task_action'));

const flightRouteInfoList = ref<FlightRouteInfoVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(false);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const flightRouteInfoFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: FlightRouteInfoForm = {
  id: undefined,
  routeNo: undefined,
  routeName: undefined,
  kmlFileUrl: undefined,
  startPoint: undefined,
  endPoint: undefined,
  verticalTakeoffHeight: undefined,
  flightAltitude: undefined,
  flightSpeed: undefined,
  taskAction: undefined,
  gimbalPitchAngle: undefined,
  gimbalRollAngle: undefined,
  totalDistance: undefined,
  flightDuration: undefined,
  roadName: undefined,
  startEndPileNo: undefined,
  remark: undefined,
}
const data = reactive<PageData<FlightRouteInfoForm, FlightRouteInfoQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    routeNo: undefined,
    routeName: undefined,
    kmlFileUrl: undefined,
    startPoint: undefined,
    endPoint: undefined,
    verticalTakeoffHeight: undefined,
    flightAltitude: undefined,
    flightSpeed: undefined,
    taskAction: undefined,
    gimbalPitchAngle: undefined,
    gimbalRollAngle: undefined,
    totalDistance: undefined,
    flightDuration: undefined,
    roadName: undefined,
    startEndPileNo: undefined,
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "航线id: 主键不能为空", trigger: "blur" }
    ],
    routeName: [
      { required: true, message: "航线名称不能为空", trigger: "blur" }
    ],
    // startPoint: [
    //   { required: true, message: "起点不能为空", trigger: "blur" }
    // ],
    // endPoint: [
    //   { required: true, message: "终点不能为空", trigger: "blur" }
    // ],
    // verticalTakeoffHeight: [
    //   { required: true, message: "垂直起飞高度不能为空", trigger: "blur" }
    // ],
    // flightAltitude: [
    //   { required: true, message: "飞行高度不能为空", trigger: "blur" }
    // ],
    // flightSpeed: [
    //   { required: true, message: "飞行速度不能为空", trigger: "blur" }
    // ],
    // taskAction: [
    //   { required: true, message: "任务动作不能为空", trigger: "change" }
    // ],
    // gimbalPitchAngle: [
    //   { required: true, message: "云台俯仰角不能为空", trigger: "blur" }
    // ],
    // gimbalRollAngle: [
    //   { required: true, message: "云台平移角不能为空", trigger: "blur" }
    // ],
    // totalDistance: [
    //   { required: true, message: "总里程不能为空", trigger: "blur" }
    // ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询航线管理列表 */
const getList = async () => {
  loading.value = true;
  const res = await listFlightRouteInfo(queryParams.value);
  flightRouteInfoList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  flightRouteInfoFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: FlightRouteInfoVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加航线管理";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: FlightRouteInfoVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getFlightRouteInfo(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改航线管理";
}

/** 提交按钮 */
const submitForm = () => {
  flightRouteInfoFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateFlightRouteInfo(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addFlightRouteInfo(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: FlightRouteInfoVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除航线管理编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delFlightRouteInfo(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'biz/flightRouteInfo/export',
    {
      ...queryParams.value
    },
    `航线.xlsx`
  );
};

onMounted(() => {
  getList();
});
</script>
