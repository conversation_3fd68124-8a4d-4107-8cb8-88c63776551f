<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="关联的任务结果ID" prop="taskId">
              <el-input v-model="queryParams.taskId" placeholder="请输入关联的任务结果ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="进度时间" prop="progressTime">
              <el-date-picker clearable
                v-model="queryParams.progressTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择进度时间"
              />
            </el-form-item>
            <el-form-item label="进度状态" prop="progressStatus">
              <el-select v-model="queryParams.progressStatus" placeholder="请选择进度状态" clearable >
                <el-option v-for="dict in flight_task_progress_status" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="进度详细信息" prop="progressDetails">
              <el-input v-model="queryParams.progressDetails" placeholder="请输入进度详细信息" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['biz:flightTaskProgress:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['biz:flightTaskProgress:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['biz:flightTaskProgress:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['biz:flightTaskProgress:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="flightTaskProgressList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="进度ID，主键" align="center" prop="id" v-if="true" />
        <el-table-column label="关联的任务结果ID" align="center" prop="taskId" />
        <el-table-column label="进度时间" align="center" prop="progressTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.progressTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="进度状态" align="center" prop="progressStatus">
          <template #default="scope">
            <dict-tag :options="flight_task_progress_status" :value="scope.row.progressStatus"/>
          </template>
        </el-table-column>
        <el-table-column label="进度详细信息" align="center" prop="progressDetails" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['biz:flightTaskProgress:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['biz:flightTaskProgress:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改任务进度对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="680px" append-to-body>
      <el-form ref="flightTaskProgressFormRef" :model="form" :rules="rules" label-width="128px">
        <el-form-item label="关联的任务结果ID" prop="taskId">
          <el-input v-model="form.taskId" placeholder="请输入关联的任务结果ID" />
        </el-form-item>
        <el-form-item label="进度时间" prop="progressTime">
          <el-date-picker clearable
            v-model="form.progressTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择进度时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="进度状态" prop="progressStatus">
          <el-select v-model="form.progressStatus" placeholder="请选择进度状态">
            <el-option
                v-for="dict in flight_task_progress_status"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="进度详细信息" prop="progressDetails">
            <el-input v-model="form.progressDetails" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="FlightTaskProgress" lang="ts">
import { listFlightTaskProgress, getFlightTaskProgress, delFlightTaskProgress, addFlightTaskProgress, updateFlightTaskProgress } from '@/api/biz/flightTaskProgress';
import { FlightTaskProgressVO, FlightTaskProgressQuery, FlightTaskProgressForm } from '@/api/biz/flightTaskProgress/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { flight_task_progress_status } = toRefs<any>(proxy?.useDict('flight_task_progress_status'));

const flightTaskProgressList = ref<FlightTaskProgressVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(false);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const flightTaskProgressFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: FlightTaskProgressForm = {
  id: undefined,
  taskId: undefined,
  progressTime: undefined,
  progressStatus: undefined,
  progressDetails: undefined,
}
const data = reactive<PageData<FlightTaskProgressForm, FlightTaskProgressQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    taskId: undefined,
    progressTime: undefined,
    progressStatus: undefined,
    progressDetails: undefined,
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "进度ID，主键不能为空", trigger: "blur" }
    ],
    taskId: [
      { required: true, message: "关联的任务结果ID不能为空", trigger: "blur" }
    ],
    progressTime: [
      { required: true, message: "进度时间不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询任务进度列表 */
const getList = async () => {
  loading.value = true;
  const res = await listFlightTaskProgress(queryParams.value);
  flightTaskProgressList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  flightTaskProgressFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: FlightTaskProgressVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加任务进度";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: FlightTaskProgressVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getFlightTaskProgress(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改任务进度";
}

/** 提交按钮 */
const submitForm = () => {
  flightTaskProgressFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateFlightTaskProgress(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addFlightTaskProgress(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: FlightTaskProgressVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除任务进度编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delFlightTaskProgress(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('biz/flightTaskProgress/export', {
    ...queryParams.value
  }, `flightTaskProgress_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
