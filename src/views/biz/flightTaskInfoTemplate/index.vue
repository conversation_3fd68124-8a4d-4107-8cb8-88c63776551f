<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="任务名称" prop="taskName">
              <el-input v-model="queryParams.taskName" placeholder="请输入任务名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="任务编号" prop="taskNo">
              <el-input v-model="queryParams.taskNo" placeholder="请输入任务编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="任务频率" prop="taskFrequency">
              <el-input v-model="queryParams.taskFrequency" placeholder="请输入任务频率" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="任务计划开始时间" prop="plannedStartTime">
              <el-date-picker clearable
                v-model="queryParams.plannedStartTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择任务计划开始时间"
              />
            </el-form-item>
            <el-form-item label="任务计划结束时间" prop="plannedEndTime">
              <el-date-picker clearable
                v-model="queryParams.plannedEndTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择任务计划结束时间"
              />
            </el-form-item>
            <el-form-item label="电池容量达到" prop="batteryCapacityReached">
              <el-input v-model="queryParams.batteryCapacityReached" placeholder="请输入电池容量达到" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="储存容量达到" prop="storageCapacityReached">
              <el-input v-model="queryParams.storageCapacityReached" placeholder="请输入储存容量达到" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="实际开始时间" prop="actualStartTime">
              <el-date-picker clearable
                v-model="queryParams.actualStartTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择实际开始时间"
              />
            </el-form-item>
            <el-form-item label="实际结束时间" prop="actualEndTime">
              <el-date-picker clearable
                v-model="queryParams.actualEndTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择实际结束时间"
              />
            </el-form-item>
            <el-form-item label="返航高度" prop="returnAltitude">
              <el-input v-model="queryParams.returnAltitude" placeholder="请输入返航高度" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="矢量操作" prop="vectorOperation">
              <el-input v-model="queryParams.vectorOperation" placeholder="请输入矢量操作" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="任务对应设备的拥有租户 ID" prop="ownerTenantId">
              <el-input v-model="queryParams.ownerTenantId" placeholder="请输入任务对应设备的拥有租户 ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="任务实际执行租户 ID" prop="targetTenantId">
              <el-input v-model="queryParams.targetTenantId" placeholder="请输入任务实际执行租户 ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="执行设备序列号" prop="deviceSn">
              <el-input v-model="queryParams.deviceSn" placeholder="请输入执行设备序列号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="执行航线编号" prop="routeNo">
              <el-input v-model="queryParams.routeNo" placeholder="请输入执行航线编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="AI识别模型，多个模型用逗号分隔，如：vehicle_dense,psw,person_dense" prop="aiRecognitionModels">
              <el-input v-model="queryParams.aiRecognitionModels" placeholder="请输入AI识别模型，多个模型用逗号分隔，如：vehicle_dense,psw,person_dense" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['biz:flightTaskInfoTemplate:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['biz:flightTaskInfoTemplate:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['biz:flightTaskInfoTemplate:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['biz:flightTaskInfoTemplate:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="flightTaskInfoTemplateList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="任务ID，主键" align="center" prop="id" v-if="true" />
        <el-table-column label="任务名称" align="center" prop="taskName" />
        <el-table-column label="任务编号" align="center" prop="taskNo" />
        <el-table-column label="任务类型" align="center" prop="taskType" />
        <el-table-column label="任务频率" align="center" prop="taskFrequency" />
        <el-table-column label="任务计划开始时间" align="center" prop="plannedStartTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.plannedStartTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="任务计划结束时间" align="center" prop="plannedEndTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.plannedEndTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="电池容量达到" align="center" prop="batteryCapacityReached" />
        <el-table-column label="储存容量达到" align="center" prop="storageCapacityReached" />
        <el-table-column label="备注" align="center" prop="notes" />
        <el-table-column label="任务状态" align="center" prop="taskStatus" />
        <el-table-column label="实际开始时间" align="center" prop="actualStartTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.actualStartTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="实际结束时间" align="center" prop="actualEndTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.actualEndTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="返航高度" align="center" prop="returnAltitude" />
        <el-table-column label="矢量操作" align="center" prop="vectorOperation" />
        <el-table-column label="任务对应设备的拥有租户 ID" align="center" prop="ownerTenantId" />
        <el-table-column label="任务实际执行租户 ID" align="center" prop="targetTenantId" />
        <el-table-column label="执行设备序列号" align="center" prop="deviceSn" />
        <el-table-column label="执行航线编号" align="center" prop="routeNo" />
        <el-table-column label="执行任务的时间点，支持多时间段配置，JSON格式" align="center" prop="taskPeriodsJson" />
        <el-table-column label="AI识别模型，多个模型用逗号分隔，如：vehicle_dense,psw,person_dense" align="center" prop="aiRecognitionModels" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['biz:flightTaskInfoTemplate:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['biz:flightTaskInfoTemplate:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改飞行任务计划模板（用于飞行任务按需执行）对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="flightTaskInfoTemplateFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="任务名称" prop="taskName">
          <el-input v-model="form.taskName" placeholder="请输入任务名称" />
        </el-form-item>
        <el-form-item label="任务编号" prop="taskNo">
          <el-input v-model="form.taskNo" placeholder="请输入任务编号" />
        </el-form-item>
        <el-form-item label="任务频率" prop="taskFrequency">
          <el-input v-model="form.taskFrequency" placeholder="请输入任务频率" />
        </el-form-item>
        <el-form-item label="任务计划开始时间" prop="plannedStartTime">
          <el-date-picker clearable
            v-model="form.plannedStartTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择任务计划开始时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="任务计划结束时间" prop="plannedEndTime">
          <el-date-picker clearable
            v-model="form.plannedEndTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择任务计划结束时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="电池容量达到" prop="batteryCapacityReached">
          <el-input v-model="form.batteryCapacityReached" placeholder="请输入电池容量达到" />
        </el-form-item>
        <el-form-item label="储存容量达到" prop="storageCapacityReached">
          <el-input v-model="form.storageCapacityReached" placeholder="请输入储存容量达到" />
        </el-form-item>
        <el-form-item label="备注" prop="notes">
            <el-input v-model="form.notes" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="返航高度" prop="returnAltitude">
          <el-input v-model="form.returnAltitude" placeholder="请输入返航高度" />
        </el-form-item>
        <el-form-item label="矢量操作" prop="vectorOperation">
          <el-input v-model="form.vectorOperation" placeholder="请输入矢量操作" />
        </el-form-item>
        <el-form-item label="任务对应设备的拥有租户 ID" prop="ownerTenantId">
          <el-input v-model="form.ownerTenantId" placeholder="请输入任务对应设备的拥有租户 ID" />
        </el-form-item>
        <el-form-item label="任务实际执行租户 ID" prop="targetTenantId">
          <el-input v-model="form.targetTenantId" placeholder="请输入任务实际执行租户 ID" />
        </el-form-item>
        <el-form-item label="执行设备序列号" prop="deviceSn">
          <el-input v-model="form.deviceSn" placeholder="请输入执行设备序列号" />
        </el-form-item>
        <el-form-item label="执行航线编号" prop="routeNo">
          <el-input v-model="form.routeNo" placeholder="请输入执行航线编号" />
        </el-form-item>
        <el-form-item label="AI识别模型，多个模型用逗号分隔，如：vehicle_dense,psw,person_dense" prop="aiRecognitionModels">
          <el-input v-model="form.aiRecognitionModels" placeholder="请输入AI识别模型，多个模型用逗号分隔，如：vehicle_dense,psw,person_dense" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="FlightTaskInfoTemplate" lang="ts">
import { listFlightTaskInfoTemplate, getFlightTaskInfoTemplate, delFlightTaskInfoTemplate, addFlightTaskInfoTemplate, updateFlightTaskInfoTemplate } from '@/api/biz/flightTaskInfoTemplate';
import { FlightTaskInfoTemplateVO, FlightTaskInfoTemplateQuery, FlightTaskInfoTemplateForm } from '@/api/biz/flightTaskInfoTemplate/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const flightTaskInfoTemplateList = ref<FlightTaskInfoTemplateVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(false);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const flightTaskInfoTemplateFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: FlightTaskInfoTemplateForm = {
  id: undefined,
  taskName: undefined,
  taskNo: undefined,
  taskType: undefined,
  taskFrequency: undefined,
  plannedStartTime: undefined,
  plannedEndTime: undefined,
  batteryCapacityReached: undefined,
  storageCapacityReached: undefined,
  notes: undefined,
  taskStatus: undefined,
  returnAltitude: undefined,
  vectorOperation: undefined,
  ownerTenantId: undefined,
  targetTenantId: undefined,
  deviceSn: undefined,
  routeNo: undefined,
  taskPeriodsJson: undefined,
  aiRecognitionModels: undefined
}
const data = reactive<PageData<FlightTaskInfoTemplateForm, FlightTaskInfoTemplateQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    taskName: undefined,
    taskNo: undefined,
    taskType: undefined,
    taskFrequency: undefined,
    plannedStartTime: undefined,
    plannedEndTime: undefined,
    batteryCapacityReached: undefined,
    storageCapacityReached: undefined,
    taskStatus: undefined,
    actualStartTime: undefined,
    actualEndTime: undefined,
    returnAltitude: undefined,
    vectorOperation: undefined,
    ownerTenantId: undefined,
    targetTenantId: undefined,
    deviceSn: undefined,
    routeNo: undefined,
    taskPeriodsJson: undefined,
    aiRecognitionModels: undefined,
    params: {
    }
  },
  rules: {
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询飞行任务计划模板（用于飞行任务按需执行）列表 */
const getList = async () => {
  loading.value = true;
  const res = await listFlightTaskInfoTemplate(queryParams.value);
  flightTaskInfoTemplateList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  flightTaskInfoTemplateFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: FlightTaskInfoTemplateVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加飞行任务计划模板（用于飞行任务按需执行）";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: FlightTaskInfoTemplateVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getFlightTaskInfoTemplate(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改飞行任务计划模板（用于飞行任务按需执行）";
}

/** 提交按钮 */
const submitForm = () => {
  flightTaskInfoTemplateFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateFlightTaskInfoTemplate(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addFlightTaskInfoTemplate(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: FlightTaskInfoTemplateVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除飞行任务计划模板（用于飞行任务按需执行）编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delFlightTaskInfoTemplate(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('biz/flightTaskInfoTemplate/export', {
    ...queryParams.value
  }, `flightTaskInfoTemplate_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
