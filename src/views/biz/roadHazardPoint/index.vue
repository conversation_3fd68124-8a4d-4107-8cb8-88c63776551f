<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="项目名称" prop="projectName">
              <el-input v-model="queryParams.projectName" placeholder="请输入项目名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="桩号" prop="pileNo">
              <el-input v-model="queryParams.pileNo" placeholder="请输入桩号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="隐患点名称" prop="hazardName">
              <el-input v-model="queryParams.hazardName" placeholder="请输入隐患点名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['biz:roadHazardPoint:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['biz:roadHazardPoint:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['biz:roadHazardPoint:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['biz:roadHazardPoint:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="roadHazardPointList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" align="center" width="80">
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="项目名称" align="center" prop="projectName" />
        <el-table-column label="桩号" align="center" prop="pileNo" />
        <el-table-column label="隐患点类型" align="center" prop="hazardType">
          <template #default="scope">
            <dict-tag :options="road_hazard_point_type" :value="scope.row.hazardType" />
          </template>
        </el-table-column>
        <el-table-column label="隐患点名称" align="center" prop="hazardName" />
        <el-table-column label="最新巡检时间" align="center" prop="latestInspectTime" width="180">
          <template #default="scope">
            <span>{{ scope.row.latestInspectTime ? parseTime(scope.row.latestInspectTime, '{y}-{m}-{d} {h}:{i}:{s}') : '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="100">
          <template #default="scope">
            <el-button link type="success" @click="handleViewInspectionRecords(scope.row)">巡检记录</el-button>
            <!-- 暂时隐藏修改和删除按钮 -->
            <!-- <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['biz:roadHazardPoint:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['biz:roadHazardPoint:remove']"></el-button>
            </el-tooltip> -->
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改公路隐患点对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="600px" append-to-body>
      <el-form ref="roadHazardPointFormRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="所属项目" prop="projectId">
          <el-select v-model="form.projectId" placeholder="请选择所属项目" filterable @change="handleProjectChange">
            <el-option
              v-for="project in projectList"
              :key="project.id"
              :label="`${project.projectName}(${project.projectDesc || '无描述'})`"
              :value="project.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="桩号" prop="pileNo">
          <el-input v-model="form.pileNo" placeholder="请输入桩号" />
        </el-form-item>
        <el-form-item label="隐患点类型" prop="hazardType">
          <el-select v-model="form.hazardType" placeholder="请选择隐患点类型">
            <el-option v-for="dict in road_hazard_point_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="隐患点名称" prop="hazardName">
          <el-input v-model="form.hazardName" placeholder="请输入隐患点名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 巡检记录弹窗 -->
    <el-dialog :title="`${currentHazardPointName} - 巡检记录`" v-model="inspectRecordDialogVisible" width="80%" append-to-body destroy-on-close>
      <div v-if="inspectRecordDialogVisible">
        <el-card shadow="never">
          <el-table :data="inspectRecordList" border v-loading="inspectRecordLoading" size="small">
            <el-table-column type="index" label="序号" width="60" align="center" />
            <el-table-column label="隐患点名称" align="center" width="150">
              <template #default="scope">
                <span>{{ currentHazardPointName }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="taskName" label="巡检任务" align="center" />
            <el-table-column label="巡检时间" align="center" width="320">
              <template #default="scope">
                <div>
                  {{ scope.row.actualStartTime ? parseTime(scope.row.actualStartTime, '{y}-{m}-{d} {h}:{i}:{s}') : '-' }}
                  ~
                  {{ scope.row.actualEndTime ? parseTime(scope.row.actualEndTime, '{y}-{m}-{d} {h}:{i}:{s}') : '-' }}
                </div>
                <div v-if="computeDuration(scope.row.actualStartTime, scope.row.actualEndTime)">
                  耗时:
                  {{ computeDuration(scope.row.actualStartTime, scope.row.actualEndTime) }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="是否存在风险" align="center" prop="pointHasRisk">
              <template #default="scope">
                <dict-tag :options="road_hazard_has_risk" :value="scope.row.pointHasRisk" />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100" align="center">
              <template #default="scope">
                <el-button link type="primary" @click="handleViewImages(scope.row)">查看图片</el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="inspectRecordTotal > 0"
            :total="inspectRecordTotal"
            v-model:page="inspectRecordQueryParams.pageNum"
            v-model:limit="inspectRecordQueryParams.pageSize"
            @pagination="getInspectRecordList"
            class="mt-4"
          />
        </el-card>
      </div>
    </el-dialog>

    <!-- 图片查看器 -->
    <HazardPointImageViewer
      v-model="imageViewerVisible"
      :hazard-point-id="currentHazardPointId!"
      :inspect-record-id="currentInspectRecordId!"
      :hazard-point-name="currentHazardPointName"
    />
  </div>
</template>

<script setup name="RoadHazardPoint" lang="ts">
import {
  listRoadHazardPoint,
  getRoadHazardPoint,
  delRoadHazardPoint,
  addRoadHazardPoint,
  updateRoadHazardPoint,
  getInspectRecordsByHazardPointId
} from '@/api/biz/roadHazardPoint';
import { RoadHazardPointVO, RoadHazardPointQuery, RoadHazardPointForm, HazardPointInspectRecordVO } from '@/api/biz/roadHazardPoint/types';
import { listTenantProjects } from '@/api/biz/project';
import { ProjectVO } from '@/api/biz/project/types';
import HazardPointImageViewer from '@/components/HazardPointImageViewer.vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { road_hazard_point_type, road_hazard_has_risk } = toRefs<any>(proxy?.useDict('road_hazard_point_type', 'road_hazard_has_risk'));

const roadHazardPointList = ref<RoadHazardPointVO[]>([]);
const projectList = ref<ProjectVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(false);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

// 巡检记录弹窗相关状态
const inspectRecordDialogVisible = ref(false);
const inspectRecordList = ref<HazardPointInspectRecordVO[]>([]);
const inspectRecordLoading = ref(false);
const inspectRecordTotal = ref(0);
const currentHazardPointId = ref<number | string>();
const currentHazardPointName = ref<string>();
const inspectRecordQueryParams = reactive({
  pageNum: 1,
  pageSize: 10
});

// 图片查看器相关状态
const imageViewerVisible = ref(false);
const currentInspectRecordId = ref<string | number>();

const queryFormRef = ref<ElFormInstance>();
const roadHazardPointFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: RoadHazardPointForm = {
  id: undefined,
  projectId: undefined,
  projectName: undefined,
  pileNo: undefined,
  hazardType: undefined,
  hazardName: undefined
};
const data = reactive<PageData<RoadHazardPointForm, RoadHazardPointQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    projectName: undefined,
    pileNo: undefined,
    hazardType: undefined,
    hazardName: undefined,
    params: {}
  },
  rules: {
    projectId: [{ required: true, message: '所属项目不能为空', trigger: 'change' }],
    pileNo: [{ required: true, message: '桩号不能为空', trigger: 'blur' }],
    hazardType: [{ required: true, message: '隐患点类型不能为空', trigger: 'change' }],
    hazardName: [{ required: true, message: '隐患点名称不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 获取项目列表 */
const getProjectList = async () => {
  try {
    const res = await listTenantProjects();
    projectList.value = res.data || [];
  } catch (error) {
    console.error('获取项目列表失败:', error);
  }
};

/** 查询公路隐患点列表 */
const getList = async () => {
  loading.value = true;
  const res = await listRoadHazardPoint(queryParams.value);
  roadHazardPointList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  roadHazardPointFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: RoadHazardPointVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = async () => {
  reset();
  await getProjectList();
  dialog.visible = true;
  dialog.title = '添加公路隐患点';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: RoadHazardPointVO) => {
  reset();
  await getProjectList();
  const _id = row?.id || ids.value[0];
  const res = await getRoadHazardPoint(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改公路隐患点';
};

/** 提交按钮 */
const submitForm = () => {
  roadHazardPointFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateRoadHazardPoint(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addRoadHazardPoint(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: RoadHazardPointVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除公路隐患点编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delRoadHazardPoint(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'biz/roadHazardPoint/export',
    {
      ...queryParams.value
    },
    `roadHazardPoint_${new Date().getTime()}.xlsx`
  );
};

/** 处理项目选择变化 */
const handleProjectChange = (projectId: string | number) => {
  if (projectId) {
    const selectedProject = projectList.value.find((project) => project.id === projectId);
    if (selectedProject) {
      form.value.projectName = selectedProject.projectName;
    }
  } else {
    form.value.projectName = undefined;
  }
};

/**
 * 计算两个时间的耗时，返回 "xxminyy s" 格式，如果时间为空则返回空字符串
 */
const computeDuration = (start?: string, end?: string): string => {
  if (!start || !end) return '';
  const startTime = new Date(start.replace(' ', 'T')).getTime();
  const endTime = new Date(end.replace(' ', 'T')).getTime();
  const duration = endTime - startTime;
  if (duration <= 0) return '';

  const minutes = Math.floor(duration / (1000 * 60));
  const seconds = Math.floor((duration % (1000 * 60)) / 1000);

  return `${minutes}min${seconds}s`;
};

/** 获取巡检记录列表 */
const getInspectRecordList = async () => {
  if (!currentHazardPointId.value) return;

  inspectRecordLoading.value = true;
  try {
    const res = await getInspectRecordsByHazardPointId(currentHazardPointId.value, inspectRecordQueryParams);
    inspectRecordList.value = res.rows;
    inspectRecordTotal.value = res.total;
  } catch (error) {
    console.error('获取巡检记录列表失败:', error);
    proxy?.$modal.msgError('获取巡检记录列表失败');
  } finally {
    inspectRecordLoading.value = false;
  }
};

/** 查看巡检记录 */
const handleViewInspectionRecords = (row: RoadHazardPointVO) => {
  currentHazardPointId.value = row.id;
  currentHazardPointName.value = row.hazardName;
  inspectRecordQueryParams.pageNum = 1;
  inspectRecordDialogVisible.value = true;
  getInspectRecordList();
};

/** 查看图片 */
const handleViewImages = (row: HazardPointInspectRecordVO) => {
  console.log('查看图片 - 隐患点ID:', currentHazardPointId.value);
  console.log('查看图片 - 巡检记录ID:', row.recordId);
  console.log('查看图片 - 隐患点名称:', currentHazardPointName.value);

  currentInspectRecordId.value = row.recordId;
  imageViewerVisible.value = true;
};

onMounted(() => {
  getList();
});
</script>
