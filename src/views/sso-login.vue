<!-- Sa-Token-SSO-Client端-登录页 -->
<template>
  <div class="sso-login-container">
    <div v-if="loading" class="loading-text">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>正在处理SSO登录，请稍候...</span>
    </div>
    <div v-else class="loading-text">正在跳转到认证中心...</div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import router from '@/router';
import { getSsoAuthUrl } from '@/api/sso/index';
import { LoginData } from '@/api/types';
import { useUserStore } from '@/store/modules/user';
import { Loading } from '@element-plus/icons-vue';
import { to } from 'await-to-js';

// 加载状态
const loading = ref(false);

// 获取参数
const getParam = (name: string): string => {
  const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)');
  const r = window.location.search.substring(1).match(reg);
  if (r != null) return decodeURIComponent(r[2]);
  return '';
};

const redirect = getParam('redirect') || (router.currentRoute.value.query.back as string);
const ticket = getParam('saToken') || (router.currentRoute.value.query.ticket as string);
const code = getParam('code') || '';
const tenantId = getParam('tenantId') || '000000';

// 页面加载后触发
onMounted(() => {
  if (ticket) {
    doLoginByTicketHandler(ticket);
  } else {
    goSsoAuthUrl();
  }
});

// 重定向至认证中心
function goSsoAuthUrl() {
  loading.value = true;
  getSsoAuthUrl({ clientLoginUrl: location.href })
    .then((res) => {
      location.href = res.data;
    })
    .catch((err) => {
      console.error('获取SSO认证中心URL失败', err);
      loading.value = false;
    });
}

// 处理错误
function handleError(error: any) {
  console.error('登录失败', error);
  loading.value = false;
  // 登录失败，重定向到认证中心
  goSsoAuthUrl();
}

// 根据ticket值登录
async function doLoginByTicketHandler(ticketValue: string) {
  loading.value = true;
  try {
    const data: LoginData = {
      ticket: ticketValue,
      socialCode: code,
      tenantId: tenantId,
      clientId: import.meta.env.VITE_APP_CLIENT_ID,
      grantType: 'timpPortal'
    };

    // 执行普通登录
    const userStore = useUserStore();
    const [err] = await to(userStore.login(data));
    if (!err) {
      // 普通登录成功，按原有逻辑跳转
      const redirectUrl = redirect || '/';
      await router.push(redirectUrl);
      loading.value = false;
    } else {
      loading.value = false;
    }

  } catch (error) {
    handleError(error);
  }
}
</script>

<style scoped>
.sso-login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f7fa;
}

.loading-text {
  font-size: 16px;
  color: #606266;
  display: flex;
  align-items: center;
  gap: 10px;
}

.is-loading {
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
