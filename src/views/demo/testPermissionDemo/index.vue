<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="演示名称" prop="demoName">
              <el-input v-model="queryParams.demoName" placeholder="请输入演示名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="演示编码" prop="demoCode">
              <el-input v-model="queryParams.demoCode" placeholder="请输入演示编码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="演示值" prop="demoValue">
              <el-input v-model="queryParams.demoValue" placeholder="请输入演示值" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="排序号" prop="sortOrder">
              <el-input v-model="queryParams.sortOrder" placeholder="请输入排序号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['demo:testPermissionDemo:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['demo:testPermissionDemo:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['demo:testPermissionDemo:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['demo:testPermissionDemo:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="testPermissionDemoList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="主键ID" align="center" prop="id" v-if="true" />
        <el-table-column label="演示名称" align="center" prop="demoName" />
        <el-table-column label="演示编码" align="center" prop="demoCode" />
        <el-table-column label="演示类型" align="center" prop="demoType" />
        <el-table-column label="演示状态" align="center" prop="demoStatus" />
        <el-table-column label="演示值" align="center" prop="demoValue" />
        <el-table-column label="排序号" align="center" prop="sortOrder" />
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['demo:testPermissionDemo:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['demo:testPermissionDemo:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改数据权限演示对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="testPermissionDemoFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="演示名称" prop="demoName">
          <el-input v-model="form.demoName" placeholder="请输入演示名称" />
        </el-form-item>
        <el-form-item label="演示编码" prop="demoCode">
          <el-input v-model="form.demoCode" placeholder="请输入演示编码" />
        </el-form-item>
        <el-form-item label="演示值" prop="demoValue">
          <el-input v-model="form.demoValue" placeholder="请输入演示值" />
        </el-form-item>
        <el-form-item label="排序号" prop="sortOrder">
          <el-input v-model="form.sortOrder" placeholder="请输入排序号" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="TestPermissionDemo" lang="ts">
import { listTestPermissionDemo, getTestPermissionDemo, delTestPermissionDemo, addTestPermissionDemo, updateTestPermissionDemo } from '@/api/demo/testPermissionDemo';
import { TestPermissionDemoVO, TestPermissionDemoQuery, TestPermissionDemoForm } from '@/api/demo/testPermissionDemo/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const testPermissionDemoList = ref<TestPermissionDemoVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(false);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const testPermissionDemoFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: TestPermissionDemoForm = {
  id: undefined,
  demoName: undefined,
  demoCode: undefined,
  demoType: undefined,
  demoStatus: undefined,
  demoValue: undefined,
  sortOrder: undefined,
  remark: undefined,
}
const data = reactive<PageData<TestPermissionDemoForm, TestPermissionDemoQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    demoName: undefined,
    demoCode: undefined,
    demoType: undefined,
    demoStatus: undefined,
    demoValue: undefined,
    sortOrder: undefined,
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "主键ID不能为空", trigger: "blur" }
    ],
    sortOrder: [
      { required: true, message: "排序号不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询数据权限演示列表 */
const getList = async () => {
  loading.value = true;
  const res = await listTestPermissionDemo(queryParams.value);
  testPermissionDemoList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  testPermissionDemoFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: TestPermissionDemoVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加数据权限演示";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: TestPermissionDemoVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getTestPermissionDemo(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改数据权限演示";
}

/** 提交按钮 */
const submitForm = () => {
  testPermissionDemoFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateTestPermissionDemo(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addTestPermissionDemo(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: TestPermissionDemoVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除数据权限演示编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delTestPermissionDemo(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('demo/testPermissionDemo/export', {
    ...queryParams.value
  }, `testPermissionDemo_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
