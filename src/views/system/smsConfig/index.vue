<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="租户ID" prop="tenantId">
              <el-input v-model="queryParams.tenantId" placeholder="请输入租户ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="场景类型" prop="sceneType">
              <el-select v-model="queryParams.sceneType" placeholder="请选择场景类型" clearable>
                <el-option v-for="dict in sys_sms_config_scene_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="短信模板ID" prop="templateId">
              <el-input v-model="queryParams.templateId" placeholder="请输入短信模板ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="一级通知人手机号" prop="level1Phones">
              <el-input v-model="queryParams.level1Phones" placeholder="请输入一级通知人手机号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="二级通知人手机号" prop="level2Phones">
              <el-input v-model="queryParams.level2Phones" placeholder="请输入二级通知人手机号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:smsConfig:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              plain
              icon="Edit"
              :disabled="single"
              @click="handleUpdate()"
              v-hasPermi="['system:smsConfig:edit']"
            >修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="Delete"
              :disabled="multiple"
              @click="handleDelete()"
              v-hasPermi="['system:smsConfig:remove']"
            >删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['system:smsConfig:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="smsConfigList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="主键ID" align="center" prop="id" v-if="true" />
        <el-table-column label="租户" align="center" prop="tenantId">
          <template #default="scope">
            {{ getTenantName(scope.row.tenantId) }}
          </template>
        </el-table-column>
        <el-table-column label="场景类型" align="center" prop="sceneType">
          <template #default="scope">
            <dict-tag :options="sys_sms_config_scene_type" :value="scope.row.sceneType" />
          </template>
        </el-table-column>
        <el-table-column label="短信模板ID" align="center" prop="templateId" />
        <el-table-column label="一级通知人" align="center" prop="level1Phones" width="200">
          <template #default="scope">
            <div v-if="scope.row.level1Phones" class="notification-display">
              <div 
                v-for="(person, index) in parseNotificationPersons(scope.row.level1Phones)" 
                :key="index" 
                class="person-row"
              >
                <el-tag size="small" class="person-tag-compact">
                  <span class="person-name">{{ person.name }}</span>
                  <span class="person-phone">{{ person.phone }}</span>
                </el-tag>
              </div>
            </div>
            <span v-else class="empty-text">未设置</span>
          </template>
        </el-table-column>
        <el-table-column label="二级通知人" align="center" prop="level2Phones" width="200">
          <template #default="scope">
            <div v-if="scope.row.level2Phones" class="notification-display">
              <div 
                v-for="(person, index) in parseNotificationPersons(scope.row.level2Phones)" 
                :key="index" 
                class="person-row"
              >
                <el-tag size="small" type="warning" class="person-tag-compact">
                  <span class="person-name">{{ person.name }}</span>
                  <span class="person-phone">{{ person.phone }}</span>
                </el-tag>
              </div>
            </div>
            <span v-else class="empty-text">未设置</span>
          </template>
        </el-table-column>
        <el-table-column label="短信服务商标识" align="center" prop="smsProvider" />
        <el-table-column label="状态" align="center" prop="status">
          <template #default="scope">
            <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'">
              {{ scope.row.status === '0' ? '正常' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" width="200">
          <template #default="scope">
            <div v-if="scope.row.remark" class="remark-content">
              {{ scope.row.remark }}
            </div>
            <span v-else class="empty-text">-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:smsConfig:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:smsConfig:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改短信配置对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="600px" append-to-body>
      <el-form ref="smsConfigFormRef" :model="form" :rules="rules" label-width="140px">
        <el-form-item label="租户" prop="tenantId" v-if="isSuperAdmin">
          <el-select v-model="form.tenantId" placeholder="请选择租户" clearable style="width: 100%">
            <el-option v-for="tenant in tenantList" :key="tenant.tenantId" :label="tenant.companyName" :value="tenant.tenantId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="租户" prop="tenantId" v-else>
          <el-input v-model="currentTenantName" :disabled="true" placeholder="当前租户" />
        </el-form-item>
        <el-form-item label="场景类型" prop="sceneType">
          <el-select v-model="form.sceneType" placeholder="请选择场景类型">
            <el-option v-for="dict in sys_sms_config_scene_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="短信模板ID" prop="templateId">
          <el-input v-model="form.templateId" placeholder="请输入短信模板ID" />
        </el-form-item>
        <el-form-item label="一级通知人" prop="level1Phones">
          <div class="notification-manager">
            <div v-for="(person, index) in level1PersonList" :key="index" class="person-input-row">
              <el-input 
                v-model="person.name" 
                placeholder="姓名" 
                style="width: 120px;" 
                @input="updateLevel1Phones"
              />
              <el-input 
                v-model="person.phone" 
                placeholder="手机号" 
                style="width: 140px; margin-left: 8px;" 
                maxlength="11"
                @input="updateLevel1Phones"
              />
              <el-button 
                @click="removeLevel1Person(index)" 
                type="danger" 
                icon="Delete" 
                size="small" 
                style="margin-left: 8px;"
                :disabled="level1PersonList.length === 1"
              />
            </div>
            <el-button 
              @click="addLevel1Person" 
              type="primary" 
              icon="Plus" 
              size="small" 
              style="margin-top: 8px;"
            >
              添加通知人
            </el-button>
          </div>
        </el-form-item>
        <el-form-item label="二级通知人" prop="level2Phones">
          <div class="notification-manager">
            <div v-for="(person, index) in level2PersonList" :key="index" class="person-input-row">
              <el-input 
                v-model="person.name" 
                placeholder="姓名" 
                style="width: 120px;" 
                @input="updateLevel2Phones"
              />
              <el-input 
                v-model="person.phone" 
                placeholder="手机号" 
                style="width: 140px; margin-left: 8px;" 
                maxlength="11"
                @input="updateLevel2Phones"
              />
              <el-button 
                @click="removeLevel2Person(index)" 
                type="danger" 
                icon="Delete" 
                size="small" 
                style="margin-left: 8px;"
                :disabled="level2PersonList.length === 1"
              />
            </div>
            <el-button 
              @click="addLevel2Person" 
              type="primary" 
              icon="Plus" 
              size="small" 
              style="margin-top: 8px;"
            >
              添加通知人
            </el-button>
          </div>
        </el-form-item>
        <el-form-item label="短信服务商标识" prop="smsProvider">
          <el-input v-model="form.smsProvider" placeholder="请输入短信服务商标识" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio value="0">正常</el-radio>
            <el-radio value="1">停用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="SmsConfig" lang="ts">
import { listSmsConfig, getSmsConfig, delSmsConfig, addSmsConfig, updateSmsConfig } from '@/api/system/smsConfig';
import { SmsConfigVO, SmsConfigQuery, SmsConfigForm } from '@/api/system/smsConfig/types';
import { getTenantList } from '@/api/login';
import { TenantVO } from '@/api/types';
import { useUserStore } from '@/store/modules/user';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_sms_config_scene_type } = toRefs<any>(proxy?.useDict('sys_sms_config_scene_type'));

const userStore = useUserStore();
const smsConfigList = ref<SmsConfigVO[]>([]);
const tenantList = ref<TenantVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(false);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const smsConfigFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

// 计算属性
const isSuperAdmin = computed(() => userStore.userId === 1);

// 当前租户名称 - 使用响应式变量避免循环依赖
const currentTenantName = ref('当前租户');

// 通知人列表管理
const level1PersonList = ref([{ name: '', phone: '' }]);
const level2PersonList = ref([{ name: '', phone: '' }]);

// 更新当前租户名称的函数
const updateCurrentTenantName = () => {
  const currentTenant = tenantList.value.find((tenant) => tenant.tenantId === userStore.tenantId);
  currentTenantName.value = currentTenant ? currentTenant.companyName : '当前租户';
};

const initFormData: SmsConfigForm = {
  id: undefined,
  tenantId: undefined,
  sceneType: undefined,
  templateId: undefined,
  level1Phones: undefined,
  level2Phones: undefined,
  smsProvider: undefined,
  status: '0',
  remark: undefined
};
const data = reactive<PageData<SmsConfigForm, SmsConfigQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    tenantId: undefined,
    sceneType: undefined,
    templateId: undefined,
    level1Phones: undefined,
    level2Phones: undefined,
    status: undefined,
    params: {}
  },
  rules: {
    id: [{ required: true, message: '主键ID不能为空', trigger: 'blur' }],
    tenantId: [{ required: true, message: '租户不能为空', trigger: 'change' }],
    sceneType: [{ required: true, message: '场景类型不能为空', trigger: 'change' }],
    templateId: [{ required: true, message: '短信模板ID不能为空', trigger: 'blur' }],
    smsProvider: [{ required: true, message: '短信服务商标识不能为空', trigger: 'blur' }],
    status: [{ required: true, message: '状态不能为空', trigger: 'change' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

// 获取租户名称
const getTenantName = (tenantId: string) => {
  const tenant = tenantList.value.find((t) => t.tenantId === tenantId);
  return tenant ? tenant.companyName : tenantId;
};

// 一级通知人管理方法
const addLevel1Person = () => {
  level1PersonList.value.push({ name: '', phone: '' });
};

const removeLevel1Person = (index: number) => {
  if (level1PersonList.value.length > 1) {
    level1PersonList.value.splice(index, 1);
    updateLevel1Phones();
  }
};

const updateLevel1Phones = () => {
  const validPersons = level1PersonList.value.filter(person => 
    person.name.trim() && person.phone.trim()
  );
  form.value.level1Phones = validPersons
    .map(person => `${person.name.trim()}(${person.phone.trim()})`)
    .join(',');
};

// 二级通知人管理方法
const addLevel2Person = () => {
  level2PersonList.value.push({ name: '', phone: '' });
};

const removeLevel2Person = (index: number) => {
  if (level2PersonList.value.length > 1) {
    level2PersonList.value.splice(index, 1);
    updateLevel2Phones();
  }
};

const updateLevel2Phones = () => {
  const validPersons = level2PersonList.value.filter(person => 
    person.name.trim() && person.phone.trim()
  );
  form.value.level2Phones = validPersons
    .map(person => `${person.name.trim()}(${person.phone.trim()})`)
    .join(',');
};

// 解析已有数据到列表
const parsePhonesToList = (phoneString: string) => {
  if (!phoneString) return [{ name: '', phone: '' }];
  
  const persons = phoneString.split(',').map(item => item.trim()).filter(item => item);
  const result = persons.map(person => {
    const match = person.match(/^(.+?)\((\d+)\)$/);
    if (match) {
      return { name: match[1].trim(), phone: match[2].trim() };
    }
    // 兼容旧格式：纯手机号
    if (person.match(/^\d{11}$/)) {
      return { name: '', phone: person };
    }
    return { name: person, phone: '' };
  });
  
  return result.length > 0 ? result : [{ name: '', phone: '' }];
};

// 表格显示用的解析函数
const parseNotificationPersons = (phoneString: string) => {
  if (!phoneString || phoneString.trim() === '') {
    return [];
  }
  
  const persons = phoneString.split(',').map(item => item.trim()).filter(item => item);
  
  return persons.map(person => {
    // 匹配格式：姓名(手机号)
    const match = person.match(/^(.+?)\((\d+)\)$/);
    if (match) {
      return {
        name: match[1].trim(),
        phone: match[2].trim()
      };
    } else {
      // 如果不符合格式，尝试判断是否是纯手机号
      const phoneMatch = person.match(/^1[3-9]\d{9}$/);
      if (phoneMatch) {
        return {
          name: '未知',
          phone: person
        };
      } else {
        // 其他情况，当作姓名处理
        return {
          name: person,
          phone: '未知'
        };
      }
    }
  });
};

// 初始化租户列表
const initTenantList = async () => {
  try {
    const { data } = await getTenantList(true);
    if (data.voList) {
      tenantList.value = data.voList;
      // 在租户列表加载完成后更新当前租户名称
      updateCurrentTenantName();
    }
  } catch (error) {
    console.error('获取租户列表失败:', error);
  }
};

/** 查询短信配置列表 */
const getList = async () => {
  loading.value = true;
  const res = await listSmsConfig(queryParams.value);
  smsConfigList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  // 如果不是超级管理员，自动设置当前租户
  if (!isSuperAdmin.value) {
    form.value.tenantId = userStore.tenantId;
  }
  // 重置通知人列表
  level1PersonList.value = [{ name: '', phone: '' }];
  level2PersonList.value = [{ name: '', phone: '' }];
  smsConfigFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: SmsConfigVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加短信配置';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: SmsConfigVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getSmsConfig(_id);
  Object.assign(form.value, res.data);
  
  // 解析已有数据到通知人列表
  level1PersonList.value = parsePhonesToList(res.data.level1Phones);
  level2PersonList.value = parsePhonesToList(res.data.level2Phones);
  
  dialog.visible = true;
  dialog.title = '修改短信配置';
};

/** 提交按钮 */
const submitForm = () => {
  smsConfigFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateSmsConfig(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addSmsConfig(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: SmsConfigVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除短信配置编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delSmsConfig(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'system/smsConfig/export',
    {
      ...queryParams.value
    },
    `smsConfig_${new Date().getTime()}.xlsx`
  );
};

onMounted(async () => {
  await initTenantList();
  getList();
});
</script>

<style lang="scss" scoped>
.notification-manager {
  .person-input-row {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    &:last-of-type {
      margin-bottom: 8px;
    }
  }
  
  .el-button {
    &.is-disabled {
      opacity: 0.6;
    }
  }
}

.notification-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 3px;
  
  .person-row {
    width: 100%;
    display: flex;
    justify-content: center;
    
    .person-tag-compact {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 2px 6px;
      min-width: 120px;
      max-width: 160px;
      
      .person-name {
        font-weight: 500;
        font-size: 13px;
        flex-shrink: 0;
      }
      
      .person-phone {
        font-size: 12px;
        opacity: 0.85;
        margin-left: 4px;
        font-weight: normal;
        text-align: right;
        flex-shrink: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}

.empty-text {
  color: #c0c4cc;
  font-style: italic;
  font-size: 12px;
}

.remark-content {
  white-space: pre-wrap;
  word-break: break-word;
  text-align: left;
  max-height: 80px;
  overflow-y: auto;
  padding: 4px;
  line-height: 1.4;
  font-size: 12px;
}
</style>
