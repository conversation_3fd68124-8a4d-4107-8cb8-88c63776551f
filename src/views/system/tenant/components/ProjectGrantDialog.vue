<template>
  <el-dialog
    title="项目授权"
    v-model="dialogVisible"
    width="600px"
    :close-on-click-modal="false"
    append-to-body
    @close="handleClose"
  >
    <div class="grant-dialog-content">
      <!-- 租户信息 -->
      <div class="tenant-info mb-4">
        <el-alert
          :title="`为租户 '${tenantName}' 批量授权项目管理权限`"
          type="info"
          :closable="false"
          show-icon
        />
      </div>

      <el-form ref="formRef" :model="form" label-width="120px">
        <el-form-item label="授权项目">
          <div class="project-selection-area">
            <!-- 搜索框 -->
            <div class="search-section">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索项目名称或描述..."
                prefix-icon="Search"
                clearable
                @input="handleSearch"
                class="search-input"
              />
            </div>

            <div class="selection-header">
              <el-checkbox 
                v-model="selectAll" 
                :indeterminate="isIndeterminate"
                @change="handleSelectAll"
              >
                全选/取消全选
              </el-checkbox>
              <span class="selected-count">已选择: {{ form.projectIds.length }} / {{ filteredProjects.length }}</span>
            </div>
            
            <div class="project-list">
              <el-checkbox-group v-model="form.projectIds">
                <div v-for="project in filteredProjects" :key="project.id" class="project-item">
                  <el-checkbox :label="String(project.id)">
                    <div class="project-info">
                        <span class="project-name">{{ project.projectName }}{{ project.projectDesc ? `(${project.projectDesc})` : '' }}</span>
                      <el-tag 
                        v-if="getExistingPermission(project.id)" 
                        size="small" 
                        class="ml-2"
                        type="success"
                      >
                        当前已授权
                      </el-tag>
                    </div>
                  </el-checkbox>
                </div>
              </el-checkbox-group>
              
              <!-- 无搜索结果提示 -->
              <div v-if="filteredProjects.length === 0 && searchKeyword" class="no-results">
                <el-empty description="未找到匹配的项目" :image-size="60" />
              </div>
            </div>
          </div>
        </el-form-item>

        <!-- 项目管理者和备注字段已移除 -->
      </el-form>

      <!-- 操作说明 -->
      <div class="operation-tips">
        <el-alert
          title="操作说明"
          type="warning"
          :closable="false"
          show-icon
        >
          <template #default>
            <ul>
              <li>批量授权采用<strong>全量覆盖</strong>方式，未选中的项目权限将被移除</li>
              <li>当前租户将可以管理被选中的项目</li>
              <li>可使用搜索框快速定位目标项目</li>
              <li>请谨慎操作，确认后将立即生效</li>
            </ul>
          </template>
        </el-alert>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submitForm" :loading="loading">
          确认授权 ({{ form.projectIds.length }})
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed, getCurrentInstance } from 'vue';
import { listProject } from '@/api/biz/project';
import request from '@/utils/request';
import type { ProjectVO } from '@/api/biz/project/types';
import type { ElFormInstance } from 'element-plus';

interface ProjectPermissionVO {
  id: string;
  projectId: string | number;
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  tenantId: {
    type: [String, Number],
    required: true
  },
  tenantName: {
    type: String,
    required: true
  }
});

const emit = defineEmits(['success', 'close']);

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => {
    if (!val) emit('close');
  }
});

const loading = ref(false);
const availableProjects = ref<ProjectVO[]>([]);
const filteredProjects = ref<ProjectVO[]>([]);
const existingPermissions = ref<ProjectPermissionVO[]>([]);
const selectAll = ref(false);
const isIndeterminate = ref(false);
const searchKeyword = ref('');

const formRef = ref<ElFormInstance>();
const form = reactive({
  tenantId: '',
  projectIds: [] as string[]
});

/** 获取项目的现有权限信息 */
const getExistingPermission = (projectId: string | number) => {
  return existingPermissions.value.find((p) => p.projectId == projectId);
};

/** 获取可选择的项目列表 */
const getAvailableProjects = async () => {
  try {
    const res = await listProject({
      pageNum: 1,
      pageSize: 1000
    });
    availableProjects.value = res.rows || [];
    // 初始化过滤结果
    filteredProjects.value = availableProjects.value;
  } catch (error) {
    console.error('获取项目列表失败:', error);
    proxy?.$modal.msgError('获取项目列表失败');
  }
};

/** 处理搜索 */
const handleSearch = () => {
  if (!searchKeyword.value.trim()) {
    filteredProjects.value = availableProjects.value;
  } else {
    const keyword = searchKeyword.value.toLowerCase();
    filteredProjects.value = availableProjects.value.filter(project => 
        project.projectName.toLowerCase().includes(keyword) ||
        (project.projectDesc && project.projectDesc.toLowerCase().includes(keyword))
      );
  }
  // 搜索后重新计算全选状态
  updateSelectAllState();
};

/** 获取现有权限关系 */
const getExistingPermissions = async () => {
  try {
    const res = await request({
      url: '/system/tenantProject/list',
      method: 'get',
      params: {
        tenantId: props.tenantId,
        pageNum: 1,
        pageSize: 1000
      }
    });
    existingPermissions.value = res.rows || [];
    
    // 预选中所有已有权限的项目
    form.projectIds = existingPermissions.value
      .map(p => String(p.projectId));
    
    updateSelectAllState();
  } catch (error) {
    console.error('获取现有权限关系失败:', error);
  }
};

/** 全选/取消全选 */
const handleSelectAll = (checked: boolean) => {
  if (checked) {
    // 基于当前过滤结果进行全选
    const currentFilteredIds = filteredProjects.value.map(project => String(project.id));
    
    // 合并现有选中项和当前过滤的项
    const existingSelected = form.projectIds.filter(id => 
      !filteredProjects.value.some(project => String(project.id) === id)
    );
    form.projectIds = [...existingSelected, ...currentFilteredIds];
  } else {
    // 取消全选时，只移除当前过滤结果中的选中项
    const currentFilteredIds = filteredProjects.value.map(project => String(project.id));
    form.projectIds = form.projectIds.filter(id => 
      !currentFilteredIds.includes(id)
    );
  }
  updateSelectAllState();
};

/** 更新全选状态 */
const updateSelectAllState = () => {
  const totalCount = filteredProjects.value.length;
  
  const selectedInFilteredCount = filteredProjects.value.filter(
    project => form.projectIds.includes(String(project.id))
  ).length;
  
  selectAll.value = selectedInFilteredCount === totalCount && totalCount > 0;
  isIndeterminate.value = selectedInFilteredCount > 0 && selectedInFilteredCount < totalCount;
};

/** 监听选中状态变化 */
watch(() => form.projectIds, updateSelectAllState, { deep: true });

/** 提交表单 */
const submitForm = async () => {
  loading.value = true;
  try {
    emit('success', {
      tenantId: props.tenantId,
      projectIds: form.projectIds
    });
  } finally {
    loading.value = false;
  }
};

/** 关闭弹窗 */
const handleClose = () => {
  formRef.value?.resetFields();
  form.projectIds = [];
  existingPermissions.value = [];
  selectAll.value = false;
  isIndeterminate.value = false;
  searchKeyword.value = '';
  filteredProjects.value = [];
  emit('close');
};

/** 初始化数据 */
const initData = async () => {
  form.tenantId = String(props.tenantId);
  await getAvailableProjects();
  await getExistingPermissions();
};

watch(
  () => props.visible,
  (val) => {
    if (val) {
      initData();
    }
  }
);
</script>

<style scoped>
.grant-dialog-content {
  max-height: 60vh;
  overflow-y: auto;
}

.tenant-info {
  margin-bottom: 16px;
}

.project-selection-area {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 16px;
  background-color: #fafafa;
}

.search-section {
  margin-bottom: 12px;
}

.search-input {
  width: 100%;
}

.selection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.selected-count {
  font-size: 12px;
  color: #909399;
}

.project-list {
  max-height: 200px;
  overflow-y: auto;
}

.project-item {
  padding: 6px 0;
  border-bottom: 1px solid #f0f0f0;
}

.project-item:last-child {
  border-bottom: none;
}

.project-info {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.project-name {
  font-weight: 500;
}

.project-code {
  font-size: 12px;
  color: #909399;
}

.operation-tips {
  margin-top: 16px;
}

.operation-tips ul {
  margin: 0;
  padding-left: 20px;
}

.operation-tips li {
  margin: 4px 0;
  font-size: 13px;
}

.no-results {
  text-align: center;
  padding: 20px;
  color: #909399;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>