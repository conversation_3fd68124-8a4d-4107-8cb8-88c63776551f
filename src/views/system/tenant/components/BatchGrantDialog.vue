<template>
  <el-dialog
    title="批量授权租户管理权限"
    v-model="dialogVisible"
    width="600px"
    :close-on-click-modal="false"
    append-to-body
    @close="handleClose"
  >
    <div class="grant-dialog-content">
      <!-- 上级租户信息 -->
      <div class="parent-tenant-info mb-4">
        <el-alert
          :title="`为租户 '${parentTenantName}' 批量授权下级租户管理权限`"
          type="info"
          :closable="false"
          show-icon
        />
      </div>

      <el-form ref="formRef" :model="form" label-width="120px">
        <el-form-item label="授权租户">
          <div class="tenant-selection-area">
            <!-- 搜索框 -->
            <div class="search-section">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索租户名称或编号..."
                prefix-icon="Search"
                clearable
                @input="handleSearch"
                class="search-input"
              />
            </div>

            <div class="selection-header">
              <el-checkbox 
                v-model="selectAll" 
                :indeterminate="isIndeterminate"
                @change="handleSelectAll"
              >
                全选/取消全选
              </el-checkbox>
              <span class="selected-count">已选择: {{ form.childTenantIds.length }} / {{ filteredTenants.length }}</span>
            </div>
            
            <div class="tenant-list">
              <el-checkbox-group v-model="form.childTenantIds">
                <div v-for="tenant in filteredTenants" :key="tenant.tenantId" class="tenant-item">
                  <el-checkbox 
                    :label="tenant.tenantId"
                    :disabled="tenant.tenantId === parentTenantId"
                  >
                    <div class="tenant-info">
                      <span class="tenant-name">{{ tenant.companyName }}</span>
                      <span class="tenant-id">(ID: {{ tenant.tenantId }})</span>
                      <el-tag 
                        v-if="getExistingPermission(tenant.tenantId)" 
                        size="small" 
                        class="ml-2"
                        :type="getExistingPermission(tenant.tenantId)?.status === '1' ? 'success' : 'warning'"
                      >
                        当前{{ getExistingPermission(tenant.tenantId)?.status === '1' ? '已授权' : '已禁用' }}
                      </el-tag>
                      <el-tag 
                        v-if="tenant.tenantId === parentTenantId" 
                        size="small" 
                        class="ml-2"
                        type="info"
                      >
                        当前租户
                      </el-tag>
                    </div>
                  </el-checkbox>
                </div>
              </el-checkbox-group>
              
              <!-- 无搜索结果提示 -->
              <div v-if="filteredTenants.length === 0 && searchKeyword" class="no-results">
                <el-empty description="未找到匹配的租户" :image-size="60" />
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>

      <!-- 操作说明 -->
      <div class="operation-tips">
        <el-alert
          title="操作说明"
          type="warning"
          :closable="false"
          show-icon
        >
          <template #default>
            <ul>
              <li>批量授权采用<strong>全量覆盖</strong>方式，未选中的租户权限将被移除</li>
              <li>当前租户将可以管理被选中的租户</li>
              <li>可使用搜索框快速定位目标租户</li>
              <li>请谨慎操作，确认后将立即生效</li>
            </ul>
          </template>
        </el-alert>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submitForm" :loading="loading">
          确认授权 ({{ form.childTenantIds.length }})
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { listTenant, listTenantPermissionByParent } from '@/api/system/tenant';
import type { TenantVO } from '@/api/system/tenant/types';

interface TenantPermissionVO {
  childTenantId: string | number;
  status: string;
  permissionType: string;
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  parentTenantId: {
    type: [String, Number],
    required: true
  },
  parentTenantName: {
    type: String,
    required: true
  }
});

const emit = defineEmits(['success', 'close']);

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => {
    if (!val) emit('close');
  }
});

const loading = ref(false);
const availableTenants = ref<TenantVO[]>([]);
const filteredTenants = ref<TenantVO[]>([]);
const existingPermissions = ref<TenantPermissionVO[]>([]);
const selectAll = ref(false);
const isIndeterminate = ref(false);
const searchKeyword = ref('');

const formRef = ref<ElFormInstance>();
const form = reactive({
  parentTenantId: '',
  childTenantIds: [] as string[],
  permissionType: '2', // 默认管理权限
  status: '1', // 默认启用状态
  remark: '' // 默认空备注
});

/** 获取租户的现有权限信息 */
const getExistingPermission = (tenantId: string | number) => {
  return existingPermissions.value.find((p) => p.childTenantId == tenantId);
};

/** 获取可选择的租户列表 */
const getAvailableTenants = async () => {
  try {
    const res = await listTenant({
      tenantId: '',
      contactUserName: '',
      contactPhone: '',
      companyName: '',
      pageNum: 1,
      pageSize: 1000
    });
    // 过滤掉当前上级租户自己
    availableTenants.value = (res.rows || []).filter(
      tenant => tenant.tenantId != props.parentTenantId
    );
    // 初始化过滤结果
    filteredTenants.value = availableTenants.value;
  } catch (error) {
    console.error('获取租户列表失败:', error);
    proxy?.$modal.msgError('获取租户列表失败');
  }
};

/** 处理搜索 */
const handleSearch = () => {
  if (!searchKeyword.value.trim()) {
    filteredTenants.value = availableTenants.value;
  } else {
    const keyword = searchKeyword.value.toLowerCase();
    filteredTenants.value = availableTenants.value.filter(tenant => 
      tenant.companyName.toLowerCase().includes(keyword) ||
      String(tenant.tenantId).toLowerCase().includes(keyword)
    );
  }
  // 搜索后重新计算全选状态
  updateSelectAllState();
};

/** 获取现有权限关系 */
const getExistingPermissions = async () => {
  try {
    const res = await listTenantPermissionByParent(props.parentTenantId);
    existingPermissions.value = res.data || [];
    
    // 预选中已有权限的租户（仅启用状态）
    form.childTenantIds = existingPermissions.value
      .filter(p => p.status === '1')
      .map(p => String(p.childTenantId));
    
    updateSelectAllState();
  } catch (error) {
    console.error('获取现有权限关系失败:', error);
  }
};

/** 全选/取消全选 */
const handleSelectAll = (checked: boolean) => {
  if (checked) {
    // 基于当前过滤结果进行全选
    const currentFilteredIds = filteredTenants.value
      .filter(tenant => tenant.tenantId != props.parentTenantId)
      .map(tenant => String(tenant.tenantId));
    
    // 合并现有选中项和当前过滤的项
    const existingSelected = form.childTenantIds.filter(id => 
      !filteredTenants.value.some(tenant => String(tenant.tenantId) === id)
    );
    form.childTenantIds = [...existingSelected, ...currentFilteredIds];
  } else {
    // 取消全选时，只移除当前过滤结果中的选中项
    const currentFilteredIds = filteredTenants.value.map(tenant => String(tenant.tenantId));
    form.childTenantIds = form.childTenantIds.filter(id => 
      !currentFilteredIds.includes(id)
    );
  }
  updateSelectAllState();
};

/** 更新全选状态 */
const updateSelectAllState = () => {
  const totalCount = filteredTenants.value.filter(
    tenant => tenant.tenantId != props.parentTenantId
  ).length;
  
  const selectedInFilteredCount = filteredTenants.value.filter(
    tenant => tenant.tenantId != props.parentTenantId && 
    form.childTenantIds.includes(String(tenant.tenantId))
  ).length;
  
  selectAll.value = selectedInFilteredCount === totalCount && totalCount > 0;
  isIndeterminate.value = selectedInFilteredCount > 0 && selectedInFilteredCount < totalCount;
};

/** 监听选中状态变化 */
watch(() => form.childTenantIds, updateSelectAllState, { deep: true });

/** 提交表单 */
const submitForm = async () => {
  loading.value = true;
  try {
    emit('success', {
      parentTenantId: props.parentTenantId,
      childTenantIds: form.childTenantIds,
      permissionType: form.permissionType,
      status: form.status,
      remark: form.remark
    });
  } finally {
    loading.value = false;
  }
};

/** 关闭弹窗 */
const handleClose = () => {
  formRef.value?.resetFields();
  form.childTenantIds = [];
  existingPermissions.value = [];
  selectAll.value = false;
  isIndeterminate.value = false;
  searchKeyword.value = '';
  filteredTenants.value = [];
  emit('close');
};

/** 初始化数据 */
const initData = async () => {
  form.parentTenantId = String(props.parentTenantId);
  await getAvailableTenants();
  await getExistingPermissions();
};

watch(
  () => props.visible,
  (val) => {
    if (val) {
      initData();
    }
  }
);
</script>

<style scoped>
.grant-dialog-content {
  max-height: 60vh;
  overflow-y: auto;
}

.parent-tenant-info {
  margin-bottom: 16px;
}

.tenant-selection-area {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 16px;
  background-color: #fafafa;
}

.search-section {
  margin-bottom: 12px;
}

.search-input {
  width: 100%;
}

.selection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.selected-count {
  font-size: 12px;
  color: #909399;
}

.tenant-list {
  max-height: 200px;
  overflow-y: auto;
}

.tenant-item {
  padding: 6px 0;
  border-bottom: 1px solid #f0f0f0;
}

.tenant-item:last-child {
  border-bottom: none;
}

.tenant-info {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.tenant-name {
  font-weight: 500;
}

.tenant-id {
  font-size: 12px;
  color: #909399;
}

.operation-tips {
  margin-top: 16px;
}

.operation-tips ul {
  margin: 0;
  padding-left: 20px;
}

.operation-tips li {
  margin: 4px 0;
  font-size: 13px;
}

.no-results {
  text-align: center;
  padding: 20px;
  color: #909399;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style> 