/* 登录页面动画效果 */

/* 呼吸灯效果 */
@keyframes breathe {
  0% {
    opacity: 0.5;
    box-shadow: 0 0 5px rgba(74, 140, 255, 0.3);
  }
  50% {
    opacity: 0.8;
    box-shadow: 0 0 15px rgba(74, 140, 255, 0.7);
  }
  100% {
    opacity: 0.5;
    box-shadow: 0 0 5px rgba(74, 140, 255, 0.3);
  }
}

/* 边框闪烁效果 */
@keyframes borderPulse {
  0% {
    border-color: rgba(74, 140, 255, 0.3);
  }
  50% {
    border-color: rgba(74, 140, 255, 0.8);
  }
  100% {
    border-color: rgba(74, 140, 255, 0.3);
  }
}

/* 按钮悬停效果 */
@keyframes buttonHover {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* 科技线条动画 */
@keyframes techLineScan {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 100% 100%;
  }
}

/* 输入框聚焦动画 */
@keyframes inputFocus {
  0% {
    box-shadow: 0 0 0 1px rgba(74, 140, 255, 0.2);
  }
  50% {
    box-shadow: 0 0 0 1px rgba(74, 140, 255, 1), 0 0 10px rgba(74, 140, 255, 0.5);
  }
  100% {
    box-shadow: 0 0 0 1px rgba(74, 140, 255, 0.2);
  }
}

/* 登录按钮渐变动画 */
@keyframes gradientFlow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}