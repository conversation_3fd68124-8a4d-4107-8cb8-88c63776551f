// 全局SCSS变量
:root {
  --menuBg: #1F2830;
  --menuColor: #bfcbd9;
  --menuActiveText: #f4f4f5;
  --menuHover: #161d24;

  --subMenuBg: #1F2830;
  --subMenuActiveText: #f4f4f5;
  --subMenuHover: #161d24;
  --subMenuTitleHover: #161d24;

  --fixedHeaderBg: #ffffff;
  --tableHeaderBg: #f8f8f9;
  --tableHeaderTextColor: #515a6e;

  // ele
  --brder-color: #e8e8e8;

  // 添加 tag 相关变量
  --tags-view-active-bg: var(--el-color-primary);
  --tags-view-active-border-color: var(--el-color-primary);
}

html.dark {
  --menuBg: #1d1e1f;
  --menuColor: #bfcbd9;
  --menuActiveText: #f4f4f5;
  --menuHover: #171819;

  --subMenuBg: #1d1e1f;
  --subMenuActiveText: #1d1e1f;
  --subMenuHover: #171819;
  --subMenuTitleHover: #171819;

  --fixedHeaderBg: #171819;
  --tableHeaderBg: var(--el-bg-color);
  --tableHeaderTextColor: var(--el-text-color);

  // 覆盖ele 高亮当前行的标准暗色
  .el-tree-node__content {
    --el-color-primary-light-9: #262727;
  }

  .el-button--primary {
    --el-button-bg-color: var(--el-color-primary-dark-6);
    --el-button-border-color: var(--el-color-primary-light-2);
  }

  .el-switch {
    --el-switch-on-color: var(--el-color-primary-dark-6);
    --el-switch-border-color: var(--el-color-primary-light-2);
  }

  .el-tag--primary {
    --el-tag-bg-color: var(--el-color-primary-dark-6);
    --el-tag-border-color: var(--el-color-primary-light-2);
  }

  // 在深色模式下使用更深的颜色
  --tags-view-active-bg: var(--el-color-primary-dark-6);
  --tags-view-active-border-color: var(--el-color-primary-light-2);
  // vxe-table 主题
  --vxe-font-color: #98989e;
  --vxe-primary-color: #2c7ecf;
  --vxe-icon-background-color: #98989e;
  --vxe-table-font-color: #98989e;
  --vxe-table-resizable-color: #95969a;
  --vxe-table-header-background-color: #28282a;
  --vxe-table-body-background-color: #151518;
  --vxe-table-background-color: #4a5663;
  --vxe-table-border-width: 1px;
  --vxe-table-border-color: #37373a;
  --vxe-toolbar-background-color: #37373a;

  // ele
  --brder-color: #37373a;
}

// base color
$blue: #324157;
$light-blue: #3a71a8;
$red: #c03639;
$pink: #e65d6e;
$green: #30b08f;
$tiffany: #4ab7bd;
$yellow: #fec171;
$panGreen: #30b08f;

// 默认菜单主题风格
$base-menu-color: var(--menuColor);
$base-menu-hover: var(--menuHover);
$base-menu-color-active: var(--menuActiveText);
$base-menu-background: var(--menuBg);
$base-logo-title-color: #ffffff;

$base-menu-light-color: rgba(0, 0, 0, 0.7);
$base-menu-light-background: #ffffff;
$base-logo-light-title-color: #001529;

$base-sub-menu-background: var(--subMenuBg);
$base-sub-menu-hover: var(--subMenuHover);
$base-sub-menu-title-hover: var(--subMenuTitleHover);
// 表单头背景色和标题颜色
$fixed-header-bg: var(--fixedHeaderBg);
$table-header-bg: var(--tableHeaderBg);
$table-header-text-color: var(--tableHeaderTextColor);

$--color-primary: #409eff;
$--color-success: #67c23a;
$--color-warning: #e6a23c;
$--color-danger: #f56c6c;
$--color-info: #909399;

$base-header-height: 90px;
$base-sidebar-width: 200px;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuColor: $base-menu-color;
  menuLightColor: $base-menu-light-color;
  menuColorActive: $base-menu-color-active;
  menuBackground: $base-menu-background;
  menuLightBackground: $base-menu-light-background;
  subMenuBackground: $base-sub-menu-background;
  subMenuHover: $base-sub-menu-hover;
  sideBarWidth: $base-sidebar-width;
  headerHeight: $base-header-height;
  logoTitleColor: $base-logo-title-color;
  logoLightTitleColor: $base-logo-light-title-color;
  primaryColor: $--color-primary;
  successColor: $--color-success;
  dangerColor: $--color-danger;
  infoColor: $--color-info;
  warningColor: $--color-warning;
}