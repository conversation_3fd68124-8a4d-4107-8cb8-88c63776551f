/**
 * 登录页面粒子动画效果
 */

class ParticleAnimation {
  constructor(containerId, options = {}) {
    this.containerId = containerId;
    this.container = document.getElementById(containerId);
    if (!this.container) {
      console.error(`Container with id '${containerId}' not found`);
      return;
    }

    // 默认配置
    this.options = {
      particleCount: options.particleCount || 50,
      particleColor: options.particleColor || '#4a8cff',
      lineColor: options.lineColor || 'rgba(74, 140, 255, 0.3)',
      particleSize: options.particleSize || 2,
      lineWidth: options.lineWidth || 1,
      speed: options.speed || 1,
      proximity: options.proximity || 100,
      responsive: options.responsive !== undefined ? options.responsive : true
    };

    this.init();
  }

  init() {
    // 创建canvas元素
    this.canvas = document.createElement('canvas');
    this.ctx = this.canvas.getContext('2d');
    this.canvas.className = 'particles-canvas';
    this.canvas.style.position = 'absolute';
    this.canvas.style.top = '0';
    this.canvas.style.left = '0';
    this.canvas.style.zIndex = '0';
    this.canvas.style.pointerEvents = 'none';
    this.container.appendChild(this.canvas);

    // 设置canvas尺寸
    this.setCanvasSize();

    // 创建粒子
    this.createParticles();

    // 如果是响应式，添加窗口大小变化监听
    if (this.options.responsive) {
      window.addEventListener('resize', this.handleResize.bind(this));
    }

    // 开始动画
    this.animate();
  }

  setCanvasSize() {
    this.canvas.width = this.container.offsetWidth;
    this.canvas.height = this.container.offsetHeight;
  }

  createParticles() {
    this.particles = [];
    for (let i = 0; i < this.options.particleCount; i++) {
      this.particles.push({
        x: Math.random() * this.canvas.width,
        y: Math.random() * this.canvas.height,
        vx: (Math.random() - 0.5) * this.options.speed,
        vy: (Math.random() - 0.5) * this.options.speed,
        size: Math.random() * this.options.particleSize + 1,
        color: this.options.particleColor
      });
    }
  }

  animate() {
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

    // 更新粒子位置并绘制
    for (let i = 0; i < this.particles.length; i++) {
      const p = this.particles[i];

      // 更新位置
      p.x += p.vx;
      p.y += p.vy;

      // 边界检查
      if (p.x < 0 || p.x > this.canvas.width) p.vx = -p.vx;
      if (p.y < 0 || p.y > this.canvas.height) p.vy = -p.vy;

      // 绘制粒子
      this.ctx.beginPath();
      this.ctx.arc(p.x, p.y, p.size, 0, Math.PI * 2);
      this.ctx.fillStyle = p.color;
      this.ctx.fill();

      // 绘制连接线
      for (let j = i + 1; j < this.particles.length; j++) {
        const p2 = this.particles[j];
        const dx = p.x - p2.x;
        const dy = p.y - p2.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance < this.options.proximity) {
          this.ctx.beginPath();
          this.ctx.moveTo(p.x, p.y);
          this.ctx.lineTo(p2.x, p2.y);
          this.ctx.strokeStyle = this.options.lineColor;
          this.ctx.lineWidth = this.options.lineWidth;
          this.ctx.stroke();
        }
      }
    }

    requestAnimationFrame(this.animate.bind(this));
  }

  handleResize() {
    this.setCanvasSize();
    this.createParticles();
  }

  destroy() {
    if (this.options.responsive) {
      window.removeEventListener('resize', this.handleResize.bind(this));
    }
    this.container.removeChild(this.canvas);
  }
}

export default ParticleAnimation;