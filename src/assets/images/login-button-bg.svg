<svg width="400" height="50" xmlns="http://www.w3.org/2000/svg">
  <!-- 渐变背景 -->
  <defs>
    <linearGradient id="buttonGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#0d6efd" />
      <stop offset="100%" stop-color="#0a58ca" />
    </linearGradient>
  </defs>
  
  <!-- 按钮背景 -->
  <rect width="100%" height="100%" fill="url(#buttonGradient)" rx="4" ry="4"/>
  
  <!-- 发光效果 -->
  <rect x="0" y="0" width="100%" height="50%" fill="#ffffff" opacity="0.1" rx="4" ry="4"/>
  
  <!-- 装饰线条 -->
  <line x1="10" y1="10" x2="30" y2="10" stroke="#ffffff" stroke-width="1" opacity="0.3"/>
  <line x1="370" y1="40" x2="390" y2="40" stroke="#ffffff" stroke-width="1" opacity="0.3"/>
</svg>