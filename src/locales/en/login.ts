export default {
  'login.title': 'Login',
  'login.username': '<PERSON>rna<PERSON>',
  'login.password': 'Password',
  'login.code': 'Verification Code',
  'login.rememberPassword': 'Remember Password',
  'login.login': 'Login',
  'login.logging': 'Logging in...',
  'login.selectPlaceholder': 'Please select a tenant',
  'login.switchRegisterPage': 'Register',
  'login.rule.tenantId.required': 'Tenant cannot be empty',
  'login.rule.username.required': 'Username cannot be empty',
  'login.rule.password.required': 'Password cannot be empty',
  'login.rule.code.required': 'Verification code cannot be empty',
  'login.social.wechat': 'WeChat Login',
  'login.social.maxkey': 'MaxKey Login',
  'login.social.topiam': 'TopIAM Login',
  'login.social.gitee': 'Gitee Login',
  'login.social.github': 'Github Login',
  // SSO related
  'login.ssoNotLoggedIn': 'Not logged in, please enter your username and password',
  'login.ssoCheckFailed': 'Failed to check SSO status',
  'login.ssoLoginFailed': 'SSO login failed',
  'login.loginSuccess': 'Login successful'
};