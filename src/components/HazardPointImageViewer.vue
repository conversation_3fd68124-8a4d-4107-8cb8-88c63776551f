<template>
  <el-dialog title="查看图片" v-model="visible" width="95%" top="2vh" append-to-body destroy-on-close @close="handleClose">
    <!-- 顶部操作栏 -->
    <div class="mb-4">
      <el-button type="primary" :disabled="!leftSelectedImage || !rightSelectedImage" @click="handleImageCompare"> 图形比对 </el-button>
    </div>

    <!-- 双列布局 -->
    <div class="flex gap-4 h-[70vh]">
      <!-- 左侧：当前巡检记录图片 -->
      <div class="flex-1 border border-gray-200 rounded">
        <div class="bg-blue-50 p-3 border-b">
          <!-- 搜索区域 -->
          <div class="flex gap-2 items-center">
            <el-date-picker
              v-model="leftQueryParams.captureDate"
              type="date"
              placeholder="请选择日期"
              value-format="YYYY-MM-DD"
              size="small"
              style="width: 140px"
              @change="handleLeftQuery"
            />
            <el-select
              v-model="leftQueryParams.hasRisk"
              placeholder="请选择是否存在隐患"
              size="small"
              style="width: 160px"
              clearable
              @change="handleLeftQuery"
            >
              <el-option label="存在隐患" value="Y" />
              <el-option label="无隐患" value="N" />
            </el-select>
            <el-input v-model="leftQueryParams.keyword" placeholder="请输入桩号" size="small" style="width: 120px" @keyup.enter="handleLeftQuery" />
            <el-button type="primary" size="small" icon="Search" @click="handleLeftQuery">搜索</el-button>
            <el-button size="small" icon="Refresh" @click="handleLeftReset">重置</el-button>
          </div>
        </div>

        <!-- 左侧图片列表 -->
        <div class="flex-1 overflow-hidden">
          <el-table
            ref="leftTableRef"
            :data="leftImageList"
            v-loading="leftLoading"
            height="100%"
            size="small"
            highlight-current-row
            @current-change="handleLeftRowSelect"
          >
            <el-table-column width="50" align="center">
              <template #default="scope">
                <div
                  class="selection-circle"
                  :class="{ 'selected': leftSelectedImage?.id === scope.row.id }"
                  @click.stop="handleLeftRowSelect(scope.row)"
                ></div>
              </template>
            </el-table-column>
            <el-table-column label="序号" width="60" align="center">
              <template #default="scope">
                <span>{{ (leftQueryParams.pageNum - 1) * leftQueryParams.pageSize + scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column label="巡检图片" width="80" align="center">
              <template #default="scope">
                <el-image
                  :src="scope.row.webpUrl || scope.row.imageUrl"
                  fit="cover"
                  style="width: 40px; height: 40px; border-radius: 3px"
                  :initial-index="0"
                  @click="openPreview(scope.row)"
                />
              </template>
            </el-table-column>
            <el-table-column prop="pileNo" label="桩号" width="100" align="center" />
            <el-table-column label="图片拍摄时间" width="140" align="center">
              <template #default="scope">
                {{ scope.row.eventTime ? parseTime(scope.row.eventTime, '{y}-{m}-{d} {h}:{i}:{s}') : '-' }}
              </template>
            </el-table-column>
            <el-table-column label="是否存在安全隐患" width="120" align="center">
              <template #default="scope">
                <dict-tag :options="road_hazard_has_risk" :value="scope.row.hasRisk" />
              </template>
            </el-table-column>
            <el-table-column prop="riskNote" label="风险备注" align="center" show-overflow-tooltip />
          </el-table>
        </div>

        <!-- 左侧分页 -->
        <div class="p-2 border-t">
          <pagination
            v-show="leftTotal > 0"
            :total="leftTotal"
            v-model:page="leftQueryParams.pageNum"
            v-model:limit="leftQueryParams.pageSize"
            @pagination="getLeftImageList"
            layout="prev, pager, next"
            :small="true"
          />
        </div>
      </div>

      <!-- 右侧：上一次巡检记录图片 -->
      <div class="flex-1 border border-gray-200 rounded">
        <div class="bg-gray-50 p-3 border-b">
          <!-- 搜索区域 -->
          <div class="flex gap-2 items-center">
            <el-date-picker
              v-model="rightQueryParams.captureDate"
              type="date"
              placeholder="请选择日期"
              value-format="YYYY-MM-DD"
              size="small"
              style="width: 140px"
              @change="handleRightQuery"
            />
            <el-select
              v-model="rightQueryParams.hasRisk"
              placeholder="请选择是否存在隐患"
              size="small"
              style="width: 160px"
              clearable
              @change="handleRightQuery"
            >
              <el-option label="存在隐患" value="Y" />
              <el-option label="无隐患" value="N" />
            </el-select>
            <el-input v-model="rightQueryParams.keyword" placeholder="请输入桩号" size="small" style="width: 120px" @keyup.enter="handleRightQuery" />
            <el-button type="primary" size="small" icon="Search" @click="handleRightQuery">搜索</el-button>
            <el-button size="small" icon="Refresh" @click="handleRightReset">重置</el-button>
          </div>
        </div>

        <!-- 右侧图片列表 -->
        <div class="flex-1 overflow-hidden">
          <el-table
            ref="rightTableRef"
            :data="rightImageList"
            v-loading="rightLoading"
            height="100%"
            size="small"
            highlight-current-row
            @current-change="handleRightRowSelect"
          >
            <el-table-column width="50" align="center">
              <template #default="scope">
                <div
                  class="selection-circle"
                  :class="{ 'selected': rightSelectedImage?.id === scope.row.id }"
                  @click.stop="handleRightRowSelect(scope.row)"
                ></div>
              </template>
            </el-table-column>
            <el-table-column label="序号" width="60" align="center">
              <template #default="scope">
                <span>{{ (rightQueryParams.pageNum - 1) * rightQueryParams.pageSize + scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column label="巡检图片" width="80" align="center">
              <template #default="scope">
                <el-image
                  :src="scope.row.webpUrl || scope.row.imageUrl"
                  fit="cover"
                  style="width: 40px; height: 40px; border-radius: 3px"
                  :initial-index="0"
                  @click="openPreview(scope.row)"
                />
              </template>
            </el-table-column>
            <el-table-column prop="pileNo" label="桩号" width="100" align="center" />
            <el-table-column label="图片拍摄时间" width="140" align="center">
              <template #default="scope">
                {{ scope.row.eventTime ? parseTime(scope.row.eventTime, '{y}-{m}-{d} {h}:{i}:{s}') : '-' }}
              </template>
            </el-table-column>
            <el-table-column label="是否存在安全隐患" width="120" align="center">
              <template #default="scope">
                <dict-tag :options="road_hazard_has_risk" :value="scope.row.hasRisk" />
              </template>
            </el-table-column>
            <el-table-column prop="riskNote" label="风险备注" align="center" show-overflow-tooltip />
          </el-table>
        </div>

        <!-- 右侧分页 -->
        <div class="p-2 border-t">
          <pagination
            v-show="rightTotal > 0"
            :total="rightTotal"
            v-model:page="rightQueryParams.pageNum"
            v-model:limit="rightQueryParams.pageSize"
            @pagination="getRightImageList"
            layout="prev, pager, next"
            :small="true"
          />
        </div>
      </div>
    </div>

    <!-- 图形比对弹窗 -->
    <el-dialog title="图形比对" v-model="compareDialogVisible" width="95%" top="2vh" append-to-body destroy-on-close @open="handleCompareDialogOpen">
      <template #header>
        <div class="dialog-header">
          <span class="dialog-title">图形比对</span>
          <div class="header-controls">
            <el-switch v-model="showCompareLabels" active-text="显示框" inactive-text="隐藏框" size="small" />
          </div>
        </div>
      </template>

      <div v-if="leftSelectedImage && rightSelectedImage" class="compare-container">
        <div class="compare-side">
          <h4 class="side-title left-title">当前巡检记录</h4>
          <div class="image-container" ref="leftImageContainerRef">
            <div class="image-wrapper" ref="leftImageWrapperRef">
              <img
                ref="leftImageRef"
                :src="leftSelectedImage.imageUrl"
                alt="当前巡检记录"
                class="compare-image"
                @load="handleLeftImageLoad"
                @error="handleLeftImageError"
              />

              <!-- 左侧图片标签绘制 -->
              <div
                v-for="label in leftCompareLabels"
                v-show="showCompareLabels"
                :key="label.id"
                class="label-box"
                :class="{ 'near-top': isLeftLabelNearTop(label) }"
                :style="getLeftLabelStyle(label)"
                :title="`${getDictLabelName(label.label)} (${(label.confidence * 100).toFixed(1)}%)`"
              >
                <div class="label-tag">{{ getDictLabelName(label.label) }} ({{ (label.confidence * 100).toFixed(1) }}%)</div>
              </div>
            </div>

            <!-- 左侧加载状态 -->
            <div v-if="leftImageLoading" class="loading-overlay">
              <el-icon class="is-loading"><Loading /></el-icon>
              <span>加载中...</span>
            </div>

            <!-- 左侧错误状态 -->
            <div v-if="leftImageError" class="error-overlay">
              <el-icon><Picture /></el-icon>
              <span>图片加载失败</span>
            </div>
          </div>
          <div class="image-info">
            <div class="info-item">
              <span class="info-label">桩号:</span>
              <span class="info-value">{{ leftSelectedImage.pileNo || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">拍摄时间:</span>
              <span class="info-value">{{
                leftSelectedImage.eventTime ? parseTime(leftSelectedImage.eventTime, '{y}-{m}-{d} {h}:{i}:{s}') : '-'
              }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">风险状态:</span>
              <span class="info-value"><dict-tag :options="road_hazard_has_risk" :value="leftSelectedImage.hasRisk" /></span>
            </div>
            <div v-if="leftSelectedImage.riskNote" class="info-item">
              <span class="info-label">风险备注:</span>
              <span class="info-value">{{ leftSelectedImage.riskNote }}</span>
            </div>
          </div>
        </div>

        <div class="compare-side">
          <h4 class="side-title right-title">上一次巡检记录</h4>
          <div class="image-container" ref="rightImageContainerRef">
            <div class="image-wrapper" ref="rightImageWrapperRef">
              <img
                ref="rightImageRef"
                :src="rightSelectedImage.imageUrl"
                alt="上一次巡检记录"
                class="compare-image"
                @load="handleRightImageLoad"
                @error="handleRightImageError"
              />

              <!-- 右侧图片标签绘制 -->
              <div
                v-for="label in rightCompareLabels"
                v-show="showCompareLabels"
                :key="label.id"
                class="label-box"
                :class="{ 'near-top': isRightLabelNearTop(label) }"
                :style="getRightLabelStyle(label)"
                :title="`${getDictLabelName(label.label)} (${(label.confidence * 100).toFixed(1)}%)`"
              >
                <div class="label-tag">{{ getDictLabelName(label.label) }} ({{ (label.confidence * 100).toFixed(1) }}%)</div>
              </div>
            </div>

            <!-- 右侧加载状态 -->
            <div v-if="rightImageLoading" class="loading-overlay">
              <el-icon class="is-loading"><Loading /></el-icon>
              <span>加载中...</span>
            </div>

            <!-- 右侧错误状态 -->
            <div v-if="rightImageError" class="error-overlay">
              <el-icon><Picture /></el-icon>
              <span>图片加载失败</span>
            </div>
          </div>
          <div class="image-info">
            <div class="info-item">
              <span class="info-label">桩号:</span>
              <span class="info-value">{{ rightSelectedImage.pileNo || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">拍摄时间:</span>
              <span class="info-value">{{
                rightSelectedImage.eventTime ? parseTime(rightSelectedImage.eventTime, '{y}-{m}-{d} {h}:{i}:{s}') : '-'
              }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">风险状态:</span>
              <span class="info-value"><dict-tag :options="road_hazard_has_risk" :value="rightSelectedImage.hasRisk" /></span>
            </div>
            <div v-if="rightSelectedImage.riskNote" class="info-item">
              <span class="info-label">风险备注:</span>
              <span class="info-value">{{ rightSelectedImage.riskNote }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作区域 -->
      <div class="compare-footer">
        <div class="footer-content">
          <div class="risk-confirmation">
            <span class="confirmation-label">是否确认存在风险隐患：</span>
            <el-radio-group v-model="riskConfirmation" size="small">
              <el-radio value="Y">是</el-radio>
              <el-radio value="N">否</el-radio>
            </el-radio-group>
          </div>
          <div class="risk-note">
            <span class="note-label">备注：</span>
            <el-input v-model="riskNote" type="textarea" placeholder="请输入备注" :rows="2" maxlength="200" show-word-limit resize="none" />
          </div>
        </div>
        <div class="footer-actions">
          <el-button @click="compareDialogVisible = false">返回</el-button>
          <el-button type="primary" @click="handleConfirmRisk">确认</el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 图片预览弹窗 -->
    <PreviewModal v-model="previewVisible" :image-url="previewImageUrl" :labels="currentLabels" @close="handlePreviewClose" />

    <!-- 左侧图形比对图片预览弹窗 -->
    <PreviewModal
      v-model="leftComparePreviewVisible"
      :image-url="leftComparePreviewImageUrl"
      :labels="leftCompareLabels"
      @close="handleLeftComparePreviewClose"
    />

    <!-- 右侧图形比对图片预览弹窗 -->
    <PreviewModal
      v-model="rightComparePreviewVisible"
      :image-url="rightComparePreviewImageUrl"
      :labels="rightCompareLabels"
      @close="handleRightComparePreviewClose"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { listRoadHazardRecordItem, updateRoadHazardRecordItem, getRoadHazardRecordItem } from '@/api/biz/roadHazardRecordItem';
import { RoadHazardRecordItemVO, RoadHazardRecordItemQuery, RoadHazardRecordItemForm } from '@/api/biz/roadHazardRecordItem/types';
import { getPreviousRecordId } from '@/api/biz/roadHazardRecord';
import PreviewModal from '@/components/PreviewModal/index.vue';
import { Loading, Picture } from '@element-plus/icons-vue';
import { useImageLabels } from '@/hooks/useImageLabels';

// 使用后端的数据类型，并添加一些前端需要的字段
type HazardPointImageVO = RoadHazardRecordItemVO & {
  imageCode?: string; // 如果后端没有这个字段，可以用其他字段代替
  captureTime: string; // 使用 eventTime
  riskLocation?: string; // 使用 riskNote 或其他字段
};

// 查询参数类型
interface ImageQueryParams {
  pageNum: number;
  pageSize: number;
  captureDate?: string;
  hasRisk?: string;
  keyword?: string;
}

const props = defineProps<{
  modelValue: boolean;
  hazardPointId: string | number;
  inspectRecordId: string | number;
  hazardPointName?: string;
}>();

const emit = defineEmits<{
  'update:modelValue': [value: boolean];
}>();

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { road_hazard_has_risk } = toRefs<any>(proxy?.useDict('road_hazard_has_risk'));

// 弹窗显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// 左侧（当前巡检记录）相关状态
const leftImageList = ref<HazardPointImageVO[]>([]);
const leftLoading = ref(false);
const leftTotal = ref(0);
const leftSelectedImage = ref<HazardPointImageVO>();
const leftTableRef = ref();

// 右侧（上一次巡检记录）相关状态
const rightImageList = ref<HazardPointImageVO[]>([]);
const rightLoading = ref(false);
const rightTotal = ref(0);
const rightSelectedImage = ref<HazardPointImageVO>();
const rightTableRef = ref();
const previousRecordId = ref<number>();

// 图形比对弹窗
const compareDialogVisible = ref(false);

// 图片预览相关状态
const previewVisible = ref(false);
const previewImageUrl = ref('');

// 图形比对弹窗的图片预览状态
const comparePreviewVisible = ref(false);
const comparePreviewImageUrl = ref('');

// 使用图片标签 hook
const { currentLabels, loadLabels, clearLabels } = useImageLabels();

// 图形比对弹窗的左右图片预览状态
const leftComparePreviewVisible = ref(false);
const rightComparePreviewVisible = ref(false);
const leftComparePreviewImageUrl = ref('');
const rightComparePreviewImageUrl = ref('');
const leftCompareLabels = ref([]);
const rightCompareLabels = ref([]);

// 图形比对弹窗相关状态
const showCompareLabels = ref(true); // 标签显示开关
const riskConfirmation = ref<string>(''); // 风险确认状态：Y是 N否
const riskNote = ref<string>(''); // 风险备注
const leftImageLoading = ref(false);
const rightImageLoading = ref(false);
const leftImageError = ref(false);
const rightImageError = ref(false);
const leftImageLoaded = ref(false);
const rightImageLoaded = ref(false);

// 图形比对弹窗的DOM引用
const leftImageContainerRef = ref<HTMLElement>();
const rightImageContainerRef = ref<HTMLElement>();
const leftImageWrapperRef = ref<HTMLElement>();
const rightImageWrapperRef = ref<HTMLElement>();
const leftImageRef = ref<HTMLImageElement>();
const rightImageRef = ref<HTMLImageElement>();

// 强制更新标签位置的key
const leftLabelUpdateKey = ref(0);
const rightLabelUpdateKey = ref(0);

// 获取当前实例以使用字典
const { ai_image_tag } = toRefs<any>(proxy?.useDict('ai_image_tag'));

// 查询参数
const leftQueryParams = reactive<ImageQueryParams>({
  pageNum: 1,
  pageSize: 10,
  captureDate: undefined,
  hasRisk: undefined,
  keyword: undefined
});

const rightQueryParams = reactive<ImageQueryParams>({
  pageNum: 1,
  pageSize: 10,
  captureDate: undefined,
  hasRisk: undefined,
  keyword: undefined
});

// 获取当前巡检记录图片列表
const getLeftImageList = async () => {
  if (!props.hazardPointId || !props.inspectRecordId) return;

  leftLoading.value = true;
  try {
    const queryParams: RoadHazardRecordItemQuery = {
      pageNum: leftQueryParams.pageNum,
      pageSize: leftQueryParams.pageSize,
      inspectRecordId: props.inspectRecordId,
      hazardPointId: props.hazardPointId,
      hasRisk: leftQueryParams.hasRisk,
      // 如果有桩号关键词搜索
      pileNo: leftQueryParams.keyword,
      // 如果有日期筛选，直接使用新的时间范围字段
      eventTimeStart: leftQueryParams.captureDate ? leftQueryParams.captureDate + ' 00:00:00' : undefined,
      eventTimeEnd: leftQueryParams.captureDate ? leftQueryParams.captureDate + ' 23:59:59' : undefined
    };

    const res = await listRoadHazardRecordItem(queryParams);

    // 直接使用后端返回的数据，不需要额外转换
    leftImageList.value = res.rows.map((item) => ({
      ...item,
      imageCode: item.id?.toString() || '', // 使用 ID 作为编号（如果需要的话）
      captureTime: item.eventTime || '', // 保持兼容性
      riskLocation: item.riskNote || '' // 保持兼容性
    }));
    leftTotal.value = res.total;
  } catch (error) {
    console.error('获取当前巡检图片失败:', error);
    proxy?.$modal.msgError('获取图片列表失败');
  } finally {
    leftLoading.value = false;
  }
};

// 获取上一次巡检记录图片列表
const getRightImageList = async () => {
  if (!props.hazardPointId || !props.inspectRecordId) return;

  rightLoading.value = true;
  try {
    // 如果还没有获取上一次巡检记录ID，先获取
    if (!previousRecordId.value) {
      const previousRes = await getPreviousRecordId(props.inspectRecordId, props.hazardPointId);
      previousRecordId.value = previousRes.data;
    }

    // 如果没有上一次巡检记录，直接返回空数据
    if (!previousRecordId.value) {
      rightImageList.value = [];
      rightTotal.value = 0;
      return;
    }

    // 使用上一次巡检记录ID获取图片列表
    const queryParams: RoadHazardRecordItemQuery = {
      pageNum: rightQueryParams.pageNum,
      pageSize: rightQueryParams.pageSize,
      inspectRecordId: previousRecordId.value,
      hazardPointId: props.hazardPointId,
      hasRisk: rightQueryParams.hasRisk,
      // 如果有桩号关键词搜索
      pileNo: rightQueryParams.keyword,
      // 如果有日期筛选，直接使用新的时间范围字段
      eventTimeStart: rightQueryParams.captureDate ? rightQueryParams.captureDate + ' 00:00:00' : undefined,
      eventTimeEnd: rightQueryParams.captureDate ? rightQueryParams.captureDate + ' 23:59:59' : undefined
    };

    const res = await listRoadHazardRecordItem(queryParams);

    // 直接使用后端返回的数据，保持与左侧一致的数据结构
    rightImageList.value = res.rows.map((item) => ({
      ...item,
      imageCode: item.id?.toString() || '', // 使用 ID 作为编号
      captureTime: item.eventTime || '', // 保持兼容性
      riskLocation: item.riskNote || '' // 保持兼容性
    }));
    rightTotal.value = res.total;
  } catch (error) {
    console.error('获取上一次巡检图片失败:', error);
    // 如果是因为没有上一次巡检记录导致的错误，不显示错误信息
    if (error?.response?.status !== 404) {
      proxy?.$modal.msgError('获取上一次巡检图片失败');
    }
    rightImageList.value = [];
    rightTotal.value = 0;
  } finally {
    rightLoading.value = false;
  }
};

// 左侧查询操作
const handleLeftQuery = () => {
  leftQueryParams.pageNum = 1;
  getLeftImageList();
};

// 左侧重置操作
const handleLeftReset = () => {
  leftQueryParams.captureDate = undefined;
  leftQueryParams.hasRisk = undefined;
  leftQueryParams.keyword = undefined;
  handleLeftQuery();
};

// 右侧查询操作
const handleRightQuery = () => {
  rightQueryParams.pageNum = 1;
  getRightImageList();
};

// 右侧重置操作
const handleRightReset = () => {
  rightQueryParams.captureDate = undefined;
  rightQueryParams.hasRisk = undefined;
  rightQueryParams.keyword = undefined;
  handleRightQuery();
};

// 左侧行选择
const handleLeftRowSelect = (row: HazardPointImageVO) => {
  leftSelectedImage.value = row;
};

// 右侧行选择
const handleRightRowSelect = (row: HazardPointImageVO) => {
  rightSelectedImage.value = row;
};

// 图形比对
const handleImageCompare = () => {
  if (!leftSelectedImage.value || !rightSelectedImage.value) {
    proxy?.$modal.msgWarning('请先选择要比对的图片');
    return;
  }
  compareDialogVisible.value = true;
};

// 图片预览
const openPreview = async (row: HazardPointImageVO) => {
  previewImageUrl.value = row.imageUrl;

  // 加载图片的AI标签
  await loadLabels(row.imageUrl);

  previewVisible.value = true;
};

// 处理预览关闭
const handlePreviewClose = () => {
  previewVisible.value = false;
  previewImageUrl.value = '';
};

// 左侧图形比对图片预览
const openLeftComparePreview = async () => {
  if (!leftSelectedImage.value) return;

  // 加载左侧图片的AI标签
  await loadLabels(leftSelectedImage.value.imageUrl);
  leftCompareLabels.value = [...currentLabels.value];

  leftComparePreviewImageUrl.value = leftSelectedImage.value.imageUrl;
  leftComparePreviewVisible.value = true;
};

// 右侧图形比对图片预览
const openRightComparePreview = async () => {
  if (!rightSelectedImage.value) return;

  // 加载右侧图片的AI标签
  await loadLabels(rightSelectedImage.value.imageUrl);
  rightCompareLabels.value = [...currentLabels.value];

  rightComparePreviewImageUrl.value = rightSelectedImage.value.imageUrl;
  rightComparePreviewVisible.value = true;
};

// 处理左侧图形比对预览关闭
const handleLeftComparePreviewClose = () => {
  leftComparePreviewVisible.value = false;
  leftCompareLabels.value = [];
  clearLabels();
};

// 处理右侧图形比对预览关闭
const handleRightComparePreviewClose = () => {
  rightComparePreviewVisible.value = false;
  rightCompareLabels.value = [];
  clearLabels();
};

// 处理风险确认
const handleConfirmRisk = async () => {
  if (!riskConfirmation.value) {
    proxy?.$modal.msgWarning('请选择是否存在风险隐患');
    return;
  }

  if (!leftSelectedImage.value) {
    proxy?.$modal.msgError('未选择当前巡检记录图片');
    return;
  }

  try {
    // 构造更新数据，只更新风险状态和备注字段
    const updateData: RoadHazardRecordItemForm = {
      id: leftSelectedImage.value.id,
      hasRisk: riskConfirmation.value,
      riskNote: riskNote.value
    };

    // 调用API更新当前巡检记录条目
    await updateRoadHazardRecordItem(updateData);

    // 更新本地数据
    leftSelectedImage.value.hasRisk = riskConfirmation.value;
    leftSelectedImage.value.riskNote = riskNote.value;

    // 更新左侧列表中对应的数据
    const index = leftImageList.value.findIndex((item) => item.id === leftSelectedImage.value?.id);
    if (index !== -1) {
      leftImageList.value[index].hasRisk = riskConfirmation.value;
      leftImageList.value[index].riskNote = riskNote.value;
    }

    proxy?.$modal.msgSuccess('风险确认成功');

    // 关闭弹窗
    compareDialogVisible.value = false;
  } catch (error) {
    console.error('更新风险确认失败:', error);
    proxy?.$modal.msgError('更新失败，请稍后重试');
  }
};

// 图形比对弹窗打开时的处理
const handleCompareDialogOpen = async () => {
  if (!leftSelectedImage.value || !rightSelectedImage.value) return;

  // 重置状态
  leftImageLoading.value = true;
  rightImageLoading.value = true;
  leftImageError.value = false;
  rightImageError.value = false;
  leftImageLoaded.value = false;
  rightImageLoaded.value = false;
  leftCompareLabels.value = [];
  rightCompareLabels.value = [];

  // 查询左侧图片的详细信息，获取数据库中的原有值
  try {
    if (leftSelectedImage.value.id) {
      const leftImageDetail = await getRoadHazardRecordItem(leftSelectedImage.value.id);
      // 填充表单字段
      riskConfirmation.value = leftImageDetail.data.hasRisk || '';
      riskNote.value = leftImageDetail.data.riskNote || '';
    }
  } catch (error) {
    console.error('获取左侧图片详细信息失败:', error);
    // 如果获取失败，使用当前选中图片的数据作为默认值
    riskConfirmation.value = leftSelectedImage.value.hasRisk || '';
    riskNote.value = leftSelectedImage.value.riskNote || '';
  }

  // 串行加载图片标签，避免并发请求导致的重复提交问题

  // 先加载左侧图片的AI标签
  try {
    await loadLabels(leftSelectedImage.value.imageUrl);
    leftCompareLabels.value = [...currentLabels.value];
  } catch (error) {
    console.error('加载左侧图片标签失败:', error);
  }

  // 等待600ms，确保避开防重复提交的500ms限制
  await new Promise((resolve) => setTimeout(resolve, 600));

  // 再加载右侧图片的AI标签
  try {
    await loadLabels(rightSelectedImage.value.imageUrl);
    rightCompareLabels.value = [...currentLabels.value];
  } catch (error) {
    console.error('加载右侧图片标签失败:', error);
  }
};

// 左侧图片加载完成
const handleLeftImageLoad = () => {
  leftImageLoading.value = false;
  leftImageError.value = false;
  leftImageLoaded.value = true;
  nextTick(() => {
    forceUpdateLeftLabels();
  });
};

// 左侧图片加载错误
const handleLeftImageError = () => {
  leftImageLoading.value = false;
  leftImageError.value = true;
  leftImageLoaded.value = false;
};

// 右侧图片加载完成
const handleRightImageLoad = () => {
  rightImageLoading.value = false;
  rightImageError.value = false;
  rightImageLoaded.value = true;
  nextTick(() => {
    forceUpdateRightLabels();
  });
};

// 右侧图片加载错误
const handleRightImageError = () => {
  rightImageLoading.value = false;
  rightImageError.value = true;
  rightImageLoaded.value = false;
};

// 强制更新左侧标签位置
const forceUpdateLeftLabels = () => {
  leftLabelUpdateKey.value++;
};

// 强制更新右侧标签位置
const forceUpdateRightLabels = () => {
  rightLabelUpdateKey.value++;
};

// 判断左侧标签是否靠近顶部
const isLeftLabelNearTop = (label: any): boolean => {
  if (!leftImageRef.value || !leftImageLoaded.value) {
    return false;
  }

  const imageElement = leftImageRef.value;
  const containerElement = leftImageWrapperRef.value;

  if (!containerElement) {
    return false;
  }

  const naturalWidth = imageElement.naturalWidth;
  const naturalHeight = imageElement.naturalHeight;
  const displayWidth = imageElement.offsetWidth;
  const displayHeight = imageElement.offsetHeight;

  const containerWidth = containerElement.offsetWidth;
  const containerHeight = containerElement.offsetHeight;
  const offsetX = (containerWidth - displayWidth) / 2;
  const offsetY = (containerHeight - displayHeight) / 2;

  const scaleY = displayHeight / naturalHeight;
  const top = label.y * scaleY + offsetY;

  return top < 30;
};

// 判断右侧标签是否靠近顶部
const isRightLabelNearTop = (label: any): boolean => {
  if (!rightImageRef.value || !rightImageLoaded.value) {
    return false;
  }

  const imageElement = rightImageRef.value;
  const containerElement = rightImageWrapperRef.value;

  if (!containerElement) {
    return false;
  }

  const naturalWidth = imageElement.naturalWidth;
  const naturalHeight = imageElement.naturalHeight;
  const displayWidth = imageElement.offsetWidth;
  const displayHeight = imageElement.offsetHeight;

  const containerWidth = containerElement.offsetWidth;
  const containerHeight = containerElement.offsetHeight;
  const offsetX = (containerWidth - displayWidth) / 2;
  const offsetY = (containerHeight - displayHeight) / 2;

  const scaleY = displayHeight / naturalHeight;
  const top = label.y * scaleY + offsetY;

  return top < 30;
};

// 获取左侧标签样式
const getLeftLabelStyle = (label: any): Record<string, string | number> => {
  // 依赖 leftLabelUpdateKey 来强制重新计算
  leftLabelUpdateKey.value;

  if (!leftImageRef.value || !leftImageLoaded.value) {
    return { display: 'none' };
  }

  const imageElement = leftImageRef.value;
  const containerElement = leftImageWrapperRef.value;

  if (!containerElement) {
    return { display: 'none' };
  }

  // 计算图片的实际显示尺寸
  const naturalWidth = imageElement.naturalWidth;
  const naturalHeight = imageElement.naturalHeight;
  const displayWidth = imageElement.offsetWidth;
  const displayHeight = imageElement.offsetHeight;

  // 计算图片在容器中的偏移位置（因为图片居中显示）
  const containerWidth = containerElement.offsetWidth;
  const containerHeight = containerElement.offsetHeight;
  const offsetX = (containerWidth - displayWidth) / 2;
  const offsetY = (containerHeight - displayHeight) / 2;

  // 计算缩放比例
  const scaleX = displayWidth / naturalWidth;
  const scaleY = displayHeight / naturalHeight;

  // 计算标签在显示图片上的位置（加上图片偏移量）
  const left = label.x * scaleX + offsetX;
  const top = label.y * scaleY + offsetY;
  const width = label.width * scaleX;
  const height = label.height * scaleY;

  return {
    position: 'absolute',
    left: `${left}px`,
    top: `${top}px`,
    width: `${width}px`,
    height: `${height}px`,
    border: '2px solid #F56C6C',
    backgroundColor: 'rgba(245, 108, 108, 0.1)',
    borderRadius: '4px',
    cursor: 'pointer',
    zIndex: 10
  };
};

// 获取右侧标签样式
const getRightLabelStyle = (label: any): Record<string, string | number> => {
  // 依赖 rightLabelUpdateKey 来强制重新计算
  rightLabelUpdateKey.value;

  if (!rightImageRef.value || !rightImageLoaded.value) {
    return { display: 'none' };
  }

  const imageElement = rightImageRef.value;
  const containerElement = rightImageWrapperRef.value;

  if (!containerElement) {
    return { display: 'none' };
  }

  // 计算图片的实际显示尺寸
  const naturalWidth = imageElement.naturalWidth;
  const naturalHeight = imageElement.naturalHeight;
  const displayWidth = imageElement.offsetWidth;
  const displayHeight = imageElement.offsetHeight;

  // 计算图片在容器中的偏移位置（因为图片居中显示）
  const containerWidth = containerElement.offsetWidth;
  const containerHeight = containerElement.offsetHeight;
  const offsetX = (containerWidth - displayWidth) / 2;
  const offsetY = (containerHeight - displayHeight) / 2;

  // 计算缩放比例
  const scaleX = displayWidth / naturalWidth;
  const scaleY = displayHeight / naturalHeight;

  // 计算标签在显示图片上的位置（加上图片偏移量）
  const left = label.x * scaleX + offsetX;
  const top = label.y * scaleY + offsetY;
  const width = label.width * scaleX;
  const height = label.height * scaleY;

  return {
    position: 'absolute',
    left: `${left}px`,
    top: `${top}px`,
    width: `${width}px`,
    height: `${height}px`,
    border: '2px solid #F56C6C',
    backgroundColor: 'rgba(245, 108, 108, 0.1)',
    borderRadius: '4px',
    cursor: 'pointer',
    zIndex: 10
  };
};

// 获取字典映射的标签名称
const getDictLabelName = (labelValue: string): string => {
  if (!ai_image_tag.value || ai_image_tag.value.length === 0) {
    return labelValue; // 如果字典未加载，返回原始值
  }
  return proxy?.selectDictLabel(ai_image_tag.value, labelValue) || labelValue;
};

// 关闭弹窗
const handleClose = () => {
  // 重置状态
  leftSelectedImage.value = undefined;
  rightSelectedImage.value = undefined;
  compareDialogVisible.value = false;
};

// 监听弹窗显示状态，初始化数据
watch(visible, (newVal) => {
  if (newVal && props.hazardPointId) {
    getLeftImageList();
    getRightImageList();
  }
});
</script>

<style scoped>
.flex {
  display: flex;
}

.flex-1 {
  flex: 1;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-4 {
  gap: 1rem;
}

.items-center {
  align-items: center;
}

.text-center {
  text-align: center;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.p-2 {
  padding: 0.5rem;
}

.p-3 {
  padding: 0.75rem;
}

.border {
  border: 1px solid #e5e7eb;
}

.border-b {
  border-bottom: 1px solid #e5e7eb;
}

.border-t {
  border-top: 1px solid #e5e7eb;
}

.border-gray-200 {
  border-color: #e5e7eb;
}

.rounded {
  border-radius: 0.375rem;
}

.bg-blue-50 {
  background-color: #eff6ff;
}

.bg-gray-50 {
  background-color: #f9fafb;
}

.text-blue-800 {
  color: #1e40af;
}

.text-gray-800 {
  color: #1f2937;
}

.text-gray-600 {
  color: #4b5563;
}

.text-sm {
  font-size: 0.875rem;
}

.font-medium {
  font-weight: 500;
}

.overflow-hidden {
  overflow: hidden;
}

.h-[70vh] {
  height: 70vh;
}

.gap-6 {
  gap: 1.5rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.text-lg {
  font-size: 1.125rem;
}

.space-y-1 > * + * {
  margin-top: 0.25rem;
}

.justify-between {
  justify-content: space-between;
}

.text-left {
  text-align: left;
}

/* 选中圆点样式 */
.selection-circle {
  width: 12px;
  height: 12px;
  border: 2px solid #e4e7ed;
  border-radius: 50%;
  background-color: transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 0 auto;
}

.selection-circle.selected {
  background-color: #409eff;
  border-color: #409eff;
}

.selection-circle:hover {
  border-color: #409eff;
  transform: scale(1.1);
}

/* 图形比对弹窗样式 */
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.dialog-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.header-controls {
  display: flex;
  align-items: center;
}

.compare-container {
  display: flex;
  gap: 1.5rem;
  height: 55vh;
}

.compare-side {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.side-title {
  margin-bottom: 0.75rem;
  font-size: 1.125rem;
  font-weight: 500;
  text-align: center;
}

.left-title {
  color: #1e40af;
}

.right-title {
  color: #1f2937;
}

.image-container {
  position: relative;
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.image-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.compare-image {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
  border-radius: 4px;
  display: block;
}

.label-box {
  box-sizing: border-box;
  transition: all 0.2s ease;
}

.label-box:hover {
  background-color: rgba(103, 194, 58, 0.2) !important;
  border-color: #409eff !important;
  transform: scale(1.05);
}

.label-box:hover .label-tag {
  color: #409eff !important;
  background-color: rgba(64, 158, 255, 0.9) !important;
}

.label-tag {
  position: absolute;
  background-color: rgba(245, 108, 108, 0.9);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  white-space: nowrap;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  transition: all 0.2s ease;
  z-index: 11;
  top: -24px;
  left: 0;
}

.label-tag::before {
  content: '';
  position: absolute;
  top: 100%;
  left: 8px;
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid rgba(245, 108, 108, 0.9);
  transition: border-top-color 0.2s ease;
}

.label-box.near-top .label-tag {
  top: 100%;
  left: 0;
}

.label-box.near-top .label-tag::before {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 8px;
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 4px solid rgba(245, 108, 108, 0.9);
  border-top: none;
}

.label-box:hover .label-tag::before {
  border-top-color: rgba(64, 158, 255, 0.9);
}

.label-box.near-top:hover .label-tag::before {
  border-bottom-color: rgba(64, 158, 255, 0.9);
}

.loading-overlay,
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.9);
  color: #909399;
  font-size: 14px;
}

.loading-overlay .el-icon,
.error-overlay .el-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.image-info {
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 8px;
  display: grid;
  grid-template-columns: 1fr;
  gap: 8px;
}

.info-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.info-label {
  font-weight: 500;
  color: #606266;
  margin-right: 8px;
  min-width: 80px;
}

.info-value {
  color: #303133;
  word-break: break-all;
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .compare-container {
    flex-direction: column;
    height: auto;
  }

  .compare-side {
    margin-bottom: 1rem;
  }

  .image-container {
    height: 300px;
  }
}

/* 底部操作区域样式 */
.compare-footer {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px 20px;
  border-top: 1px solid #e4e7ed;
  background-color: #fafafa;
}

.footer-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-right: 20px;
}

.risk-confirmation {
  display: flex;
  align-items: center;
  gap: 12px;
}

.confirmation-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
  white-space: nowrap;
}

.risk-note {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.note-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
  white-space: nowrap;
  margin-top: 8px;
}

.risk-note .el-textarea {
  flex: 1;
  max-width: 400px;
}

.footer-actions {
  display: flex;
  gap: 12px;
  align-self: flex-end;
}
</style>
