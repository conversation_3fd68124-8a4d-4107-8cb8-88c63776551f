<template>
  <el-image :src="realSrc" fit="cover" :style="`width:${realWidth};height:${realHeight};`" :preview-src-list="props.disablePreview ? [] : realSrcList" preview-teleported>
    <template #error>
      <div class="image-slot">
        <el-icon><picture-filled /></el-icon>
      </div>
    </template>
  </el-image>
</template>

<script setup lang="ts">
import { propTypes } from '@/utils/propTypes';
import { ref, onMounted } from 'vue';

const props = defineProps({
  src: propTypes.string.def(''),
  width: {
    type: [Number, String],
    default: ''
  },
  height: {
    type: [Number, String],
    default: ''
  },
  disablePreview: {
    type: Boolean,
    default: false
  }
});

const realSrc = computed(() => {
  if (!props.src) {
    return;
  }
  const real_src = props.src.split(',')[0];
  // 如果是HTTP资源且当前页面是HTTPS，则通过代理转发
  if (real_src && real_src.startsWith('http:') && window.location.protocol === 'https:') {
    return import.meta.env.VITE_APP_BASE_API + '/proxy?url=' + encodeURIComponent(real_src);
  }
  return real_src;
});

const realSrcList = computed(() => {
  if (!props.src) {
    return [];
  }
  const real_src_list = props.src.split(',');
  const srcList: string[] = [];
  real_src_list.forEach((item: string) => {
    if (item.trim() === '') {
      return;
    }
    // 如果是HTTP资源且当前页面是HTTPS，则通过代理转发
    if (item && item.startsWith('http:') && window.location.protocol === 'https:') {
      return srcList.push(import.meta.env.VITE_APP_BASE_API + '/proxy?url=' + encodeURIComponent(item));
    }
    return srcList.push(item);
  });
  return srcList;
});

const realWidth = computed(() => (typeof props.width == 'string' ? props.width : `${props.width}px`));

const realHeight = computed(() => (typeof props.height == 'string' ? props.height : `${props.height}px`));
</script>

<style lang="scss" scoped>
.el-image {
  border-radius: 5px;
  background-color: #ebeef5;
  box-shadow: 0 0 5px 1px #ccc;

  :deep(.el-image__inner) {
    transition: all 0.3s;
    cursor: pointer;

    &:hover {
      transform: scale(1.2);
    }
  }

  :deep(.image-slot) {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    color: #909399;
    font-size: 30px;
  }
}
</style>
