<template>
  <el-dialog v-model="visible" :width="dialogWidth" :style="dialogStyle" align-center destroy-on-close @close="handleClose">
    <template #header>
      <div class="dialog-header">
        <span class="dialog-title">图片预览</span>
        <div class="header-controls">
          <el-switch v-model="showLabels" active-text="显示框" inactive-text="隐藏框" size="small" />
        </div>
      </div>
    </template>

    <div class="preview-container" ref="containerRef">
      <div class="image-wrapper" ref="imageWrapperRef">
        <img ref="imageRef" :src="imageUrl" alt="预览图片" class="preview-image" @load="handleImageLoad" @error="handleImageError" />

        <!-- 标签绘制 -->
        <div
          v-for="label in labels"
          v-show="showLabels"
          :key="label.id"
          class="label-box"
          :class="{ 'near-top': isLabelNearTop(label) }"
          :style="getLabelStyle(label)"
          :title="`${getDictLabelName(label.label)} (${(label.confidence * 100).toFixed(1)}%)`"
        >
          <!-- 类型标签显示在框体上方 -->
          <div class="label-tag">{{ getDictLabelName(label.label) }} ({{ (label.confidence * 100).toFixed(1) }}%)</div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-overlay">
        <el-icon class="is-loading">
          <Loading />
        </el-icon>
        <span>加载中...</span>
      </div>

      <!-- 错误状态 -->
      <div v-if="error" class="error-overlay">
        <el-icon>
          <Picture />
        </el-icon>
        <span>图片加载失败</span>
      </div>
    </div>

    <!-- 图片信息 -->
<!--    <div v-if="imageInfo" class="image-info">-->
<!--      <div class="info-item">-->
<!--        <span class="info-label">当前标签:</span>-->
<!--        <span class="info-value">{{ currentLabel || '无' }}</span>-->
<!--      </div>-->
<!--      <div class="info-item">-->
<!--        <span class="info-label">置信度:</span>-->
<!--        <span class="info-value">{{ currentConfidence ? `${(currentConfidence * 100).toFixed(2)}%` : '0%' }}</span>-->
<!--      </div>-->
<!--      <div class="info-item">-->
<!--        <span class="info-label">经纬度:</span>-->
<!--        <span class="info-value">{{ `${imageInfo.latitude}, ${imageInfo.longitude}` }}</span>-->
<!--      </div>-->
<!--      <div class="info-item">-->
<!--        <span class="info-label">文件名:</span>-->
<!--        <span class="info-value">{{ imageInfo.file_name }}</span>-->
<!--      </div>-->
<!--      <div class="info-item">-->
<!--        <span class="info-label">文件大小:</span>-->
<!--        <span class="info-value">{{ formatFileSize(imageInfo.file_size) }}</span>-->
<!--      </div>-->
<!--      <div class="info-item">-->
<!--        <span class="info-label">绝对高度:</span>-->
<!--        <span class="info-value">{{ imageInfo.absolute_altitude }} m</span>-->
<!--      </div>-->
<!--      <div class="info-item">-->
<!--        <span class="info-label">相对高度:</span>-->
<!--        <span class="info-value">{{ imageInfo.relative_altitude }} m</span>-->
<!--      </div>-->
<!--      <div class="info-item">-->
<!--        <span class="info-label">云台偏航:</span>-->
<!--        <span class="info-value">{{ imageInfo.gimbal_yaw_degree }}°</span>-->
<!--      </div>-->
<!--      <div class="info-item">-->
<!--        <span class="info-label">图片宽度:</span>-->
<!--        <span class="info-value">{{ imageInfo.image_width }} px</span>-->
<!--      </div>-->
<!--      <div class="info-item">-->
<!--        <span class="info-label">图片高度:</span>-->
<!--        <span class="info-value">{{ imageInfo.image_height }} px</span>-->
<!--      </div>-->
<!--    </div>-->
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onUnmounted, onMounted, getCurrentInstance } from 'vue';
import { ElMessage } from 'element-plus';
import { Loading, Picture } from '@element-plus/icons-vue';
import { toRefs } from 'vue';

// 获取当前实例
const { proxy } = getCurrentInstance() as any;

// 使用ai_image_tag数据字典
const { ai_image_tag } = toRefs<any>(proxy?.useDict('ai_image_tag'));

// 定义标签数据接口
interface LabelData {
  id: number;
  label: string;
  x: number;
  y: number;
  width: number;
  height: number;
  confidence: number;
  longitude?: number;
  latitude?: number;
  file_size?: number;
  file_name?: string;
  absolute_altitude?: number;
  relative_altitude?: number;
  gimbal_yaw_degree?: number;
  image_width?: number;
  image_height?: number;
}

// 组件属性定义
interface Props {
  modelValue: boolean;
  imageUrl: string;
  labels?: LabelData[];
}

// 组件事件定义
interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'close'): void;
}

// 属性和事件定义
const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  imageUrl: '',
  labels: () => []
});

const emit = defineEmits<Emits>();

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const containerRef = ref<HTMLElement>();
const imageWrapperRef = ref<HTMLElement>();
const imageRef = ref<HTMLImageElement>();
const loading = ref(false);
const error = ref(false);
const imageLoaded = ref(false);
const showLabels = ref(true); // 新增：标签显示开关

// 监听窗口尺寸变化
const resizeObserver = ref<ResizeObserver | null>(null);

// 计算弹窗宽度
const dialogWidth = computed(() => {
  const screenWidth = window.innerWidth;
  const minWidth = Math.floor(screenWidth * 0.6);
  const maxWidth = 1200;
  return `${Math.min(maxWidth, Math.max(minWidth, 600))}px`;
});

// 弹窗样式
const dialogStyle = computed(() => ({
  '--el-dialog-width': dialogWidth.value,
  maxWidth: '1200px',
  minWidth: '60vw'
}));

// 当前选中的标签信息
const currentLabel = ref<string>('');
const currentConfidence = ref<number>(0);

// 图片信息（取第一个标签的信息）
const imageInfo = computed(() => {
  if (props.labels && props.labels.length > 0) {
    return props.labels[0];
  }
  return null;
});

// 强制更新标签位置
const labelUpdateKey = ref(0);
const forceUpdateLabels = () => {
  labelUpdateKey.value++;
};

// 判断标签是否靠近顶部
const isLabelNearTop = (label: LabelData): boolean => {
  if (!imageRef.value || !imageLoaded.value) {
    return false;
  }

  const imageElement = imageRef.value;
  const containerElement = imageWrapperRef.value;

  if (!containerElement) {
    return false;
  }

  const naturalWidth = imageElement.naturalWidth;
  const naturalHeight = imageElement.naturalHeight;
  const displayWidth = imageElement.offsetWidth;
  const displayHeight = imageElement.offsetHeight;

  const containerWidth = containerElement.offsetWidth;
  const containerHeight = containerElement.offsetHeight;
  const offsetX = (containerWidth - displayWidth) / 2;
  const offsetY = (containerHeight - displayHeight) / 2;

  const scaleY = displayHeight / naturalHeight;
  const top = label.y * scaleY + offsetY;

  return top < 30;
};

// 获取标签样式
const getLabelStyle = (label: LabelData): Record<string, string | number> => {
  // 依赖 labelUpdateKey 来强制重新计算
  labelUpdateKey.value;

  if (!imageRef.value || !imageLoaded.value) {
    return { display: 'none' };
  }

  const imageElement = imageRef.value;
  const containerElement = imageWrapperRef.value;

  if (!containerElement) {
    return { display: 'none' };
  }

  // 计算图片的实际显示尺寸
  const naturalWidth = imageElement.naturalWidth;
  const naturalHeight = imageElement.naturalHeight;
  const displayWidth = imageElement.offsetWidth;
  const displayHeight = imageElement.offsetHeight;

  // 计算图片在容器中的偏移位置（因为图片居中显示）
  const containerWidth = containerElement.offsetWidth;
  const containerHeight = containerElement.offsetHeight;
  const offsetX = (containerWidth - displayWidth) / 2;
  const offsetY = (containerHeight - displayHeight) / 2;

  // 计算缩放比例
  const scaleX = displayWidth / naturalWidth;
  const scaleY = displayHeight / naturalHeight;

  // 计算标签在显示图片上的位置（加上图片偏移量）
  const left = label.x * scaleX + offsetX;
  const top = label.y * scaleY + offsetY;
  const width = label.width * scaleX;
  const height = label.height * scaleY;

  return {
    position: 'absolute',
    left: `${left}px`,
    top: `${top}px`,
    width: `${width}px`,
    height: `${height}px`,
    border: '2px solid #F56C6C',
    backgroundColor: 'rgba(245, 108, 108, 0.1)',
    borderRadius: '4px',
    cursor: 'pointer',
    zIndex: 10
  };
};

// 处理图片加载
const handleImageLoad = () => {
  loading.value = false;
  error.value = false;
  imageLoaded.value = true;
  nextTick(() => {
    // 图片加载完成后，更新标签位置
    if (props.labels && props.labels.length > 0) {
      currentLabel.value = props.labels[0].label;
      currentConfidence.value = props.labels[0].confidence;
    }
    forceUpdateLabels();
  });
};

// 处理图片加载错误
const handleImageError = () => {
  loading.value = false;
  error.value = true;
  imageLoaded.value = false;
  //ElMessage.error('图片加载失败');
};

// 设置监听器
const setupResizeObserver = () => {
  if (!imageWrapperRef.value) return;

  resizeObserver.value = new ResizeObserver(() => {
    if (imageLoaded.value) {
      // 延迟更新，确保DOM已更新
      nextTick(() => {
        forceUpdateLabels();
      });
    }
  });

  resizeObserver.value.observe(imageWrapperRef.value);
};

// 清理监听器
const cleanupResizeObserver = () => {
  if (resizeObserver.value) {
    resizeObserver.value.disconnect();
    resizeObserver.value = null;
  }
};

// 清理所有状态
const resetState = () => {
  loading.value = false;
  error.value = false;
  imageLoaded.value = false;
  currentLabel.value = '';
  currentConfidence.value = 0;
  showLabels.value = true; // 重置标签显示状态
  labelUpdateKey.value = 0;

  // 清理图片缓存
  if (imageRef.value) {
    imageRef.value.src = '';
  }

  // 清理监听器
  cleanupResizeObserver();
};

// 处理弹窗关闭
const handleClose = () => {
  emit('close');
  // 延迟清理状态，确保动画完成
  setTimeout(() => {
    resetState();
  }, 300);
};

// 格式化文件大小
const formatFileSize = (size: number) => {
  if (size < 1024) {
    return `${size} B`;
  } else if (size < 1024 * 1024) {
    return `${(size / 1024).toFixed(2)} KB`;
  } else {
    return `${(size / (1024 * 1024)).toFixed(2)} MB`;
  }
};

// 获取字典映射的标签名称
const getDictLabelName = (labelValue: string): string => {
  if (!ai_image_tag.value || ai_image_tag.value.length === 0) {
    return labelValue; // 如果字典未加载，返回原始值
  }
  return proxy?.selectDictLabel(ai_image_tag.value, labelValue) || labelValue;
};

// 监听弹窗显示状态
watch(visible, (newVal) => {
  if (newVal) {
    loading.value = true;
    error.value = false;
    imageLoaded.value = false;
    // 设置监听器
    nextTick(() => {
      setupResizeObserver();
    });
  } else {
    // 弹窗关闭时清理状态
    resetState();
  }
});

// 监听图片URL变化
watch(
  () => props.imageUrl,
  (newUrl) => {
    if (newUrl && visible.value) {
      loading.value = true;
      error.value = false;
      imageLoaded.value = false;
    }
  }
);

// 监听窗口大小变化
const handleWindowResize = () => {
  if (imageLoaded.value) {
    forceUpdateLabels();
  }
};

// 组件挂载
onMounted(() => {
  window.addEventListener('resize', handleWindowResize);
});

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener('resize', handleWindowResize);
  resetState();
});
</script>

<style lang="scss" scoped>
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.dialog-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.header-controls {
  display: flex;
  align-items: center;
}

.preview-container {
  position: relative;
  width: 100%;

  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
}

.image-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.preview-image {
  max-width: 100%;
  max-height: 80%;
  width: auto;
  height: auto;
  object-fit: contain;
  border-radius: 4px;
  display: block;
}

.label-box {
  box-sizing: border-box;
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(103, 194, 58, 0.2) !important;
    border-color: #409eff !important;
    transform: scale(1.05);

    .label-tag {
      color: #409eff !important;
      background-color: rgba(64, 158, 255, 0.9) !important;
    }
  }
}

.label-tag {
  position: absolute;
  background-color: rgba(245, 108, 108, 0.9);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  white-space: nowrap;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  transition: all 0.2s ease;
  z-index: 11;

  // 默认显示在框上方
  top: -24px;
  left: 0;

  // 如果标签超出左边界，调整位置
  &::before {
    content: '';
    position: absolute;
    top: 100%;
    left: 8px;
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 4px solid rgba(245, 108, 108, 0.9);
    transition: border-top-color 0.2s ease;
  }
}

// 当靠近顶部时，将标签显示在框底部
.label-box.near-top .label-tag {
  top: 100%;
  left: 0;
  
  &::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 8px;
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-bottom: 4px solid rgba(245, 108, 108, 0.9);
    border-top: none;
  }
}

.label-box:hover .label-tag::before {
  border-top-color: rgba(64, 158, 255, 0.9);
}

.label-box.near-top:hover .label-tag::before {
  border-bottom-color: rgba(64, 158, 255, 0.9);
}

.loading-overlay,
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.9);
  color: #909399;
  font-size: 14px;

  .el-icon {
    font-size: 32px;
    margin-bottom: 12px;
  }
}

.image-info {
  margin-top: 16px;
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 8px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 8px 16px;
}

.info-item {
  display: flex;
  align-items: center;

  .info-label {
    font-weight: 500;
    color: #606266;
    margin-right: 8px;
    min-width: 80px;
  }

  .info-value {
    color: #303133;
    word-break: break-all;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .preview-container {
    height: 50vh;
    min-height: 300px;
  }

  .image-info {
    grid-template-columns: 1fr;
  }

  .control-bar {
    justify-content: center;
  }
}

// 深度样式覆盖
:deep(.el-dialog) {
  margin: 5vh auto !important;
  max-height: 90vh;

  .el-dialog__body {
    padding: 20px;
    max-height: 80vh;
    overflow-y: auto;
  }
}
</style>
