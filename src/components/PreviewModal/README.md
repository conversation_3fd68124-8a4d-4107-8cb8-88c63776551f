# PreviewModal 图片预览弹窗组件

一个基于 Element Plus 的图片预览弹窗组件，支持在图片上绘制识别标签。

## 功能特性

- ✅ 基于 Element Plus Dialog 组件
- ✅ 响应式设计，支持最大宽度 1440px，最小宽度为屏幕的 80%
- ✅ 图片自适应显示，保持原始比例
- ✅ 支持标签绘制，以绿色边框显示在图片上
- ✅ 标签悬停效果，显示置信度
- ✅ 图片信息展示（经纬度、文件信息、高度等）
- ✅ 加载状态和错误处理
- ✅ TypeScript 支持

## 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| modelValue | boolean | false | 控制弹窗显示/隐藏 |
| imageUrl | string | '' | 图片URL地址 |
| labels | LabelData[] | [] | 标签数据数组 |

## 组件事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | (value: boolean) | 弹窗状态变化时触发 |
| close | () | 弹窗关闭时触发 |

## 标签数据格式

```typescript
interface LabelData {
  id: number                    // 标签唯一ID
  label: string                 // 标签名称
  x: number                     // 标签左上角X坐标
  y: number                     // 标签左上角Y坐标
  width: number                 // 标签宽度
  height: number                // 标签高度
  confidence: number            // 置信度 (0-1)
  longitude?: number            // 经度（可选）
  latitude?: number             // 纬度（可选）
  file_size?: number           // 文件大小（可选）
  file_name?: string           // 文件名（可选）
  absolute_altitude?: number   // 绝对高度（可选）
  relative_altitude?: number   // 相对高度（可选）
  gimbal_yaw_degree?: number   // 云台偏航角（可选）
  image_width?: number         // 图片宽度（可选）
  image_height?: number        // 图片高度（可选）
}
```

## 使用示例

### 基础用法

```vue
<template>
  <div>
    <el-button @click="showPreview = true">
      打开预览
    </el-button>
    
    <PreviewModal
      v-model="showPreview"
      :image-url="imageUrl"
      :labels="labels"
      @close="handleClose"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import PreviewModal from '@/components/PreviewModal/index.vue'

const showPreview = ref(false)
const imageUrl = ref('https://example.com/image.jpg')
const labels = ref([
  {
    id: 1,
    label: 'car',
    x: 100,
    y: 200,
    width: 50,
    height: 30,
    confidence: 0.85,
    longitude: 121.4737,
    latitude: 31.2304
  }
])

const handleClose = () => {
  console.log('预览关闭')
}
</script>
```

### 完整示例

参考 `demo.vue` 文件，展示了如何加载模拟数据并使用组件。

## 样式自定义

组件使用了 SCSS 样式，支持以下 CSS 变量自定义：

```scss
:root {
  --preview-modal-bg: #f5f5f5;          // 容器背景色
  --preview-modal-label-color: #67C23A;  // 标签颜色
  --preview-modal-info-bg: #f9f9f9;     // 信息面板背景色
}
```

## 注意事项

1. 确保项目已安装并配置了 Element Plus
2. 标签坐标系统基于原始图片尺寸，组件会自动计算缩放比例
3. 图片加载失败时会显示错误提示
4. 支持跨域图片，但需要服务器支持 CORS


