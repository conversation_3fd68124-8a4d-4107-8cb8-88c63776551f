<template>
  <div class="demo-container">
    <h2>PreviewModal 组件示例</h2>

    <div class="demo-controls">
      <el-button type="primary" @click="openPreview"> 打开预览弹窗 </el-button>

      <el-button type="success" @click="loadMockData"> 加载模拟数据 </el-button>
    </div>

    <!-- 预览弹窗 -->
    <PreviewModal v-model="previewVisible" :image-url="currentImageUrl" :labels="currentLabels" @close="handlePreviewClose" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import PreviewModal from './index.vue';
import mockData from './mockresponse.json';

// 响应式数据
const previewVisible = ref(false);
const currentImageUrl = ref('');
const currentLabels = ref<any[]>([]);

// 打开预览
const openPreview = () => {
  if (!currentImageUrl.value) {
    loadMockData();
  }
  previewVisible.value = true;
};

// 加载模拟数据
const loadMockData = () => {
  if (mockData.data && mockData.data.length > 0) {
    // 使用第一个数据项的图片URL
    currentImageUrl.value = mockData.data[0].image_url;
    // 使用所有的标签数据
    currentLabels.value = mockData.data;
  }
};

// 处理预览关闭
const handlePreviewClose = () => {
  console.log('预览弹窗已关闭');
};

// 组件挂载时自动加载数据
import { onMounted } from 'vue';
onMounted(() => {
  loadMockData();
});
</script>

<style lang="scss" scoped>
.demo-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;

  h2 {
    color: #303133;
    margin-bottom: 20px;
    text-align: center;
  }
}

.demo-controls {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-bottom: 20px;

  .el-button {
    padding: 12px 24px;
  }
}
</style>
