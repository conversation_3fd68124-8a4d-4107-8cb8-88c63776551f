<template>
  <div class="bask-report-preview">
    <!-- 工具栏 -->
    <div class="toolbar" v-if="showToolbar">
      <el-row :gutter="10" justify="space-between">
        <el-col :span="18">
          <el-button-group>
            <el-button 
              type="primary" 
              icon="View" 
              @click="handlePreview"
              :disabled="!isReportConfigValid"
            >
              预览
            </el-button>
            <el-button 
              icon="Printer" 
              @click="handlePrint"
              :disabled="!isReportConfigValid"
            >
              打印
            </el-button>
            <el-button 
              icon="Download" 
              @click="handleExportPdf"
              :disabled="!isReportConfigValid"
            >
              导出PDF
            </el-button>
            <el-button 
              icon="Document" 
              @click="handleExportWord"
              :disabled="!isReportConfigValid"
            >
              导出Word
            </el-button>
            <el-button 
              icon="Grid" 
              @click="handleExportExcel"
              :disabled="!isReportConfigValid"
            >
              导出Excel
            </el-button>
          </el-button-group>
        </el-col>
        <el-col :span="6" class="text-right">
          <el-button 
            circle 
            icon="Refresh" 
            @click="handleRefresh"
            title="刷新"
          />
          <el-button 
            circle 
            icon="FullScreen" 
            @click="handleFullscreen"
            title="全屏"
          />
        </el-col>
      </el-row>
    </div>
    
    <!-- 报表内容区域 -->
    <div 
      class="report-content" 
      :style="{ height: contentHeight }"
      ref="reportContentRef"
    >
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>报表加载中...</span>
      </div>
      
      <!-- BaskReport报表组件 -->
      <BaskReportViewer 
        v-else-if="reportId || reportCode"
        :baseUrl="baseUrl"
        :id="reportId"
        :code="reportCode"
        :tenantId="tenantId"
        :parameters="parameters"
        :width="reportWidth"
        :height="reportHeight"
        :callback="handleReportCallback"
      />
      
      <!-- 空状态 -->
      <div v-else class="empty-container">
        <el-icon><Document /></el-icon>
        <span>请配置报表ID或代码</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Document, Printer, Download, Refresh, FullScreen, Loading } from '@element-plus/icons-vue'
import { BaskReportViewer, BaskReport } from 'baskreport'
import 'baskreport/dist/style.css'

// 定义组件属性
interface Props {
  // 报表服务器地址（可选，默认从环境变量获取）
  baseUrl?: string
  // 报表ID（与code二选一）
  reportId?: string | number
  // 报表代码（与id二选一）
  reportCode?: string
  // 团队ID
  tenantId?: string
  // 报表参数
  parameters?: Record<string, any>
  // 是否显示工具栏
  showToolbar?: boolean
  // 组件高度
  height?: string | number
  // 导出文件名
  fileName?: string
}

const props = withDefaults(defineProps<Props>(), {
  baseUrl: import.meta.env.VITE_APP_REPORT_BASE_URL,
  showToolbar: true,
  height: '600px',
  parameters: () => ({}),
  fileName: '报表文件'
})

// 定义事件
const emit = defineEmits<{
  load: [info: any]
  error: [error: any]
  refresh: []
}>()

// 响应式数据
const loading = ref(false)
const error = ref('')
const reportContentRef = ref(null)
const isFullscreen = ref(false)
const reportInfo = ref(null)

// 工具栏高度常量
const TOOLBAR_HEIGHT = 60

// 计算属性
const reportWidth = computed(() => {
  return typeof props.width === 'number' ? props.width : parseInt(props.width) || undefined
})

const reportHeight = computed(() => {
  const toolbarHeight = props.showToolbar ? TOOLBAR_HEIGHT : 0
  const height = typeof props.height === 'number' ? props.height : parseInt(props.height) || 600
  return height - toolbarHeight
})

const contentHeight = computed(() => {
  const toolbarHeight = props.showToolbar ? `${TOOLBAR_HEIGHT}px` : '0px'
  const height = typeof props.height === 'number' ? `${props.height}px` : props.height
  return `calc(${height} - ${toolbarHeight})`
})

// 检查报表配置是否有效
const isReportConfigValid = computed(() => {
  return props.baseUrl && (props.reportId || props.reportCode)
})

// 构建报表URL
const buildReportUrl = (action: string = 'preview') => {
  if (!props.baseUrl) {
    throw new Error('baseUrl is required')
  }
  
  if (!props.reportId && !props.reportCode) {
    throw new Error('reportId or reportCode is required')
  }
  
  let url = `${props.baseUrl}/report/${action}?`
  
  // 添加报表标识
  if (props.reportId) {
    url += `id=${props.reportId}`
  } else if (props.reportCode) {
    url += `code=${props.reportCode}`
    if (props.tenantId) {
      url += `&tenantId=${props.tenantId}`
    }
  }
  
  // 添加参数
  if (props.parameters && Object.keys(props.parameters).length > 0) {
    const params = new URLSearchParams()
    Object.entries(props.parameters).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        params.append(key, String(value))
      }
    })
    url += `&${params.toString()}`
  }
  
  return url
}

// 参数验证
const validateReportParams = () => {
  if (!props.baseUrl) {
    throw new Error('baseUrl参数是必需的')
  }
  if (!props.reportId && !props.reportCode) {
    throw new Error('报表ID或报表代码必须提供其中之一')
  }
}

// 构建报表配置
const buildReportConfig = (includeFileName = false) => {
  const config: any = {
    baseUrl: props.baseUrl,
    parameters: props.parameters || {}
  }
  
  if (includeFileName) {
    config.name = props.fileName || 'report'
  }
  
  if (props.reportId) {
    config.id = props.reportId
  } else if (props.reportCode) {
    config.code = props.reportCode
    if (props.tenantId) {
      config.tenantId = props.tenantId
    }
  }
  
  return config
}

// 获取BaskReport API
const getBaskReportAPI = () => {
  return new Promise((resolve, reject) => {
    if (!BaskReport) {
      console.error('BaskReport API not available')
      reject(new Error('BaskReport API not available'))
      return
    }
    
    console.log('BaskReport API is available')
    resolve(BaskReport)
  })
}

// 通用的导出处理函数
const handleExportAction = async (action: 'doPrint' | 'doExportPdf' | 'doExportWord' | 'doExportExcel', actionName: string) => {
  try {
    loading.value = true
    validateReportParams()
    
    const baskReportAPI = await getBaskReportAPI()
    const config = buildReportConfig(action !== 'doPrint')
    
    console.log(`${actionName} with config:`, config)
    
    if (!baskReportAPI || typeof baskReportAPI[action] !== 'function') {
      throw new Error(`BaskReport.${action}方法不可用`)
    }
    
    baskReportAPI[action](config)
  } catch (error) {
    console.error(`${actionName} error:`, error)
    ElMessage.error(`${actionName}失败: ${error.message}`)
    emit('error', error)
  } finally {
    loading.value = false
  }
}

// 默认的报表加载完成处理
const defaultHandleReportLoad = (info: any) => {
  console.log('报表加载完成:', info)
  // 可以在这里处理报表加载完成后的默认逻辑
}

// 默认的报表错误处理
const defaultHandleReportError = (error: any) => {
  console.error('报表加载错误:', error)
  ElMessage.error('报表加载失败，请稍后再试。')
}

// 默认的报表刷新处理
const defaultHandleReportRefresh = () => {
  console.log('报表刷新')
  // 可以添加默认的刷新逻辑
}

// 修改现有的handleReportCallback
const handleReportCallback = (info) => {
  reportInfo.value = info
  loading.value = false
  defaultHandleReportLoad(info)
  emit('load', info)
}

// 新增报表错误回调
const handleReportError = (error: any) => {
  defaultHandleReportError(error)
  emit('error', error)
}

// 新增报表刷新回调
const handleReportRefresh = () => {
  defaultHandleReportRefresh()
  emit('refresh')
}

// 预览报表（在新窗口打开）
const handlePreview = async () => {
  try {
    loading.value = true
    const url = buildReportUrl('view')
    
    // 在新窗口打开报表
    window.open(url, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes')
    
    emit('refresh')
  } catch (error) {
    ElMessage.error(`预览失败: ${error.message}`)
    emit('error', error)
  } finally {
    loading.value = false
  }
}

// 打印报表
const handlePrint = () => handleExportAction('doPrint', '打印')

// 导出PDF
const handleExportPdf = () => handleExportAction('doExportPdf', '导出PDF')

// 导出Word
const handleExportWord = () => handleExportAction('doExportWord', '导出Word')

// 导出Excel
const handleExportExcel = () => handleExportAction('doExportExcel', '导出Excel')

// 刷新报表
const handleRefresh = () => {
  // 刷新操作，可以重新加载API或重置状态
  emit('refresh')
}

// 全屏显示
const handleFullscreen = () => {
  if (!reportContentRef.value) return
  
  if (document.fullscreenElement) {
    document.exitFullscreen()
  } else {
    reportContentRef.value.requestFullscreen()
  }
}

// 暴露方法给父组件
defineExpose({
  preview: handlePreview,
  print: handlePrint,
  exportPdf: handleExportPdf,
  exportWord: handleExportWord,
  exportExcel: handleExportExcel,
  refresh: handleRefresh
})


</script>

<style scoped>
.bask-report-preview {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
}

.toolbar {
  padding: 10px 15px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.report-content {
  position: relative;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 20px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: #909399;
  position: absolute;
  top: 0;
  left: 0;
}

.loading-container .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: #909399;
  text-align: center;
  position: absolute;
  top: 0;
  left: 0;
}

.empty-container .el-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #409eff;
}

.empty-container > span {
  font-size: 16px;
  color: #606266;
}

.text-right {
  text-align: right;
}

/* BaskReportViewer 容器样式 */
.report-content > * {
  max-width: 100%;
  width: auto;
}

/* 全屏样式 */
.report-content:fullscreen {
  background: white;
  padding: 0;
  align-items: center;
}

.report-content:fullscreen iframe {
  width: 100vw;
  height: 100vh;
}
</style>