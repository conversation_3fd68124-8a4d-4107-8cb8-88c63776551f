# BaskReportPreview 组件

基于 BaskReport JavaScript API 的通用报表预览组件，提供完整的工具栏功能和报表操作能力。

## 功能特性

- 🎯 **完整工具栏**: 预览、打印、导出(PDF/Word/Excel)、刷新、全屏等功能
- 📊 **内嵌报表**: 使用BaskReportViewer组件直接在页面中显示报表内容
- 🔐 **自动鉴权**: 自动处理BaskReport的认证和权限
- 📱 **响应式设计**: 适配不同屏幕尺寸
- 🎨 **美观界面**: 现代化UI设计，与Element Plus风格一致
- 🪟 **新窗口预览**: 预览功能在新窗口打开报表
- 📥 **JavaScript API**: 使用BaskReport官方API进行打印和导出
- 🔧 **易于集成**: 简单的属性配置，快速集成到现有项目

## 安装使用

### 1. 基本用法

```vue
<template>
  <BaskReportPreview
    :baseUrl="'https://your-report-server.com'"
    :reportCode="'your_report_code'"
    :parameters="{ param1: 'value1', param2: 'value2' }"
    :showToolbar="true"
    :height="'600px'"
    @load="handleLoad"
    @error="handleError"
  />
</template>

<script setup>
import BaskReportPreview from '@/components/BaskReportPreview/index.vue'

const handleLoad = (info) => {
  console.log('报表加载完成:', info)
}

const handleError = (error) => {
  console.error('报表加载失败:', error)
}
</script>
```

### 2. 在弹窗中使用

```vue
<template>
  <el-dialog
    title="报表预览"
    v-model="dialogVisible"
    width="80%"
    append-to-body
  >
    <BaskReportPreview
      v-if="dialogVisible"
      :baseUrl="reportBaseUrl"
      :reportCode="reportCode"
      :parameters="reportParams"
      :height="'700px'"
      :fileName="'自定义文件名'"
    />
  </el-dialog>
</template>
```

## 属性配置

| 属性名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| baseUrl | string | ✅ | - | 报表服务器地址 |
| reportId | string/number | ❌ | - | 报表ID（与reportCode二选一） |
| reportCode | string | ❌ | - | 报表代码（与reportId二选一） |
| tenantId | string | ❌ | - | 团队ID（使用reportCode时建议提供） |
| parameters | object | ❌ | {} | 报表参数 |
| showToolbar | boolean | ❌ | true | 是否显示工具栏 |
| height | string/number | ❌ | '600px' | 组件高度 |
| fileName | string | ❌ | '报表文件' | 导出文件名 |

## 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| load | info: any | 报表加载完成时触发 |
| error | error: any | 报表加载失败时触发 |
| refresh | - | 报表刷新时触发 |

## 方法

通过 `ref` 可以调用组件的方法：

```vue
<template>
  <BaskReportPreview ref="reportRef" ... />
  <el-button @click="exportPdf">导出PDF</el-button>
</template>

<script setup>
import { ref } from 'vue'

const reportRef = ref()

const exportPdf = () => {
  reportRef.value?.exportPdf()
}
</script>
```

可用方法：
- `preview()` - 预览报表
- `print()` - 打印报表
- `exportPdf()` - 导出PDF
- `exportWord()` - 导出Word
- `exportExcel()` - 导出Excel
- `refresh()` - 刷新报表

## 工具栏功能

### 左侧按钮组
- **预览**：在iframe中加载报表内容
- **打印**：调用浏览器打印功能
- **导出PDF**：将报表导出为PDF文件
- **导出Word**：将报表导出为Word文档
- **导出Excel**：将报表导出为Excel文件

### 右侧操作
- **刷新**：重新加载报表内容
- **全屏**：切换全屏显示模式

## 技术实现

- **BaskReportViewer组件**: 使用官方的BaskReportViewer组件直接在页面中显示报表内容
- **BaskReport ES模块**: 使用ES模块导入方式引入BaskReport API，避免动态脚本加载
- **BaskReport JavaScript API**: 使用官方的BaskReport.doXXX方法进行打印和导出操作
- **新窗口预览**: 预览功能使用window.open在新窗口打开报表
- **Vue 3 Composition API**: 使用最新的Vue 3语法，代码更简洁
- **Element Plus**: 基于Element Plus组件库，界面美观统一

### 1. JavaScript API 集成

组件使用 ES 模块方式导入 BaskReport API：
```javascript
// ES 模块导入
import { BaskReportViewer, BaskReport } from 'baskreport'

// 调用API方法
BaskReport.doPrint(config)
BaskReport.doExportPdf(config)
BaskReport.doExportWord(config)
BaskReport.doExportExcel(config)
```

### 2. 鉴权处理

通过 iframe 方式加载报表，自动携带当前页面的认证信息，无需额外处理鉴权问题。

### 3. 参数传递

支持动态参数传递，参数变化时会自动重新加载报表：
```javascript
// URL构建示例
{baseUrl}/report/view?code={reportCode}&param1=value1&param2=value2
```

## 注意事项

1. **浏览器弹窗**: 使用新窗口打开，可能被浏览器拦截，需要用户允许弹窗
2. **认证处理**: 新窗口会自动携带当前会话的认证信息
3. **下载权限**: 导出功能需要浏览器允许自动下载文件
4. **服务器配置**: 确保BaskReport服务器支持直接URL访问
5. **网络要求**: 需要能够访问BaskReport服务器
6. **HTTPS要求**: 在HTTPS环境下使用效果更佳

## 样式定制

组件提供了基础样式，可以通过CSS变量或深度选择器进行定制：

```css
/* 自定义工具栏样式 */
.bask-report-preview .toolbar {
  background-color: #your-color;
}

/* 自定义按钮样式 */
.bask-report-preview .el-button {
  /* 自定义样式 */
}
```

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基础的报表预览和工具栏功能
- 集成 BaskReport JavaScript API
- 提供完整的 TypeScript 类型支持